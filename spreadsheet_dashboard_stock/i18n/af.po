# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* spreadsheet_dashboard_stock
# 
# Translators:
# <PERSON>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-04-14 06:19+0000\n"
"PO-Revision-Date: 2022-09-29 09:37+0000\n"
"Last-Translator: <PERSON>, 2022\n"
"Language-Team: Afrikaans (https://app.transifex.com/odoo/teams/41243/af/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: af\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#, python-format
msgid " days"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#, python-format
msgid "Average Cycle Time"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#, python-format
msgid "Average Delay"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#, python-format
msgid "Avg cycle time"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#, python-format
msgid "Avg delay"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#, python-format
msgid "Current"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#, python-format
msgid "KPI"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#, python-format
msgid "Location"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#, python-format
msgid "Moved Quantity"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#, python-format
msgid "Moves"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#, python-format
msgid "Partner"
msgstr "Vennoot"

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#, python-format
msgid "Period"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#, python-format
msgid "Previous"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#, python-format
msgid "Product"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#, python-format
msgid "Product quantity"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#, python-format
msgid "Quantity"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#, python-format
msgid "Stock Moves Analysis by Destination Address "
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#, python-format
msgid "Stock Moves Analysis by Destination Location"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#, python-format
msgid "Stock Moves Analysis by Product"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#, python-format
msgid "Stock Moves Analysis by Warehouse"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#, python-format
msgid "Top Locations"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#, python-format
msgid "Top Partners"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#, python-format
msgid "Top Products"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#, python-format
msgid "Top Warehouses"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#, python-format
msgid "Warehouse"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#, python-format
msgid "Weekly Stock Moves"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#, python-format
msgid "last period"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#, python-format
msgid "since last period"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#, python-format
msgid "stats - current"
msgstr ""

#. module: spreadsheet_dashboard_stock
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock/data/files/inventory_flow_dashboard.json:0
#, python-format
msgid "stats - previous"
msgstr ""
