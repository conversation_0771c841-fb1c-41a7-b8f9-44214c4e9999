# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* delivery_iot
# 
# Translators:
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-04-14 06:17+0000\n"
"PO-Revision-Date: 2022-09-22 05:46+0000\n"
"Last-Translator: <PERSON><PERSON>, 2023\n"
"Language-Team: Malayalam (https://app.transifex.com/odoo/teams/41243/ml/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ml\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: delivery_iot
#: model:ir.model.fields,help:delivery_iot.field_stock_picking_type__iot_printer_id
msgid "Automatically print the shipping labels using this printer."
msgstr ""

#. module: delivery_iot
#: model:ir.model.fields,help:delivery_iot.field_choose_delivery_package__available_scale_ids
#: model:ir.model.fields,help:delivery_iot.field_stock_picking_type__iot_scale_ids
msgid ""
"Choose the scales you want to use for this operation type. Those scales can "
"be used to weigh the packages created."
msgstr ""

#. module: delivery_iot
#: model:ir.model,name:delivery_iot.model_choose_delivery_package
msgid "Delivery Package Selection Wizard"
msgstr ""

#. module: delivery_iot
#: model:ir.model.fields,field_description:delivery_iot.field_choose_delivery_package__iot_ip
msgid "Domain Address"
msgstr "ഡൊമെയ്ൻ വിലാസം"

#. module: delivery_iot
#: model:ir.model,name:delivery_iot.model_iot_device
msgid "IOT Device"
msgstr "IOT ഉപകരണം"

#. module: delivery_iot
#: model:ir.model.fields,field_description:delivery_iot.field_choose_delivery_package__iot_device_identifier
msgid "Identifier"
msgstr ""

#. module: delivery_iot
#: model_terms:ir.ui.view,arch_db:delivery_iot.view_picking_type_form_inherit
msgid "IoT"
msgstr "ഐഓട്ടി"

#. module: delivery_iot
#: model_terms:ir.ui.view,arch_db:delivery_iot.iot_device_view_form_inherit
msgid "Linked Operation Types"
msgstr ""

#. module: delivery_iot
#: model:ir.model.fields,field_description:delivery_iot.field_choose_delivery_package__manual_measurement
msgid "Manual Measurement"
msgstr ""

#. module: delivery_iot
#: model:ir.model.fields,help:delivery_iot.field_choose_delivery_package__manual_measurement
msgid "Manually read the measurement from the device"
msgstr ""

#. module: delivery_iot
#: model:ir.model.fields,field_description:delivery_iot.field_iot_device__picking_type_ids
msgid "Operation Types"
msgstr "പ്രവർത്തന തരങ്ങൾ"

#. module: delivery_iot
#: model:ir.model,name:delivery_iot.model_stock_picking_type
msgid "Picking Type"
msgstr "പീക്കിങ് ടൈപ്പ് "

#. module: delivery_iot
#. odoo-javascript
#: code:addons/delivery_iot/static/src/field_many2one_iot_scale.xml:0
#, python-format
msgid "Read weight"
msgstr ""

#. module: delivery_iot
#: model:ir.model.fields,field_description:delivery_iot.field_choose_delivery_package__iot_device_id
msgid "Scale"
msgstr "സ്കെയിൽ"

#. module: delivery_iot
#: model:ir.model.fields,field_description:delivery_iot.field_choose_delivery_package__available_scale_ids
#: model:ir.model.fields,field_description:delivery_iot.field_stock_picking_type__iot_scale_ids
msgid "Scales"
msgstr ""

#. module: delivery_iot
#: model:ir.model.fields,field_description:delivery_iot.field_stock_picking_type__iot_printer_id
msgid "Shipping Labels Printer"
msgstr ""

#. module: delivery_iot
#: model:ir.model,name:delivery_iot.model_stock_picking
msgid "Transfer"
msgstr "ട്രാൻസ്ഫെർ"
