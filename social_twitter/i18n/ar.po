# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* social_twitter
# 
# Translators:
# <PERSON>, 2022
# Wil <PERSON>do<PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:25+0000\n"
"PO-Revision-Date: 2022-09-22 05:49+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/models/social_post_template.py:0
#, python-format
msgid "%s / %s characters to fit in a Tweet"
msgstr "%s / %s خانات لتتسع في التغريدة "

#. module: social_twitter
#: model_terms:ir.ui.view,arch_db:social_twitter.twitter_preview
msgid "11m"
msgstr "11m"

#. module: social_twitter
#: model_terms:ir.ui.view,arch_db:social_twitter.twitter_preview
msgid "<b class=\"text-900\">Twitter Account</b>"
msgstr "<b class=\"text-900\">حساب تويتر</b> "

#. module: social_twitter
#: model_terms:ir.ui.view,arch_db:social_twitter.social_stream_post_view_kanban
msgid "<i class=\"fa fa-comments me-1\" title=\"Comments\"/>"
msgstr "<i class=\"fa fa-comments me-1\" title=\"التعليقات \"/>"

#. module: social_twitter
#: model_terms:ir.ui.view,arch_db:social_twitter.social_stream_post_view_kanban
msgid "<i class=\"fa fa-heart me-1\" title=\"Likes\"/>"
msgstr "<i class=\"fa fa-heart me-1\" title=\"الإعجابات \"/>"

#. module: social_twitter
#: model_terms:ir.ui.view,arch_db:social_twitter.social_stream_post_view_kanban
msgid ""
"<i class=\"fa fa-pencil me-1\" title=\"Quote a tweet\"/>\n"
"                                    <span>Quote Tweet</span>"
msgstr ""
"<i class=\"fa fa-pencil me-1\" title=\"Quote a tweet\"/>\n"
"                                    <span>اقتباس التغريدة</span> "

#. module: social_twitter
#: model_terms:ir.ui.view,arch_db:social_twitter.social_stream_post_view_kanban
msgid "<i class=\"fa fa-retweet me-1\" title=\"Retweet a tweet\"/>"
msgstr "<i class=\"fa fa-retweet me-1\" title=\"إعادة تغريد التغريدة \"/>"

#. module: social_twitter
#: model_terms:ir.ui.view,arch_db:social_twitter.social_stream_post_view_kanban
msgid "<span class=\"fst-italic\">Empty tweet</span>"
msgstr "<span class=\"fst-italic\">تغريدة فارغة</span> "

#. module: social_twitter
#: model_terms:ir.ui.view,arch_db:social_twitter.twitter_preview
msgid "@twitteraccount ·"
msgstr "@twitteraccount ·"

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/models/social_stream_post.py:0
#, python-format
msgid "A retweet already exists"
msgstr "لقد قمت بإعادة التغريد بالفعل "

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/controllers/main.py:0
#, python-format
msgid "Authentication failed. Please enter valid credentials."
msgstr "فشلت عملية المصادقة. يرجى إدخال بيانات اعتماد صالحة. "

#. module: social_twitter
#. odoo-javascript
#: code:addons/social_twitter/static/src/xml/social_twitter_templates.xml:0
#: model_terms:ir.ui.view,arch_db:social_twitter.social_stream_post_view_kanban
#, python-format
msgid "Author Image"
msgstr "صورة الكاتب "

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/models/social_stream_post.py:0
#, python-format
msgid ""
"Can not like / unlike the tweet\n"
"%s."
msgstr ""
"لا يمكن الإعجاب / عدم الإعجاب بالتغريدة\n"
"%s. "

#. module: social_twitter
#. odoo-javascript
#: code:addons/social_twitter/static/src/xml/social_twitter_templates.xml:0
#, python-format
msgid "Cancel"
msgstr "إلغاء "

#. module: social_twitter
#: model:ir.model.fields,help:social_twitter.field_res_config_settings__twitter_use_own_account
msgid ""
"Check this if you want to use your personal Twitter Developer Account "
"instead of the provided one."
msgstr ""
"قم بتحديد ذلك إذا كنت ترغب في استخدام حساب مطور Twitter الخاص بك عوضاً عن "
"المقدم إليك. "

#. module: social_twitter
#: model:ir.model,name:social_twitter.model_res_config_settings
msgid "Config Settings"
msgstr "تهيئة الإعدادات "

#. module: social_twitter
#: model_terms:ir.ui.view,arch_db:social_twitter.res_config_settings_view_form
msgid "Consumer Key"
msgstr "مفتاح المستهلك "

#. module: social_twitter
#: model_terms:ir.ui.view,arch_db:social_twitter.res_config_settings_view_form
msgid "Consumer Secret Key"
msgstr "مفتاح سر المستهلك "

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_twitter_account__create_uid
msgid "Created by"
msgstr "أنشئ بواسطة"

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_twitter_account__create_date
msgid "Created on"
msgstr "أنشئ في"

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_twitter_account__description
msgid "Description"
msgstr "الوصف"

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_twitter_account__display_name
msgid "Display Name"
msgstr "اسم العرض "

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_post__has_twitter_accounts
#: model:ir.model.fields,field_description:social_twitter.field_social_post_template__has_twitter_accounts
msgid "Display Twitter Preview"
msgstr "عرض معاينة Twitter "

#. module: social_twitter
#. odoo-javascript
#: code:addons/social_twitter/static/src/js/stream_post_kanban_record.js:0
#, python-format
msgid "Error"
msgstr "خطأ"

#. module: social_twitter
#. odoo-javascript
#: code:addons/social_twitter/static/src/js/stream_post_twitter_quote.js:0
#, python-format
msgid "Error while sending the data to the server."
msgstr "حدث خطأ أثناء إرسال البيانات إلى الخادم. "

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/models/social_stream_post.py:0
#, python-format
msgid ""
"Failed to delete the Tweet\n"
"%s."
msgstr ""
"تعذر حذف التغريدة\n"
"%s. "

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/models/social_stream_post.py:0
#, python-format
msgid "Failed to fetch the conversation id: '%s' using the account %s."
msgstr "تعذر جلب معرف المحادثة: '%s' باستخدام الحساب %s. "

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/models/social_stream_post.py:0
#, python-format
msgid ""
"Failed to fetch the tweets in the same thread: '%s' using the account %s."
msgstr "تعذر جلب التغريدات في نفس المحادثة: '%s' باستخدام الحساب %s. "

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/models/social_stream_post.py:0
#, python-format
msgid "Failed to post comment: %s with the account %s."
msgstr "تعذر نشر التعليق: %s باستخدام الحساب %s. "

#. module: social_twitter
#: model:social.stream.type,name:social_twitter.stream_type_twitter_likes
msgid "Favorites of"
msgstr "مفضلات "

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_twitter_account__id
msgid "ID"
msgstr "المُعرف"

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_twitter_account__image
msgid "Image"
msgstr "صورة"

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_post__is_twitter_post_limit_exceed
#: model:ir.model.fields,field_description:social_twitter.field_social_post_template__is_twitter_post_limit_exceed
msgid "Is Twitter Post Limit Exceed"
msgstr "منشور تويتر يتخطى الحد المسموح به "

#. module: social_twitter
#: model:social.stream.type,name:social_twitter.stream_type_twitter_keyword
msgid "Keyword"
msgstr "كلمة مفتاحية "

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_twitter_account____last_update
msgid "Last Modified on"
msgstr "آخر تعديل في"

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_twitter_account__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_twitter_account__write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: social_twitter
#. odoo-javascript
#: code:addons/social_twitter/static/src/xml/social_twitter_templates.xml:0
#, python-format
msgid "Likes"
msgstr "الإعجابات"

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/models/social_stream.py:0
#, python-format
msgid ""
"Looks like you've made too many requests. Please wait a few minutes before "
"giving it another try."
msgstr ""
"يبدو أنك قمت بإنشاء الكثير من الطلبات. يرجى الانتظار لبضع دقائق قبل المحاولة"
" مجدداً. "

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_media__media_type
msgid "Media Type"
msgstr "نوع الوسائط الاجتماعية "

#. module: social_twitter
#: model:social.stream.type,name:social_twitter.stream_type_twitter_user_mentions
msgid "Mentions"
msgstr "الإشارات "

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_twitter_account__name
msgid "Name"
msgstr "الاسم"

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/models/social_stream_post.py:0
#, python-format
msgid "Oops! Couldn't find this tweet on Twitter.com"
msgstr "عذراً! لم نتمكن من إيجاد هذه التغريدة على Twitter.com "

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/models/social_stream.py:0
#, python-format
msgid "Please select a Twitter account for this stream type."
msgstr "يرجى تحديد حساب Twitter لنوع الوسائط هذا. "

#. module: social_twitter
#. odoo-javascript
#: code:addons/social_twitter/static/src/xml/social_twitter_templates.xml:0
#, python-format
msgid "Post"
msgstr "منشور "

#. module: social_twitter
#: model_terms:ir.ui.view,arch_db:social_twitter.twitter_preview
msgid "Post Image"
msgstr "صورة المنشور "

#. module: social_twitter
#. odoo-javascript
#: code:addons/social_twitter/static/src/js/stream_post_kanban_record.js:0
#, python-format
msgid "Quote a Tweet"
msgstr "اقتباس تغريدة "

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_stream_post__twitter_quoted_tweet_author_link
msgid "Quoted tweet author Link"
msgstr "رابط كاتب التغريدة المقتبسة "

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_stream_post__twitter_quoted_tweet_author_name
msgid "Quoted tweet author Name"
msgstr "اسم كاتب التغريدة المقتبسة "

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_stream_post__twitter_quoted_tweet_message
msgid "Quoted tweet message"
msgstr "رسالة التغريدة المقتبسة "

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_stream_post__twitter_quoted_tweet_profile_image_url
msgid "Quoted tweet profile image URL"
msgstr "رابط URL لصورة الملف التعريفي للتغريدة المقتبسة "

#. module: social_twitter
#: model_terms:ir.ui.view,arch_db:social_twitter.social_stream_post_view_kanban
msgid "RT"
msgstr "إعادة التغريد "

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_stream_post__twitter_retweet_count
msgid "Re-tweets"
msgstr "إعادات التغريد "

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/models/social_stream_post.py:0
#, python-format
msgid "Replies from Tweets older than 7 days must be accessed on Twitter.com"
msgstr "يجب الوصول إلى الردود الأقدم من 7 أيام عن طريق Twitter.com "

#. module: social_twitter
#: model_terms:ir.ui.view,arch_db:social_twitter.social_stream_post_view_kanban
msgid "Retweet"
msgstr "إعادة التغريد "

#. module: social_twitter
#: model_terms:ir.ui.view,arch_db:social_twitter.social_stream_post_view_kanban
msgid "Retweet or Quote"
msgstr "إعادة التغريد أو الاقتباس "

#. module: social_twitter
#. odoo-javascript
#: code:addons/social_twitter/static/src/xml/social_twitter_templates.xml:0
#, python-format
msgid "Retweets"
msgstr "إعادات التغريد "

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_stream__twitter_searched_keyword
msgid "Search Keyword"
msgstr "البحث في الكلمات المفتاحية "

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_stream__twitter_followed_account_search
msgid "Search User"
msgstr "البحث عن مستخدم "

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_twitter_account__twitter_searched_by_id
msgid "Searched by"
msgstr "تم البحث حسب "

#. module: social_twitter
#: model:ir.model,name:social_twitter.model_social_account
msgid "Social Account"
msgstr "الحساب الاجتماعي "

#. module: social_twitter
#: model:ir.model,name:social_twitter.model_social_live_post
msgid "Social Live Post"
msgstr "منشور اجتماعي حي "

#. module: social_twitter
#: model:ir.model,name:social_twitter.model_social_media
msgid "Social Media"
msgstr "مواقع التواصل الاجتماعي"

#. module: social_twitter
#: model:ir.model,name:social_twitter.model_social_post
msgid "Social Post"
msgstr "منشور اجتماعي "

#. module: social_twitter
#: model:ir.model,name:social_twitter.model_social_post_template
msgid "Social Post Template"
msgstr "قالب المنشور الاجتماعي "

#. module: social_twitter
#: model:ir.model,name:social_twitter.model_social_stream
msgid "Social Stream"
msgstr "الوسائط الاجتماعية "

#. module: social_twitter
#: model:ir.model,name:social_twitter.model_social_stream_post
msgid "Social Stream Post"
msgstr "منشور الوسائط الاجتماعية "

#. module: social_twitter
#: model:ir.model,name:social_twitter.model_social_twitter_account
msgid "Social Twitter Account"
msgstr "حساب Twitter الاجتماعي "

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/models/social_stream.py:0
#, python-format
msgid ""
"The keyword you've typed in does not look valid. Please try again with other"
" words."
msgstr ""
"الكلمة المفتاحية التي كتبتها لا تبدو صالحة. يرجى المحاولة مجدداً باستخدام "
"كلمات أخرى. "

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/models/social_media.py:0
#, python-format
msgid ""
"The url that this service requested returned an error. Please contact the "
"author of the app."
msgstr ""
"حدث خطأ ما في رابط url الذي تم طلبه من قِبَل هذه الخدمة. يرجى التواصل مع "
"منشئ هذا التطبيق. "

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/controllers/main.py:0
#: code:addons/social_twitter/controllers/main.py:0
#: code:addons/social_twitter/controllers/main.py:0
#, python-format
msgid "This Tweet has been deleted."
msgstr "لقد تم حذف هذه التغريدة. "

#. module: social_twitter
#: model:social.stream.type,name:social_twitter.stream_type_twitter_follow
msgid "Tweets of"
msgstr "تغريدات "

#. module: social_twitter
#: model:ir.model.fields.selection,name:social_twitter.selection__social_media__media_type__twitter
#: model:social.media,name:social_twitter.social_media_twitter
msgid "Twitter"
msgstr "تويتر"

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_stream_post__twitter_author_id
msgid "Twitter Author ID"
msgstr "معرّف كاتب Twitter "

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_stream_post__twitter_can_retweet
msgid "Twitter Can Retweet"
msgstr "بإمكان تويتر إعادة التغريد "

#. module: social_twitter
#. odoo-javascript
#: code:addons/social_twitter/static/src/js/stream_post_kanban_record.js:0
#: model:ir.model.fields,field_description:social_twitter.field_social_stream_post__twitter_comments_count
#, python-format
msgid "Twitter Comments"
msgstr "تعليقات Twitter "

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_res_config_settings__twitter_consumer_key
msgid "Twitter Consumer Key"
msgstr "مفتاح مستهلك Twitter "

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_res_config_settings__twitter_consumer_secret_key
msgid "Twitter Consumer Secret Key"
msgstr "مفتاح سر مستهلك Twitter "

#. module: social_twitter
#: model_terms:ir.ui.view,arch_db:social_twitter.res_config_settings_view_form
msgid "Twitter Developer Account"
msgstr "حساب مطور تويتر "

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_stream__twitter_followed_account_id
msgid "Twitter Followed Account"
msgstr "حساب Twitter المتبع "

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_twitter_account__twitter_id
msgid "Twitter ID"
msgstr "معرّف Twitter "

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_stream_post__twitter_likes_count
msgid "Twitter Likes"
msgstr "إعجابات Twitter "

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_account__twitter_oauth_token
msgid "Twitter OAuth Token"
msgstr "رمز Twitter OAuth "

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_account__twitter_oauth_token_secret
msgid "Twitter OAuth Token Secret"
msgstr "سر رمز Twitter OAuth "

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_post__twitter_post_limit_message
#: model:ir.model.fields,field_description:social_twitter.field_social_post_template__twitter_post_limit_message
msgid "Twitter Post Limit Message"
msgstr "رسالة الوصول إلى الحد الأقصى للمنشورات على Twitter "

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_post__twitter_preview
#: model:ir.model.fields,field_description:social_twitter.field_social_post_template__twitter_preview
msgid "Twitter Preview"
msgstr "معاينة Twitter "

#. module: social_twitter
#. odoo-javascript
#: code:addons/social_twitter/static/src/xml/social_twitter_templates.xml:0
#, python-format
msgid "Twitter Profile Image"
msgstr "صورة الملف التعريفي لـ Twitter "

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_stream_post__twitter_profile_image_url
msgid "Twitter Profile Image URL"
msgstr "رابط URL لصورة الملف التعريفي لـ Twitter "

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_stream_post__twitter_quoted_tweet_id_str
msgid "Twitter Quoted Tweet ID"
msgstr "معرّف تغريدة تويتر المقتبسة "

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_stream_post__twitter_retweeted_tweet_id_str
msgid "Twitter Retweet ID"
msgstr "معرّف إعادة التغريدفي تويتر "

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_stream_post__twitter_screen_name
msgid "Twitter Screen Name"
msgstr "اسم شاشة Twitter "

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_stream_post__twitter_tweet_id
msgid "Twitter Tweet ID"
msgstr "معرّف تغريدة Twitter "

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_account__twitter_user_id
msgid "Twitter User ID"
msgstr "معرّف مستخدم Twitter "

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_stream_post__twitter_user_likes
msgid "Twitter User Likes"
msgstr "إعجابات مستخدم Twitter "

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/controllers/main.py:0
#, python-format
msgid "Twitter did not provide a valid access token or it may have expired."
msgstr "لم يقدم Twitter رمز وصول صالح أو ربما قد انتهت صلاحيته. "

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/controllers/main.py:0
#, python-format
msgid "Twitter did not provide a valid access token."
msgstr "لم يقدم Twitter رمز وصول صالح. "

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_social_live_post__twitter_tweet_id
msgid "Twitter tweet id"
msgstr "معرّف تغريدة Twitter "

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/controllers/main.py:0
#, python-format
msgid "Unauthorized. Please contact your administrator."
msgstr "غير مصرح به. يرجى التواصل مع مديرك. "

#. module: social_twitter
#: model_terms:ir.ui.view,arch_db:social_twitter.social_stream_post_view_kanban
msgid "Undo Retweet"
msgstr "إلغاء إعادة التغريد "

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/models/social_stream.py:0
#, python-format
msgid "Unknown"
msgstr "غير معروف"

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/models/social_stream_post.py:0
#: code:addons/social_twitter/models/social_stream_post.py:0
#, python-format
msgid "Unknown error"
msgstr "خطأ غير معروف"

#. module: social_twitter
#: model:ir.model.fields,field_description:social_twitter.field_res_config_settings__twitter_use_own_account
msgid "Use your own Twitter Account"
msgstr "استخدم حساب Twitter الخاص بك "

#. module: social_twitter
#: model:ir.model.fields,help:social_twitter.field_social_media__media_type
msgid ""
"Used to make comparisons when we need to restrict some features to a "
"specific media ('facebook', 'twitter', ...)."
msgstr ""
"يستخدم لإجراء المقارنات عند الحاجة إلى تقييد بعض الخصائص لموقع تواصل اجتماعي"
" محدد ('facebook'، 'twitter'، ...). "

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/models/social_account.py:0
#, python-format
msgid ""
"We could not upload your image, it may be corrupted, it may exceed size "
"limit or API may have send improper response (error: %s)."
msgstr ""
"لم نتمكن من رفع صورتك. قد تكون تالفة أو قد تكون قد تجاوزت الحد الأقصى "
"المسموح به للحجم، أو قد تكون الواجهة البرمجية قد أرسلت رداً غير مناسب (خطأ: "
"%s). "

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/models/social_stream_post.py:0
#: code:addons/social_twitter/models/social_stream_post.py:0
#, python-format
msgid "You are not authenticated"
msgstr "أنت لست مصادقاً "

#. module: social_twitter
#. odoo-javascript
#: code:addons/social_twitter/static/src/js/stream_post_comments.js:0
#, python-format
msgid ""
"You can comment only three times a tweet as it may be considered as spamming"
" by Twitter"
msgstr ""
"يمكنك التعليق فقط ثلاث مرات في كل تغريدة لأنه قد يعد ذلك إزعاجاً في تويتر "

#. module: social_twitter
#: model:ir.model.constraint,message:social_twitter.constraint_social_stream_post_tweet_uniq
msgid "You can not store two times the same tweet on the same stream!"
msgstr "لا يمكنك تخزين نفس التغريدة مرتين في نفس التغريدة ونفس الوسط! "

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/models/social_stream.py:0
#, python-format
msgid ""
"You cannot create a Stream from this Twitter account.\n"
"It may be because it's protected. To solve this, please make sure you follow it before trying again."
msgstr ""
"لا يمكنك إنشاء وسط من حساب Twitter هذا. \n"
"قد يكون السبب هو أنه محمي. لحل هذه المشكلة، يرجى التأكد من أنك تتابعه قبل المحاولة مجدداً. "

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/models/social_media.py:0
#, python-format
msgid "You don't have an active subscription. Please buy one here: %s"
msgstr "لا تملك اشتراكاً سارياً. يرجى شراء واحد من هنا: %s "

#. module: social_twitter
#. odoo-python
#: code:addons/social_twitter/models/social_media.py:0
#, python-format
msgid ""
"You need to add the following callback URL to your twitter application "
"settings: %s"
msgstr ""
"تحتاج إلى إضافة رابط الاستدعاء التالي إلى إعدادات تطبيق Twitter الخاصة بك: "
"%s "

#. module: social_twitter
#: model_terms:ir.ui.view,arch_db:social_twitter.social_stream_view_form
msgid "e.g. #odoo"
msgstr "مثال: #odoo "

#. module: social_twitter
#. odoo-javascript
#: code:addons/social_twitter/static/src/js/stream_post_comment.js:0
#, python-format
msgid "tweet"
msgstr "تغريدة "
