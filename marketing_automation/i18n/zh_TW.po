# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* marketing_automation
# 
# Translators:
# <PERSON>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON>, 2024
# <PERSON><PERSON>, 2025
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-06-27 03:40+0000\n"
"PO-Revision-Date: 2022-09-22 05:47+0000\n"
"Last-Translator: <PERSON>, 2025\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__link_tracker_click_count
msgid "# Clicks"
msgstr "點擊次數"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__mailing_filter_count
msgid "# Favorite Filters"
msgstr "最愛篩選器數目"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__mass_mailing_count
msgid "# Mailings"
msgstr "推廣郵件數目"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__total_participant_count
msgid "# of active and completed participants"
msgstr "# 於有效以及已完成的參與者 "

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__running_participant_count
msgid "# of active participants"
msgstr " # 於有效參與者"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__completed_participant_count
msgid "# of completed participants"
msgstr "已完成參與者的 #"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__test_participant_count
msgid "# of test participants"
msgstr "測試參加者數目"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "<i class=\"fa fa-check-circle\"/> Bounced"
msgstr "<i class=\"fa fa-check-circle\"/>退回"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "<i class=\"fa fa-check-circle\"/> Clicked"
msgstr "<i class=\"fa fa-check-circle\"/>點選"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "<i class=\"fa fa-check-circle\"/> Opened"
msgstr "<i class=\"fa fa-check-circle\"/>已開啟"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "<i class=\"fa fa-check-circle\"/> Replied"
msgstr "<i class=\"fa fa-check-circle\"/> 已回覆"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid ""
"<i class=\"fa fa-clock-o pe-1\" role=\"img\" aria-label=\"Select time\" "
"title=\"Select time\"/>"
msgstr "<i class=\"fa fa-clock-o pe-1\" role=\"img\" aria-label=\"選擇時間\" title=\"選擇時間\"/>"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "<i class=\"fa fa-envelope-open-o\"/> Not opened within"
msgstr "<i class=\"fa fa-envelope-open-o\"/> 未在指定時間內打開"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "<i class=\"fa fa-envelope-open-o\"/> Opened after"
msgstr "<i class=\"fa fa-envelope-open-o\"/> 在指定時間後打開"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "<i class=\"fa fa-exclamation-circle\"/> Bounced after"
msgstr "<i class=\"fa fa-exclamation-circle\"/> 此期限後彈回："

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "<i class=\"fa fa-hand-pointer-o\"/> Clicked after"
msgstr "<i class=\"fa fa-hand-pointer-o\"/>在指定時間後點選"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "<i class=\"fa fa-hand-pointer-o\"/> Not clicked within"
msgstr "<i class=\"fa fa-hand-pointer-o\"/> 未在指定時間內點選"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "<i class=\"fa fa-pie-chart\"/> Details"
msgstr "<i class=\"fa fa-pie-chart\"/> 詳情"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "<i class=\"fa fa-plus-circle\"/> Add child activity"
msgstr "<i class=\"fa fa-plus-circle\"/> 添加子活動"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "<i class=\"fa fa-reply\"/> Not replied within"
msgstr "<i class=\"fa fa-reply\"/> 未在指定時間內回覆"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "<i class=\"fa fa-reply\"/> Replied after"
msgstr "<i class=\"fa fa-reply\"/>在指定時間後回覆"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_view_form
msgid "<span class=\"col-2 px-0 pt-1\">after</span>"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "<span class=\"o_form_label\">Record</span>"
msgstr "<span class=\"o_form_label\">記錄</span>"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_kanban
msgid "<span>Completed</span>"
msgstr "<span>已完成</span>"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_kanban
msgid "<span>Running</span>"
msgstr "<span>執行中</span>"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_kanban
msgid "<span>Total</span>"
msgstr "<span>總計</span>"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "<strong>The workflow has been modified!</strong>"
msgstr "<strong>工作流已修改！</strong>"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__ab_testing_mailings_count
msgid "A/B Test Mailings #"
msgstr "A/B 測試郵寄 #"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__ab_testing_completed
msgid "A/B Testing Campaign Finished"
msgstr "A/B 測試活動完結"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_participant__trace_ids
msgid "Actions"
msgstr "動作"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__active
msgid "Active"
msgstr "啟用"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__marketing_activity_ids
#: model_terms:ir.ui.view,arch_db:marketing_automation.mailing_mailing_view_form_marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.mailing_mailing_view_tree_marketing_automation
msgid "Activities"
msgstr "活動"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__parent_id
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_trace__activity_id
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_view_form
msgid "Activity"
msgstr "活動"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__activity_domain
msgid "Activity Filter"
msgstr "活動篩選器"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_view_form
msgid "Activity Name"
msgstr "活動名稱"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__activity_type
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_trace__activity_type
msgid "Activity Type"
msgstr "活動類型"

#. module: marketing_automation
#: model:ir.model.fields,help:marketing_automation.field_marketing_activity__domain
msgid ""
"Activity will only be performed if record satisfies this domain, obtained "
"from the combination of the activity filter and its inherited filter"
msgstr "只限在記錄滿足這個範圍時（該範圍是結合活動篩選器及其繼承篩選器所得），才會執行活動"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Add Another Activity"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Add new activity"
msgstr "加入新活動"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.mailing_mailing_view_form_marketing_activity
msgid "Add to Templates"
msgstr "加入至範本"

#. module: marketing_automation
#: model:ir.model.fields,help:marketing_automation.field_marketing_activity__allowed_parent_ids
msgid "All activities which can be the parent of this one"
msgstr "所有可以成為此活動母項的活動"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__social_post_ids
msgid "All related social media posts"
msgstr "所有相關的社交媒體帖文"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__allowed_parent_ids
msgid "Allowed parents"
msgstr "允許的母項"

#. module: marketing_automation
#: model:ir.model.fields,help:marketing_automation.field_marketing_campaign__is_auto_campaign
msgid "Allows us to filter relevant Campaigns"
msgstr "允許我們過濾相關行銷活動"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/wizard/marketing_campaign_test.py:0
#, python-format
msgid "Another test has been launched for this participant"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__domain
msgid "Applied Filter"
msgstr "已套用的篩選"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_search
msgid "Archived"
msgstr "已封存"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Are you sure you want to proceed ?"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.mailing_mailing_view_form_marketing_activity
msgid "Attach a file"
msgstr "附加一個文件"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__is_auto_campaign
msgid "Automatically Generated Campaign"
msgstr "自動生成的活動"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid ""
"Be aware that participants that had no more activities could be reintroduced"
" into the campaign and new traces could be created for them."
msgstr "請注意，沒有更多活動的參與者可重新進入行銷活動，並且可為這些參與者建立新的痕跡。"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Bounced"
msgstr "被退回"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__bounced_ratio
msgid "Bounced Ratio"
msgstr "被退回的比率"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__campaign_id
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign_test__campaign_id
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_participant__campaign_id
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_search
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_tree
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_search
msgid "Campaign"
msgstr "行銷"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__name
msgid "Campaign Identifier"
msgstr "活動標識"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__title
msgid "Campaign Name"
msgstr "行銷名稱"

#. module: marketing_automation
#: model:ir.actions.act_window,name:marketing_automation.marketing_campaign_action
#: model:ir.ui.menu,name:marketing_automation.marketing_campaign_menu
msgid "Campaigns"
msgstr "行銷"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_test_view_form
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "Cancel"
msgstr "取消"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_view_form
msgid "Cancel after"
msgstr "在此後取消："

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_trace__state__canceled
msgid "Canceled"
msgstr "已取消"

#. module: marketing_automation
#: model:ir.model.fields,help:marketing_automation.field_marketing_activity__validity_duration
msgid ""
"Check this to make sure your actions are not executed after a specific "
"amount of time after the scheduled date. (e.g. : Time-limited offer, "
"Upcoming event, …)"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__child_ids
msgid "Child Activities"
msgstr "兒童活動"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Clicked"
msgstr "點選率"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_trace__links_click_datetime
msgid "Clicked On"
msgstr "點擊開啟於"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_tree
msgid "Clicks"
msgstr "點選"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__color
msgid "Color Index"
msgstr "顏色索引"

#. module: marketing_automation
#: model_terms:ir.actions.act_window,help:marketing_automation.marketing_participants_action_reporting
msgid ""
"Come back later once your campaigns are running to overview your "
"participants."
msgstr "推廣計劃開始運行後，你可返回此處，概覽參加者情況。"

#. module: marketing_automation
#: model:utm.campaign,title:marketing_automation.marketing_campaign_1_utm_campaign
msgid "Commercial prospection"
msgstr "商業尋找新客戶"

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_participant__state__completed
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_search
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_trace_view_search
msgid "Completed"
msgstr "已完成"

#. module: marketing_automation
#: model:ir.ui.menu,name:marketing_automation.marketing_automation_menu_configuration
msgid "Configuration"
msgstr "配置"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_test_view_form
msgid "Continue"
msgstr "繼續"

#. module: marketing_automation
#: model_terms:ir.actions.act_window,help:marketing_automation.marketing_campaign_action
msgid "Create a Marketing Automation Campaign"
msgstr "建立自動化推廣活動"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__create_uid
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__create_uid
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign_test__create_uid
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_participant__create_uid
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_trace__create_uid
msgid "Created by"
msgstr "創立者"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__create_date
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__create_date
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign_test__create_date
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_participant__create_date
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_trace__create_date
msgid "Created on"
msgstr "建立於"

#. module: marketing_automation
#: model:ir.model.fields,help:marketing_automation.field_marketing_campaign__ab_testing_schedule_datetime
msgid ""
"Date that will be used to know when to determine and send the winner mailing"
msgstr "用於了解何時確定和發送優勝郵件的日期"

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__interval_type__days
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__validity_duration_type__days
msgid "Days"
msgstr "天"

#. module: marketing_automation
#: model_terms:ir.actions.act_window,help:marketing_automation.marketing_campaign_action
msgid ""
"Define workflows by chaining up activities and let Odoo handle the rest."
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__interval_type
msgid "Delay Type"
msgstr "延遲類型"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Delete"
msgstr "刪除"

#. module: marketing_automation
#. odoo-javascript
#: code:addons/marketing_automation/static/src/js/marketing_automation_one2many.js:0
#, python-format
msgid ""
"Deleting this activity will delete ALL its children activities. Are you "
"sure?"
msgstr "刪除此活動亦會刪除其所有子活動。確定要繼續？"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_trace__child_ids
msgid "Direct child traces"
msgstr "直系子痕跡"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__display_name
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__display_name
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign_test__display_name
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_participant__display_name
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_trace__display_name
msgid "Display Name"
msgstr "顯示名稱"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_trace__res_id
msgid "Document ID"
msgstr "文件識別碼"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_view_form
msgid "Domain"
msgstr "Domain"

#. module: marketing_automation
#: model:ir.model.fields,help:marketing_automation.field_marketing_activity__activity_domain
msgid "Domain that applies to this activity and its child activities"
msgstr "套用至此活動及其子活動的域"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Don't update"
msgstr "不更新"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Edit"
msgstr "編輯"

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__activity_type__email
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__mass_mailing_id_mailing_type__mail
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "Email"
msgstr "發送電郵"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_activity.py:0
#, python-format
msgid "Email canceled"
msgstr "電子郵件已取消"

#. module: marketing_automation
#: model:ir.model,name:marketing_automation.model_mail_compose_message
msgid "Email composition wizard"
msgstr "電郵撰寫精靈"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_activity.py:0
#, python-format
msgid "Email failed"
msgstr "電郵失敗"

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_trace__state__error
msgid "Error"
msgstr "錯誤"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_trace__state_msg
msgid "Error message"
msgstr "錯誤消息"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_activity.py:0
#, python-format
msgid "Error! You can't create recursive hierarchy of Activity."
msgstr "出錯了！您不能建立活動的遞歸層次結構。"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_activity.py:0
#, python-format
msgid "Exception in mass mailing: %s"
msgstr "信件群發異常：%s"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_activity.py:0
#, python-format
msgid "Exception in server action: %s"
msgstr "伺服器操作異常：%s"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_search
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_trace_view_search
msgid "Exclude Test"
msgstr "排除測試"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Execute activities"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_view_form
msgid "Expiry Duration"
msgstr "到期持續時間"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__mailing_filter_id
msgid "Favorite Filter"
msgstr "最愛篩選器"

#. module: marketing_automation
#: model:ir.ui.menu,name:marketing_automation.mailing_filter_menu_action_marketing_automaion
msgid "Favorite Filters"
msgstr "最愛篩選器"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__mailing_filter_domain
msgid "Favorite filter domain"
msgstr "最愛篩選範圍"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__domain
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Filter"
msgstr "篩選"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Generate participants"
msgstr "生成參加者"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Graph"
msgstr "圖形"

#. module: marketing_automation
#: model_terms:ir.actions.act_window,help:marketing_automation.marketing_trace_action
msgid ""
"Here you will be able to check the results of your mailings from all "
"Marketing Automation Campaigns."
msgstr "此處可查看所有自動化推廣計劃的郵件結果。"

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__interval_type__hours
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__validity_duration_type__hours
msgid "Hours"
msgstr "小時"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__id
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__id
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign_test__id
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_participant__id
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_trace__id
msgid "ID"
msgstr "ID"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__is_mailing_campaign_activated
msgid "Is Mailing Campaign Activated"
msgstr "郵寄活動是否已啟動"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "It will be generated automatically once you save this record."
msgstr "儲存此記錄後將自動生成參與者。"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity____last_update
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign____last_update
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign_test____last_update
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_participant____last_update
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_trace____last_update
msgid "Last Modified on"
msgstr "最後修改於"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__write_uid
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__write_uid
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign_test__write_uid
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_participant__write_uid
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_trace__write_uid
msgid "Last Updated by"
msgstr "最後更新者"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__write_date
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__write_date
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign_test__write_date
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_participant__write_date
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_trace__write_date
msgid "Last Updated on"
msgstr "最後更新於"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__last_sync_date
msgid "Last activities synchronization"
msgstr "上次活動同步"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/wizard/marketing_campaign_test.py:0
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
#, python-format
msgid "Launch a Test"
msgstr "啟動一項測試"

#. module: marketing_automation
#: model:ir.actions.act_window,name:marketing_automation.marketing_campaign_test_action
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_test_view_form
msgid "Launch a test"
msgstr "啟動一項測試"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__crm_lead_count
msgid "Leads/Opportunities count"
msgstr "潛在商機/商機數量"

#. module: marketing_automation
#: model:ir.actions.act_window,name:marketing_automation.link_tracker_action_marketing_campaign
msgid "Link Statistics"
msgstr "連結統計"

#. module: marketing_automation
#: model:ir.ui.menu,name:marketing_automation.link_tracker_menu_reporting_marketing_automation
msgid "Link Tracker"
msgstr "連結追蹤"

#. module: marketing_automation
#: model_terms:ir.actions.act_window,help:marketing_automation.link_tracker_action_marketing_campaign
msgid ""
"Link Trackers are created when mailings with links are sent to track how "
"many clicks they get."
msgstr "連結追蹤工具會在發送附有連結的郵件時建立，以追蹤點擊數量。"

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__trigger_category__email
msgid "Mail"
msgstr "郵件"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.mailing_mailing_view_form_marketing_activity
#: model_terms:ir.ui.view,arch_db:marketing_automation.mailing_mailing_view_form_marketing_automation
msgid "Mail Body"
msgstr "郵件正文"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.mailing_mailing_view_form_marketing_activity
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_view_form
msgid "Mail Template"
msgstr "信件範本"

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__trigger_type__mail_bounce
msgid "Mail: bounced"
msgstr "信件：已退回"

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__trigger_type__mail_click
msgid "Mail: clicked"
msgstr "信件：已點選"

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__trigger_type__mail_not_click
msgid "Mail: not clicked"
msgstr "信件：未點選"

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__trigger_type__mail_not_open
msgid "Mail: not opened"
msgstr "信件：未打開"

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__trigger_type__mail_not_reply
msgid "Mail: not replied"
msgstr "信件：未回覆"

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__trigger_type__mail_open
msgid "Mail: opened"
msgstr "信件：已打開"

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__trigger_type__mail_reply
msgid "Mail: replied"
msgstr "信件：已回覆"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.mailing_mailing_view_form_marketing_activity
msgid "Mailing"
msgstr "郵寄"

#. module: marketing_automation
#: model:ir.model,name:marketing_automation.model_mailing_trace
msgid "Mailing Statistics"
msgstr "簡訊統計"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__mass_mailing_id_mailing_type
msgid "Mailing Type"
msgstr "簡訊類型"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_tree
msgid "Mailings"
msgstr "信件"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Mails sent and not bounced"
msgstr "信件已發送，且未退回"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_trace.py:0
#, python-format
msgid "Manually"
msgstr "手工"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_participant.py:0
#, python-format
msgid "Marked as completed"
msgstr "已標記為已完成"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_mailing_mailing__marketing_activity_ids
msgid "Marketing Activities"
msgstr "推廣活動"

#. module: marketing_automation
#: model:ir.model,name:marketing_automation.model_marketing_activity
#: model:ir.model.fields,field_description:marketing_automation.field_account_invoice_send__marketing_activity_id
#: model:ir.model.fields,field_description:marketing_automation.field_mail_compose_message__marketing_activity_id
msgid "Marketing Activity"
msgstr "推廣活動"

#. module: marketing_automation
#: model:ir.module.category,name:marketing_automation.module_marketing_automation_category
#: model:ir.ui.menu,name:marketing_automation.marketing_automation_menu
msgid "Marketing Automation"
msgstr "自動化推廣"

#. module: marketing_automation
#: model:ir.actions.act_window,name:marketing_automation.mail_mass_mailing_action_marketing_automation
msgid "Marketing Automation Mailings"
msgstr "自動化推廣郵件發送"

#. module: marketing_automation
#: model:ir.actions.server,name:marketing_automation.ir_cron_campaign_execute_activities_ir_actions_server
#: model:ir.cron,cron_name:marketing_automation.ir_cron_campaign_execute_activities
msgid "Marketing Automation: execute activities"
msgstr "自動化推廣：執行活動"

#. module: marketing_automation
#: model:ir.actions.server,name:marketing_automation.ir_cron_campaign_sync_participants_ir_actions_server
#: model:ir.cron,cron_name:marketing_automation.ir_cron_campaign_sync_participants
msgid "Marketing Automation: sync participants"
msgstr "自動化推廣：同步參加者"

#. module: marketing_automation
#: model:ir.model,name:marketing_automation.model_marketing_campaign
msgid "Marketing Campaign"
msgstr "推廣計劃"

#. module: marketing_automation
#: model:ir.model,name:marketing_automation.model_marketing_campaign_test
msgid "Marketing Campaign: Launch a Test"
msgstr "推廣活動：開展測試活動"

#. module: marketing_automation
#: model:ir.model,name:marketing_automation.model_marketing_participant
msgid "Marketing Participant"
msgstr "推廣參加者"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__mass_mailing_id
msgid "Marketing Template"
msgstr "推廣範本"

#. module: marketing_automation
#: model:ir.model,name:marketing_automation.model_marketing_trace
#: model:ir.model.fields,field_description:marketing_automation.field_mailing_trace__marketing_trace_id
msgid "Marketing Trace"
msgstr "推廣追蹤"

#. module: marketing_automation
#: model:ir.model.fields,help:marketing_automation.field_mailing_mailing__use_in_marketing_automation
msgid ""
"Marketing campaigns use mass mailings with some specific behavior; this "
"field is used to indicate its statistics may be suspicious."
msgstr "推廣計劃使用的群發郵件，有某些特別行為。此欄位是指出其統計數據或非完全可信。"

#. module: marketing_automation
#: model:ir.model,name:marketing_automation.model_mailing_mailing
msgid "Mass Mailing"
msgstr "群發信件"

#. module: marketing_automation
#: model:ir.model,name:marketing_automation.model_mailing_trace_report
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_trace_view_form
msgid "Mass Mailing Statistics"
msgstr "群發信件階段"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__mailing_mail_ids
msgid "Mass Mailings"
msgstr "群發信件"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_trace__mailing_trace_ids
msgid "Mass mailing statistics"
msgstr "群發信件階段"

#. module: marketing_automation
#: model:ir.actions.server,name:marketing_automation.ir_action_server_action_message_sales_person
msgid "Message for sales person"
msgstr "銷售人員消息"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__model_id
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__model_id
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign_test__model_id
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_participant__model_id
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_search
msgid "Model"
msgstr "模型"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.ir_model_view_tree_marketing
msgid "Model Description"
msgstr "模型說明"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__model_name
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__model_name
msgid "Model Name"
msgstr "模型名稱"

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__interval_type__months
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__validity_duration_type__months
msgid "Months"
msgstr "月"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__name
msgid "Name"
msgstr "名稱"

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_campaign__state__draft
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_search
msgid "New"
msgstr "新增"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "No activity"
msgstr "沒有活動"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "No activity for this campaign."
msgstr "此推廣計劃未有活動。"

#. module: marketing_automation
#: model_terms:ir.actions.act_window,help:marketing_automation.link_tracker_action_marketing_campaign
#: model_terms:ir.actions.act_window,help:marketing_automation.marketing_participants_action_reporting
#: model_terms:ir.actions.act_window,help:marketing_automation.marketing_trace_action
msgid "No data yet!"
msgstr "暫無資料！"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Not Clicked"
msgstr "未點擊"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Not Opened"
msgstr "未開啟"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Not Replied"
msgstr "未回覆"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "Not bounced yet"
msgstr "尚未彈回"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "Not clicked yet"
msgstr "尚未點選"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "Not opened yet"
msgstr "尚未打開"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "Not replied yet"
msgstr "尚未回覆"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__mailing_mail_count
msgid "Number of Mass Mailing"
msgstr "群發郵件數目"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__click_count
msgid "Number of clicks generated by the campaign"
msgstr "行銷活動產生的點擊次數"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__social_engagement
msgid ""
"Number of interactions (likes, shares, comments ...) with the social posts"
msgstr "社交帖文互動次數（讚好、分享、留言⋯）"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Opened"
msgstr "已開啟"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__opened_ratio
msgid "Opened Ratio"
msgstr "打開比例"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.mailing_mailing_view_form_marketing_activity
msgid "Options"
msgstr "選項"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "Other activity"
msgstr "其他活動"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_trace__parent_id
msgid "Parent"
msgstr "上級"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_trace.py:0
#, python-format
msgid "Parent activity mail bounced"
msgstr "父活動信件已退回"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_trace.py:0
#, python-format
msgid "Parent activity mail clicked"
msgstr "已點選父活動信件"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_trace.py:0
#, python-format
msgid "Parent activity mail opened"
msgstr "已打開父活動信件"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_trace.py:0
#, python-format
msgid "Parent activity mail replied"
msgstr "已回覆父活動信件"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_trace__participant_id
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_tree
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_trace_view_form
msgid "Participant"
msgstr "參與者"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_tree
msgid "Participant Name"
msgstr "參加者姓名"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_graph
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_pivot
msgid "Participant summary"
msgstr "參加者摘要"

#. module: marketing_automation
#: model:ir.actions.act_window,name:marketing_automation.marketing_participant_action_campaign
#: model:ir.actions.act_window,name:marketing_automation.marketing_participant_action_campaign_test
#: model:ir.actions.act_window,name:marketing_automation.marketing_participants_action_mail
#: model:ir.actions.act_window,name:marketing_automation.marketing_participants_action_reporting
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__participant_ids
#: model:ir.ui.menu,name:marketing_automation.marketing_participants_menu
#: model_terms:ir.ui.view,arch_db:marketing_automation.mailing_mailing_view_form_marketing_activity
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_tree
msgid "Participants"
msgstr "訊息參與者"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_activity.py:0
#, python-format
msgid "Participants of %s (%s)"
msgstr "%s（%s）的參加者"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_view_form
msgid "Pick a Server Action"
msgstr "選擇伺服器操作"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_view_form
msgid "Pick a Template"
msgstr "選擇範本"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_test_view_form
msgid "Pick or create a/an"
msgstr "選擇或新增一個"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__processed
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_trace__state__processed
msgid "Processed"
msgstr "已處理"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "REJECTED"
msgstr "已拒絕"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__received_ratio
msgid "Received Ratio"
msgstr "已接收比例"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign_test__resource_ref
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_participant__resource_ref
msgid "Record"
msgstr "記錄"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign_test__res_id
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_participant__res_id
msgid "Record ID"
msgstr "記錄 ID"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_participant.py:0
#, python-format
msgid "Record deleted"
msgstr "記錄已刪除"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign_test__model_name
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_participant__model_name
msgid "Record model"
msgstr "記錄模型"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_activity.py:0
#: code:addons/marketing_automation/models/marketing_activity.py:0
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__rejected
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_trace__state__rejected
#, python-format
msgid "Rejected"
msgstr "已拒絕"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_activity.py:0
#, python-format
msgid "Rejected by activity filter or record deleted / archived"
msgstr "已被活動過濾器拒絕或記錄已刪除/歸檔"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Reload a favorite filter"
msgstr "重新載入最愛篩選器"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.mailing_mailing_view_form_marketing_activity
msgid "Remove from Templates"
msgstr "從範本移除"

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_participant__state__unlinked
msgid "Removed"
msgstr "已刪除"

#. module: marketing_automation
#: model:ir.model.fields,help:marketing_automation.field_marketing_participant__state
msgid "Removed means the related record does not exist anymore."
msgstr "已刪除表示相關記錄不再存在。"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Replied"
msgstr "已回覆"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__replied_ratio
msgid "Replied Ratio"
msgstr "回覆比例"

#. module: marketing_automation
#: model:ir.ui.menu,name:marketing_automation.marketing_automation_reporting_menu
msgid "Reporting"
msgstr "報表"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__require_sync
msgid "Require trace sync"
msgstr "需進行痕跡同步"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "Resource ID"
msgstr "資源ID"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_search
msgid "Resource Name"
msgstr "資源名稱"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__user_id
msgid "Responsible"
msgstr "負責人"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "Run"
msgstr "執行"

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_campaign__state__running
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_participant__state__running
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_search
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_search
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_trace_view_search
msgid "Running"
msgstr "執行中"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "SUCCESS"
msgstr "成功"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_trace__schedule_date
msgid "Schedule Date"
msgstr "預定日期"

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_trace__state__scheduled
msgid "Scheduled"
msgstr "已排程"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_search
msgid "Search Campaign"
msgstr "搜尋行銷活動"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_search
msgid "Search Participant"
msgstr "搜尋參加者"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_trace_view_search
msgid "Search Traces"
msgstr "搜尋追溯"

#. module: marketing_automation
#: model:ir.model.fields,help:marketing_automation.field_marketing_campaign__ab_testing_winner_selection
msgid "Selection to determine the winner mailing that will be sent."
msgstr "確定將會發送的優勝者郵件的選擇。"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__ab_testing_schedule_datetime
msgid "Send Final On"
msgstr "傳送最後版本在"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__interval_number
msgid "Send after"
msgstr "在指定時間後發送"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__interval_standardized
msgid "Send after (in hours)"
msgstr "在指定時間（以小時計）後發送"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Sent"
msgstr "已發送"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__server_action_id
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__activity_type__action
msgid "Server Action"
msgstr "伺服器動作"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__social_posts_count
msgid "Social Media Posts"
msgstr "社交媒體帖文"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid ""
"Some participants are already running on this campaign. Click on 'Update' to"
" apply the modifications you've just made."
msgstr "點選「更新」套用您剛剛完成的修改。"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__source_id
msgid "Source"
msgstr "來源"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_mailing_mailing__use_in_marketing_automation
msgid "Specific mailing used in marketing campaign"
msgstr "推廣計劃使用的特定郵件"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__stage_id
msgid "Stage"
msgstr "階段"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Start"
msgstr "開始"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__state
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_participant__state
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_trace__state
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_search
msgid "State"
msgstr "狀態"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "State: #{record.state.raw_value}"
msgstr "狀態: #{record.state.raw_value}"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__statistics_graph_data
msgid "Statistics Graph Data"
msgstr "統計圖表資料"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_trace__mailing_trace_status
msgid "Status"
msgstr "狀態"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "Stop"
msgstr "停止"

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_campaign__state__stopped
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_search
msgid "Stopped"
msgstr "已停止"

#. module: marketing_automation
#: model:ir.model.fields,help:marketing_automation.field_marketing_trace__links_click_datetime
msgid "Stores last click datetime in case of multi clicks."
msgstr "若有多次點擊，將儲存最後一次點擊的日期時間。"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.mailing_mailing_view_form_marketing_activity
msgid "Subject"
msgstr "主題"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_activity.py:0
#: code:addons/marketing_automation/models/marketing_activity.py:0
#, python-format
msgid "Success"
msgstr "成功"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
#, python-format
msgid ""
"Switching Target Model invalidates the existing activities. Either update "
"your activity actions to match the new Target Model or delete them."
msgstr "切換目標模型會使現有活動變為無效。請更新你的活動動作，以符合新的目標模型，或刪除動作。"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__require_sync
msgid "Sync of participants is required"
msgstr "需要同步參與者"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__tag_ids
msgid "Tags"
msgstr "標籤"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_search
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_tree
msgid "Target"
msgstr "目標"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_tree
msgid "Target Model"
msgstr "目標模型"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Templates"
msgstr "模板"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "Test"
msgstr "測試"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_participant__is_test
msgid "Test Record"
msgstr "測試記錄"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_trace__is_test
msgid "Test Trace"
msgstr "測試追溯"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Tests"
msgstr "測試"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
#, python-format
msgid ""
"The saved filter targets different model and is incompatible with this "
"campaign."
msgstr "已儲存的篩選器用於不同模型，不兼容此活動。"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_activity.py:0
#, python-format
msgid ""
"To use this feature you should be an administrator or belong to the "
"marketing automation group."
msgstr "要使用此功能，必須是管理員或者是行銷自動化組成員"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__ab_testing_total_pc
msgid "Total A/B test percentage"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__total_bounce
msgid "Total Bounce"
msgstr "總退回數"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__total_click
msgid "Total Click"
msgstr "總點選數"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__total_open
msgid "Total Open"
msgstr "總打開數"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__total_reply
msgid "Total Reply"
msgstr "總回覆數"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__total_sent
msgid "Total Sent"
msgstr "總發送數"

#. module: marketing_automation
#: model:ir.actions.act_window,name:marketing_automation.marketing_trace_action
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__trace_ids
#: model:ir.ui.menu,name:marketing_automation.marketing_trace_menu
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_trace_view_tree
msgid "Traces"
msgstr "痕跡"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_view_form
msgid "Trigger"
msgstr "觸發器"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__trigger_category
msgid "Trigger Category"
msgstr "觸發類別"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__trigger_type
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_trace__trigger_type
msgid "Trigger Type"
msgstr "觸發類型"

#. module: marketing_automation
#: model:ir.model,name:marketing_automation.model_utm_campaign
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__utm_campaign_id
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__utm_campaign_id
msgid "UTM Campaign"
msgstr "UTM 行銷活動"

#. module: marketing_automation
#: model:ir.model,name:marketing_automation.model_utm_source
msgid "UTM Source"
msgstr "UTM來源"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Unicity based on"
msgstr "unicity基於"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__unique_field_id
msgid "Unique Field"
msgstr "唯一欄位"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Update"
msgstr "更新"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__use_leads
msgid "Use Leads"
msgstr "使用潛在顧客"

#. module: marketing_automation
#: model:ir.model.fields,help:marketing_automation.field_marketing_campaign__unique_field_id
msgid ""
"Used to avoid duplicates based on model field.\n"
"e.g.\n"
"                For model 'Customers', select email field here if you don't\n"
"                want to process records which have the same email address"
msgstr ""
"用於根據模型欄位，避免出現重複項目。\n"
"例如：\n"
"                對於模型「客戶」，若不想處理電郵地址相同的記錄，\n"
"                請在此處選擇電郵欄位。"

#. module: marketing_automation
#: model:res.groups,name:marketing_automation.group_marketing_automation_user
msgid "User"
msgstr "使用者"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__validity_duration_number
msgid "Valid during"
msgstr "在指定期間內有效"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__validity_duration
msgid "Validity Duration"
msgstr "有效期"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__validity_duration_type
msgid "Validity Duration Type"
msgstr "有效期類型"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
#, python-format
msgid "Warning"
msgstr "警告"

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__interval_type__weeks
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__validity_duration_type__weeks
msgid "Weeks"
msgstr "星期"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__ab_testing_winner_selection
msgid "Winner Selection"
msgstr "選出優勝者"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "Workflow"
msgstr "工作流"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "Workflow Started On"
msgstr "工作流啟動時間"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_activity.py:0
#, python-format
msgid ""
"You are trying to set the activity \"%s\" as \"%s\" while its child \"%s\" has the trigger type \"%s\"\n"
"Please modify one of those activities before saving."
msgstr ""
"你正嘗試將活動「%s」設為 %s，而它的子活動「%s」的觸發器類型為 %s。\n"
"請修改其中一項活動，然後才儲存。"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/utm_campaign.py:0
#, python-format
msgid ""
"You cannot delete these UTM Campaigns as they are linked to the following marketing campaigns in Marketing Automation:\n"
"%(campaign_names)s"
msgstr ""
"您不能刪除這些UTM推廣活動，因為它們連結至自動化推廣中的以下推廣活動:\n"
"%(campaign_names)s"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/utm_source.py:0
#, python-format
msgid ""
"You cannot delete these UTM Sources as they are linked to the following marketing activities in Marketing Automation:\n"
"%(activities_names)s"
msgstr ""
"您不能刪除這些UTM來源，因為它們連結至自動化推廣中的以下推廣活動:\n"
"%(activities_names)s"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
#, python-format
msgid "You must set up at least one activity to start this campaign."
msgstr "您必須設定至少一個活動才能啟動此行銷活動。"

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__trigger_type__activity
msgid "another activity"
msgstr "其他活動"

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__trigger_type__begin
msgid "beginning of workflow"
msgstr "工作流程的開始"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_view_form
msgid "e.g. eCommerce Offers"
msgstr "例如，電子商務提議"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "e.g. eCommerce Offers Plan"
msgstr "例：電子商務優惠計劃"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_test_view_form
msgid "to generate a Test Participant"
msgstr "以生成一名測試參加者"
