# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * web
# 
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON> <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-10-08 06:49+0000\n"
"PO-Revision-Date: 2018-10-08 06:49+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Greek (https://www.transifex.com/odoo/teams/41243/el/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: el\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_model.js:520
#, python-format
msgid " [Me]"
msgstr "[Εγώ]"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search/search_filters.js:231
#, python-format
msgid " and "
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search/search_inputs.js:580
#: code:addons/web/static/src/js/views/search/search_view.js:226
#, python-format
msgid " or "
msgstr "ή"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/field_utils.js:285
#: code:addons/web/static/src/js/views/kanban/kanban_column.js:156
#, python-format
msgid " records"
msgstr "εγγραφές"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:655
#, python-format
msgid "# Code editor"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search/search_filters.js:112
#: code:addons/web/static/src/js/views/search/search_filters.js:354
#, python-format
msgid "%(field)s %(operator)s"
msgstr "%(field)s %(operator)s"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search/search_filters.js:113
#, python-format
msgid "%(field)s %(operator)s \"%(value)s\""
msgstr "%(field)s %(operator)s \"%(value)s\""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/translation.js:114
#, python-format
msgid "%d days ago"
msgstr "%d ημέρες πριν"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/translation.js:112
#, python-format
msgid "%d hours ago"
msgstr "%d ώρες πριν"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/translation.js:110
#, python-format
msgid "%d minutes ago"
msgstr "%d λεπτά πριν"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/translation.js:116
#, python-format
msgid "%d months ago"
msgstr "%d μήνες πριν"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/tools/debug_manager.js:141
#, python-format
msgid "%d requests (%d ms) %d queries (%d ms)"
msgstr "%d αιτήσεις (%d ms) %d ερωτήματα (%d ms)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/translation.js:118
#, python-format
msgid "%d years ago"
msgstr "%d χρόνια πριν"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.webclient_bootstrap
msgid ""
"&lt;!--[if lt IE 10]&gt;\n"
"                        &lt;body class=\"ie9\"&gt;\n"
"                    &lt;![endif]--&gt;"
msgstr ""
"&lt;!--[if lt IE 10]&gt;\n"
"&lt;body class=\"ie9\"&gt;\n"
"&lt;![endif]--&gt;"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/field_utils.js:431
#, python-format
msgid "'%s' is not a correct date"
msgstr "'%s' δεν είναι σωστή ημερομηνία"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/time.js:193
#, python-format
msgid "'%s' is not a correct date, datetime nor time"
msgstr "'%s' δεν είναι έγκυρη ημερομηνία. ημερομηνία ή/και ώρα"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/field_utils.js:478
#, python-format
msgid "'%s' is not a correct datetime"
msgstr "'%s' δεν είναι μια σωστή ημερομηνία/ώρα"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/field_utils.js:512
#, python-format
msgid "'%s' is not a correct float"
msgstr "'%s' δεν είναι σωστός πραγματικός αριθμός"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/field_utils.js:602
#, python-format
msgid "'%s' is not a correct integer"
msgstr "'%s' δεν είναι σωστός ακέραιος αριθμός"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/field_utils.js:548
#, python-format
msgid "'%s' is not a correct monetary field"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/time.js:205
#, python-format
msgid "'%s' is not convertible to date, datetime nor time"
msgstr "'%s' δεν μπορεί να μετατραπεί σε ημερομηνία η ώρα"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/menu.xml:101
#, python-format
msgid "(Enterprise Edition)"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:298
#, python-format
msgid "(change)"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:508
#: code:addons/web/static/src/xml/base.xml:537
#, python-format
msgid "(count)"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:77
#, python-format
msgid "(no string)"
msgstr "(no string)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:10
#, python-format
msgid "(nolabel)"
msgstr "(χωρίς ετικέτα)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/field_utils.js:283
#, python-format
msgid "1 record"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/action_manager.js:577
#, python-format
msgid ""
"A popup window has been blocked. You may need to change your browser "
"settings to allow popup windows for this page."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/action_manager_report.js:66
#, python-format
msgid ""
"A popup window with your report was blocked. You may need to change your "
"browser settings to allow popup windows for this page."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:632
#, python-format
msgid "ALL"
msgstr "ΟΛΑ"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:633
#, python-format
msgid "ANY"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:19
#, python-format
msgid "Access Denied"
msgstr "Μη επιτρεπτή πρόσβαση"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:15
#, python-format
msgid "Access Error"
msgstr "Σφάλμα Πρόσβασης"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1568
#, python-format
msgid "Access to all Enterprise Apps"
msgstr "Πρόσβασης σε όλες τις Enterprise Εφαρμογές"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/sidebar.js:34
#, python-format
msgid "Action"
msgstr "Ενέργεια"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:111
#, python-format
msgid "Action ID:"
msgstr "ID Ενέργειας:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:2044
#, python-format
msgid "Activate"
msgstr "Ενεργοποίηση"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:248
#, python-format
msgid "Activate Assets Debugging"
msgstr "Ενεργοποίηση Εντοπισμού Σφαλμάτων Σταθερών Αρχείων"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:2043
#: code:addons/web/static/src/js/fields/basic_fields.js:2047
#: code:addons/web/static/src/js/fields/basic_fields.js:2051
#, python-format
msgid "Active"
msgstr "Σε Ισχύ"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:802
#: code:addons/web/static/src/xml/base.xml:1411
#: code:addons/web/static/src/xml/kanban.xml:56
#: code:addons/web/static/src/xml/kanban.xml:73
#, python-format
msgid "Add"
msgstr "Προσθήκη"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_renderer.js:82
#, python-format
msgid "Add %s"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1126
#, python-format
msgid "Add Custom Filter"
msgstr "Προσθήκη Προσαρμοσμένου Φίλτρου"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1111
#, python-format
msgid "Add Custom Group"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:49
#, python-format
msgid "Add a Column"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1130
#, python-format
msgid "Add a condition"
msgstr "Προσθέστε μια συνθήκη"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list/list_editable_renderer.js:78
#, python-format
msgid "Add a line"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:616
#, python-format
msgid "Add branch"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:48
#, python-format
msgid "Add column"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:640
#, python-format
msgid "Add filter"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:700
#, python-format
msgid "Add new value"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:615
#, python-format
msgid "Add node"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:701
#, python-format
msgid "Add tag"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:1966
#, python-format
msgid "Add to Favorites"
msgstr "Προσθήκη στα Αγαπημένα"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:375
#, python-format
msgid "Add..."
msgstr "Προσθήκη..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:1574
#, python-format
msgid "Add: "
msgstr "Προσθήκη: "

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1210
#, python-format
msgid "Advanced Search..."
msgstr "Προχωρημένη αναζήτηση..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/dialog.js:315
#, python-format
msgid "Alert"
msgstr "Προσοχή"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:622
#: code:addons/web/static/src/xml/base.xml:627
#, python-format
msgid "All"
msgstr "Όλα"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_model.js:402
#: code:addons/web/static/src/js/views/calendar/calendar_renderer.js:397
#, python-format
msgid "All day"
msgstr "Ολοήμερη"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:494
#, python-format
msgid "All users"
msgstr "Όλοι οι χρήστες"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/menu.xml:134
#: code:addons/web/static/src/xml/menu.xml:137
#: code:addons/web/static/src/xml/menu.xml:143
#: code:addons/web/static/src/xml/menu.xml:146
#: code:addons/web/static/src/xml/menu.xml:152
#: code:addons/web/static/src/xml/menu.xml:155
#: code:addons/web/static/src/xml/menu.xml:161
#: code:addons/web/static/src/xml/menu.xml:164
#: code:addons/web/static/src/xml/menu.xml:170
#: code:addons/web/static/src/xml/menu.xml:173
#: code:addons/web/static/src/xml/menu.xml:179
#: code:addons/web/static/src/xml/menu.xml:182
#: code:addons/web/static/src/xml/menu.xml:188
#: code:addons/web/static/src/xml/menu.xml:191
#: code:addons/web/static/src/xml/menu.xml:197
#: code:addons/web/static/src/xml/menu.xml:200
#, python-format
msgid "Alt"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:149
#, python-format
msgid "An error occurred"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/abstract_web_client.js:203
#, python-format
msgid ""
"An unknown CORS error occured. The error probably originates from a "
"JavaScript file served from a different origin. (Opening your browser "
"console might give you a hint on the error.)"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1573
#, python-format
msgid "And more"
msgstr "και περισσότερα"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:623
#: code:addons/web/static/src/xml/base.xml:628
#, python-format
msgid "Any"
msgstr "Οποιοδήποτε"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1120
#: code:addons/web/static/src/xml/base.xml:1128
#: code:addons/web/static/src/xml/base.xml:1203
#, python-format
msgid "Apply"
msgstr "Εφαρμογή"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:2048
#: code:addons/web/static/src/js/views/list/list_controller.js:154
#, python-format
msgid "Archive"
msgstr "Αρχειοθετήθηκαν"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:27
#, python-format
msgid "Archive All"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:2047
#, python-format
msgid "Archived"
msgstr "Αρχειοθετημένα"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_column.js:372
#, python-format
msgid ""
"Are you sure that you want to archive all the records from this column?"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list/list_controller.js:156
#, python-format
msgid "Are you sure that you want to archive all the selected records?"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_column.js:310
#, python-format
msgid "Are you sure that you want to remove this column ?"
msgstr "Είστε σίγουροι ότι θέλετε να αφαιρέστε αυτή την στήλη;"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search/favorites_menu.js:273
#, python-format
msgid "Are you sure that you want to remove this filter?"
msgstr "Είστε σίγουροι ότι θέλετε να αφαιρέστε αυτό το φίλτρο;"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/basic/basic_controller.js:308
#: code:addons/web/static/src/js/views/calendar/calendar_controller.js:345
#, python-format
msgid "Are you sure you want to delete this record ?"
msgstr "Είστε σίγουροι ότι θέλετε να διαγράψετε αυτήν την εγγραφή;"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1028
#, python-format
msgid "Attach"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:348
#, python-format
msgid "Attachment :"
msgstr "Συνημένο:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1407
#, python-format
msgid "Available fields"
msgstr "Διαθέσιμα πεδία"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1536
#: code:addons/web/static/src/xml/kanban.xml:161
#: code:addons/web/static/src/xml/web_calendar.xml:53
#: code:addons/web/static/src/xml/web_calendar.xml:56
#, python-format
msgid "Avatar"
msgstr "Άβαταρ"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:515
#, python-format
msgid "Bar Chart"
msgstr "Ραβδόγραμμα"

#. module: web
#: model:ir.model,name:web.model_base
msgid "Base"
msgstr "Βάση"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1143
#, python-format
msgid "Based On"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:250
#, python-format
msgid "Become Superuser"
msgstr ""

#. module: web
#: code:addons/web/controllers/main.py:1538
#, python-format
msgid ""
"Binary fields can not be exported to Excel unless their content is "
"base64-encoded. That does not seem to be the case for %s."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:952
#, python-format
msgid "Binary file"
msgstr "Δυαδικό αρχείο"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/colorpicker.xml:35
#, python-format
msgid "Blue"
msgstr "Μπλε"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1572
#, python-format
msgid "Bugfixes guarantee"
msgstr "Εγγύηση διορθώσεις σφαλμάτων"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:74
#, python-format
msgid "Button"
msgstr "Πλήκτρο"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:103
#, python-format
msgid "Button Type:"
msgstr "Τύπος Κουμπιού:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/utils.js:131
#, python-format
msgid "Bytes,Kb,Mb,Gb,Tb,Pb,Eb,Zb,Yb"
msgstr "Bytes,Kb,Mb,Gb,Tb,Pb,Eb,Zb,Yb"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1222
#, python-format
msgid "CLEAR"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_view.js:22
#, python-format
msgid "Calendar"
msgstr "Ημερολόγιο"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_view.js:44
#, python-format
msgid "Calendar view has not defined 'date_start' attribute."
msgstr ""
"Στην προβολή ημερολογίου δεν έχει ορισθεί το χαρακτηριστικό 'date_start'"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/dialog.js:329
#: code:addons/web/static/src/js/core/dialog.js:382
#: code:addons/web/static/src/js/fields/relational_fields.js:65
#: code:addons/web/static/src/js/fields/upgrade_fields.js:75
#: code:addons/web/static/src/js/services/crash_manager.js:200
#: code:addons/web/static/src/js/views/calendar/calendar_quick_create.js:56
#: code:addons/web/static/src/js/views/kanban/kanban_column.js:304
#: code:addons/web/static/src/js/views/view_dialogs.js:397
#: code:addons/web/static/src/xml/base.xml:179
#, python-format
msgid "Cancel"
msgstr "Ακύρωση"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph/graph_renderer.js:106
#, python-format
msgid "Cannot render chart with mode : "
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:414
#, python-format
msgid "Card color: %s"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/controllers/main.py:819
#: code:addons/web/controllers/main.py:821
#: code:addons/web/controllers/main.py:833
#: code:addons/web/static/src/js/widgets/change_password.js:27
#: code:addons/web/static/src/xml/base.xml:178
#, python-format
msgid "Change Password"
msgstr "Αλλαγή Κωδικού Πρόσβασης"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:49
#, python-format
msgid "Change default:"
msgstr "Αλλαγή προεπιλογής:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/colorpicker.js:35
#, python-format
msgid "Choose"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:936
#: code:addons/web/static/src/xml/base.xml:976
#: code:addons/web/static/src/xml/base.xml:1007
#, python-format
msgid "Clear"
msgstr "Καθαρισμός"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:245
#, python-format
msgid "Clear Events"
msgstr "Εκκαθάριση γεγονότων"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:2052
#: code:addons/web/static/src/js/tools/debug_manager.js:490
#: code:addons/web/static/src/js/views/calendar/calendar_controller.js:356
#: code:addons/web/static/src/js/views/view_dialogs.js:123
#: code:addons/web/static/src/js/widgets/data_export.js:212
#: code:addons/web/static/src/js/widgets/domain_selector_dialog.js:24
#: code:addons/web/static/src/xml/base.xml:743
#, python-format
msgid "Close"
msgstr "Κλείσιμο"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:2051
#, python-format
msgid "Closed"
msgstr "Κλειστό"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:54
#, python-format
msgid "Column title"
msgstr "Τίτλος στήλης"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1185
#, python-format
msgid "Compare To"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:468
#, python-format
msgid "Condition:"
msgstr "Κατάσταση:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:173
#, python-format
msgid "Confirm New Password"
msgstr "Επιβεβαίωση νέου Κωδικού Πρόσβασης"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/dialog.js:341
#: code:addons/web/static/src/js/core/dialog.js:391
#, python-format
msgid "Confirmation"
msgstr "Επιβεβαίωση"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/abstract_web_client.js:337
#, python-format
msgid "Connection lost"
msgstr "Χάθηκε η σύνδεση"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/abstract_web_client.js:351
#, python-format
msgid "Connection restored"
msgstr "Η σύνδεση αποκαταστάθηκε"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:37
#: code:addons/web/static/src/xml/base.xml:90
#, python-format
msgid "Context:"
msgstr "Περιεχόμενο:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/menu.xml:137
#: code:addons/web/static/src/xml/menu.xml:146
#: code:addons/web/static/src/xml/menu.xml:155
#: code:addons/web/static/src/xml/menu.xml:164
#: code:addons/web/static/src/xml/menu.xml:173
#: code:addons/web/static/src/xml/menu.xml:182
#: code:addons/web/static/src/xml/menu.xml:191
#: code:addons/web/static/src/xml/menu.xml:200
#, python-format
msgid "Control"
msgstr "Έλεγχος"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:1305
#: code:addons/web/static/src/js/services/crash_manager.js:129
#, python-format
msgid "Copied !"
msgstr "Αντιγράφηκε !"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:956
#: code:addons/web/static/src/xml/base.xml:961
#, python-format
msgid "Copy Text"
msgstr "Αντιγραφή Κειμένου"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:147
#, python-format
msgid "Copy the full error to clipboard"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/ajax.js:372
#, python-format
msgid "Could not connect to the server"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:1515
#, python-format
msgid "Could not display the selected image."
msgstr "Δεν ήταν δυνατή η προβολή της συγκεκριμένης εικόνας."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/utils.js:457
#, python-format
msgid "Could not serialize XML"
msgstr "Δεν ήταν δυνατή η σειριοποίηση XML"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph/graph_view.js:49
#: code:addons/web/static/src/js/views/graph/graph_view.js:52
#: code:addons/web/static/src/js/views/pivot/pivot_view.js:48
#: code:addons/web/static/src/js/views/pivot/pivot_view.js:60
#: code:addons/web/static/src/xml/base.xml:511
#: code:addons/web/static/src/xml/base.xml:540
#, python-format
msgid "Count"
msgstr "Πλήθος"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:44
#: code:addons/web/static/src/js/views/calendar/calendar_controller.js:262
#: code:addons/web/static/src/js/views/calendar/calendar_quick_create.js:45
#: code:addons/web/static/src/js/views/kanban/kanban_controller.js:403
#: code:addons/web/static/src/js/views/view_dialogs.js:403
#: code:addons/web/static/src/xml/base.xml:386
#: code:addons/web/static/src/xml/base.xml:406
#, python-format
msgid "Create"
msgstr "Δημιουργία"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form/form_controller.js:616
#, python-format
msgid "Create "
msgstr "Δημιουργία"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:520
#, python-format
msgid "Create \"<strong>%s</strong>\""
msgstr "Δημιουργία \"<strong>%s</strong>\""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:41
#, python-format
msgid "Create a %s"
msgstr "Δημιουργία ενός %s"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/menu.xml:159
#, python-format
msgid "Create a new record"
msgstr "Δημιουργία Νέας Εγγραφής"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:534
#, python-format
msgid "Create and Edit..."
msgstr "Δημιουργία και Επεξεργασία..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:55
#, python-format
msgid "Create and edit"
msgstr "Δημιουργία και επεξεργασία"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:563
#, python-format
msgid "Create: "
msgstr "Δημιουργία: "

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_controller.js:245
#, python-format
msgid "Create: %s"
msgstr ""

#. module: web
#: model:ir.model.fields,field_description:web.field_report_layout__create_uid
msgid "Created by"
msgstr "Δημιουργήθηκε από"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:353
#, python-format
msgid "Created by :"
msgstr "Δημιουργήθηκε από:"

#. module: web
#: model:ir.model.fields,field_description:web.field_report_layout__create_date
msgid "Created on"
msgstr "Δημιουργήθηκε στις"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:307
#, python-format
msgid "Creation Date:"
msgstr "Ημερομηνία Δημιουργίας:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:303
#, python-format
msgid "Creation User:"
msgstr "Χρήστης δημιουργίας"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:919
#, python-format
msgid "Current state"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search/favorites_menu.js:185
#, python-format
msgid "Custom Filter"
msgstr "Προσαρμοσμένο Φίλτρο"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:29
#: code:addons/web/static/src/xml/base.xml:867
#: code:addons/web/static/src/xml/kanban.xml:88
#, python-format
msgid "Dark blue"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:26
#: code:addons/web/static/src/xml/base.xml:864
#: code:addons/web/static/src/xml/kanban.xml:85
#, python-format
msgid "Dark purple"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid "Database"
msgstr "Βάση Δεδομένων"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/menu.xml:107
#, python-format
msgid "Database expiration:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:558
#: code:addons/web/static/src/xml/web_calendar.xml:80
#, python-format
msgid "Day"
msgstr "Ημέρα"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:2044
#, python-format
msgid "Deactivate"
msgstr "Απενεργοποίηση"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:451
#, python-format
msgid "Default:"
msgstr "Προεπιλογή:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_controller.js:343
#: code:addons/web/static/src/js/views/form/form_controller.js:178
#: code:addons/web/static/src/js/views/list/list_controller.js:168
#: code:addons/web/static/src/xml/base.xml:853
#: code:addons/web/static/src/xml/base.xml:1330
#: code:addons/web/static/src/xml/base.xml:1453
#: code:addons/web/static/src/xml/kanban.xml:24
#, python-format
msgid "Delete"
msgstr "Διαγραφή"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:614
#, python-format
msgid "Delete node"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list/list_editable_renderer.js:557
#, python-format
msgid "Delete row "
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:364
#, python-format
msgid "Delete this attachment"
msgstr "Διαγραφή συνημμένου"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1049
#, python-format
msgid "Delete this file"
msgstr "Διαγραφή αυτού του αρχείου"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/menu.xml:125
#, python-format
msgid "Description"
msgstr "Περιγραφή"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/view_dialogs.js:123
#: code:addons/web/static/src/js/widgets/colorpicker.js:36
#: code:addons/web/static/src/js/widgets/domain_selector_dialog.js:31
#: code:addons/web/static/src/xml/base.xml:393
#: code:addons/web/static/src/xml/base.xml:416
#: code:addons/web/static/src/xml/kanban.xml:75
#: code:addons/web/static/src/xml/report.xml:18
#, python-format
msgid "Discard"
msgstr "Απόρριψη"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/menu.xml:150
#, python-format
msgid "Discard a record modification"
msgstr ""

#. module: web
#: model:ir.model.fields,field_description:web.field_report_layout__display_name
msgid "Display Name"
msgstr "Εμφάνιση Ονόματος"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/data_export.js:362
#, python-format
msgid "Do you really want to delete this export template?"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_renderer.js:148
#, python-format
msgid "Do you really want to delete this filter from favorites ?"
msgstr "Θέλετε πραγματικά να διαγράψετε αυτό το φίλτρο από τα αγαπημένα;"

#. module: web
#: model:ir.model.fields,field_description:web.field_report_layout__view_id
msgid "Document Template"
msgstr "Πρότυπο Εγγράφου"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1546
#, python-format
msgid "Documentation"
msgstr "Τεκμηρίωση"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector_dialog.js:36
#: code:addons/web/static/src/xml/base.xml:601
#, python-format
msgid "Domain"
msgstr "Τομέας"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector.js:536
#, python-format
msgid "Domain error"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:41
#, python-format
msgid "Domain:"
msgstr "Τομέας:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/misc.js:16
#, python-format
msgid "Don't leave yet,<br />it's still loading..."
msgstr "Μην φύγετε ακόμη,<br />φορτώνει..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:546
#, python-format
msgid "Download xls"
msgstr "Λήψη xls"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:587
#: code:addons/web/static/src/xml/base.xml:1556
#, python-format
msgid "Dropdown menu"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form/form_controller.js:184
#, python-format
msgid "Duplicate"
msgstr "Δημιουργία Αντίγραφου"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_controller.js:337
#: code:addons/web/static/src/js/views/calendar/calendar_quick_create.js:50
#: code:addons/web/static/src/xml/base.xml:402
#: code:addons/web/static/src/xml/base.xml:935
#: code:addons/web/static/src/xml/kanban.xml:74
#: code:addons/web/static/src/xml/report.xml:14
#, python-format
msgid "Edit"
msgstr "Επεξεργασία"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:256
#, python-format
msgid "Edit Action"
msgstr "Επεξεργασία ενέργειας"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_column.js:323
#, python-format
msgid "Edit Column"
msgstr "Επεξεργασία Στήλης"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:608
#, python-format
msgid "Edit Domain"
msgstr "Επεξεργασία Τομέα"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:279
#, python-format
msgid "Edit SearchView"
msgstr "Επεξεργασία Προβολής Αναζήτησης"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:23
#, python-format
msgid "Edit Stage"
msgstr "Επεξεργασία Σταδίου"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:276
#, python-format
msgid "Edit View:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/menu.xml:141
#, python-format
msgid "Edit a record"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid "Email"
msgstr "Email"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_standard
msgid "Email:"
msgstr "E-mail:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:132
#: code:addons/web/static/src/js/views/search/favorites_menu.js:87
#: code:addons/web/static/src/js/views/search/favorites_menu.js:94
#, python-format
msgid "Error"
msgstr "Σφάλμα"

#. module: web
#: code:addons/web/controllers/main.py:823
#, python-format
msgid "Error, password not changed !"
msgstr "Σφάλμα, ο κωδικός δεν άλλαξε!"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:62
#, python-format
msgid "Esc to discard"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_model.js:529
#, python-format
msgid "Everybody's calendars"
msgstr "Το ημερολόγιο όλων"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_model.js:529
#, python-format
msgid "Everything"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:60
#, python-format
msgid "Examples"
msgstr "Παραδείγματα"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:545
#, python-format
msgid "Expand all"
msgstr "Ανάπτυξη όλων"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1433
#, python-format
msgid "Expand parents"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list/list_controller.js:149
#, python-format
msgid "Export"
msgstr "Εξαγωγή"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/data_export.js:209
#, python-format
msgid "Export Data"
msgstr "Εξαγωγή δεδομένων"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1400
#, python-format
msgid "Export Format:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/data_export.js:211
#, python-format
msgid "Export To File"
msgstr "Εξαγωγή σε αρχείο"

#. module: web
#. openerp-web
#: code:addons/web/controllers/main.py:1302
#: code:addons/web/controllers/main.py:1305
#: code:addons/web/static/src/js/widgets/data_export.js:482
#, python-format
msgid "External ID"
msgstr "Εξωτερικό Αναγνωριστικό"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:826
#, python-format
msgid "External link"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1220
#, python-format
msgid "FILTER"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/action_manager_act_window.js:540
#, python-format
msgid "Failed to evaluate search criterions"
msgstr "Αποτυχία εκτίμησης των κριτηρίων αναζήτησης"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/field_utils.js:62
#, python-format
msgid "False"
msgstr "Λάθος"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1352
#: code:addons/web/static/src/xml/base.xml:1353
#, python-format
msgid "Favorites"
msgstr "Αγαπημένα"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:17
#, python-format
msgid "Field:"
msgstr "Πεδίο:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/tools/debug_manager.js:527
#: code:addons/web/static/src/xml/base.xml:274
#, python-format
msgid "Fields View Get"
msgstr "Ανάκτηση Πεδίων Προβολής"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1419
#, python-format
msgid "Fields to export"
msgstr "Πεδία για εξαγωγή"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:1416
#, python-format
msgid "File Upload"
msgstr "Μεταφόρτωση αρχείου"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:1396
#, python-format
msgid "File upload"
msgstr "Μεταφόρτωση αρχείου"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search/search_inputs.js:578
#, python-format
msgid "Filter"
msgstr "Φίλτρο"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search/favorites_menu.js:87
#, python-format
msgid "Filter name is required."
msgstr "Απαιτείται όνομα φίλτρου."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search/search_inputs.js:516
#, python-format
msgid "Filter on: %s"
msgstr "Ενεργό φίλτρο: %s"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search/favorites_menu.js:94
#, python-format
msgid "Filter with same name already exists."
msgstr "Υπάρχει ήδη φίλτρο με το όνομα αυτό."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search/filters_menu.js:67
#, python-format
msgid "Filters"
msgstr "Φίλτρα"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:544
#, python-format
msgid "Flip axis"
msgstr "Αναστροφή άξονα"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:21
#, python-format
msgid "Fold"
msgstr "Δίπλωμα"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:756
#, python-format
msgid "Followed by"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/pivot/pivot_controller.js:128
#, python-format
msgid ""
"For Excel compatibility, data cannot be exported if there are more than 256 columns.\n"
"\n"
"Tip: try to flip axis, filter further or reduce the number of measures."
msgstr ""
"Για λόγους συμβατότητας με το Excel, τα δεδομένα δεν μπορούν να εξαχθούν εάν υπάρχουν περισσότερες από 256 στήλες.\n"
"\n"
"Συμβουλή: Δοκιμάστε να αναστρέψετε τους άξονες, να φιλτράρετε περαιτέρω ή να μειώσετε τον αριθμό των μέτρων."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form/form_view.js:18
#, python-format
msgid "Form"
msgstr "Φόρμα"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search/filters_menu.js:179
#, python-format
msgid "From %s To %s"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:30
#: code:addons/web/static/src/xml/base.xml:868
#: code:addons/web/static/src/xml/kanban.xml:89
#, python-format
msgid "Fushia"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1565
#, python-format
msgid "Get this feature and much more with Odoo Enterprise!"
msgstr ""
"Αποκτήστε αυτό το χαρακτηριστικό και πολύ περισσότερα με Odoo Enterprise!"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:18
#, python-format
msgid "Global Business Error"
msgstr "Γενικό Επιχειρησιακό Σφάλμα"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_column_quick_create.js:189
#, python-format
msgid "Got it"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph/graph_view.js:23
#, python-format
msgid "Graph"
msgstr "Γράφημα"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:31
#: code:addons/web/static/src/xml/base.xml:869
#: code:addons/web/static/src/xml/colorpicker.xml:31
#: code:addons/web/static/src/xml/kanban.xml:90
#, python-format
msgid "Green"
msgstr "Πράσινο"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search/groupby_menu.js:88
#: code:addons/web/static/src/js/views/search/search_inputs.js:780
#, python-format
msgid "Group By"
msgstr "Ομαδοποίηση κατά"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search/search_inputs.js:754
#, python-format
msgid "Group by: %s"
msgstr "Ομαδοποίηση κατά: %s"

#. module: web
#: model:ir.model,name:web.model_ir_http
msgid "HTTP Routing"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:877
#, python-format
msgid "Hide in Kanban"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:432
#, python-format
msgid "Hit DOWN to navigate to the list below"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:439
#, python-format
msgid "Hit ENTER to"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:431
#, python-format
msgid "Hit ENTER to CREATE"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:423
#, python-format
msgid "Hit ENTER to SAVE"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:424
#, python-format
msgid "Hit ESCAPE to DISCARD"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/colorpicker.xml:41
#, python-format
msgid "Hue"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/dialog.js:357
#, python-format
msgid "I am sure about this."
msgstr ""

#. module: web
#: model:ir.model.fields,field_description:web.field_report_layout__id
msgid "ID"
msgstr "Κωδικός"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:286
#, python-format
msgid "ID:"
msgstr "Αναγνωριστικό:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:1515
#: code:addons/web/static/src/js/fields/basic_fields.js:1835
#, python-format
msgid "Image"
msgstr "Εικόνα"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:2043
#, python-format
msgid "Inactive"
msgstr "Ανενεργή"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph/graph_renderer.js:258
#: code:addons/web/static/src/js/views/graph/graph_renderer.js:273
#, python-format
msgid "Invalid data"
msgstr "Μη έγκυρα δεδομένα"

#. module: web
#: code:addons/web/controllers/main.py:717
#: code:addons/web/controllers/main.py:731
#, python-format
msgid ""
"Invalid database name. Only alphanumerical characters, underscore, hyphen "
"and dot are allowed."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:606
#, python-format
msgid "Invalid domain"
msgstr "Μη έγκυρος τομέας"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/model_field_selector.js:405
#: code:addons/web/static/src/xml/base.xml:737
#, python-format
msgid "Invalid field chain"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph/graph_renderer.js:105
#, python-format
msgid "Invalid mode for chart"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/tools/debug_manager.js:209
#, python-format
msgid "JS Mobile Tests"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/tools/debug_manager.js:198
#, python-format
msgid "JS Tests"
msgstr "JS Τεστ"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_view.js:17
#, python-format
msgid "Kanban"
msgstr "Kanban"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/user_menu.js:125
#, python-format
msgid "Keyboard Shortcuts"
msgstr ""

#. module: web
#: code:addons/web/controllers/main.py:840
#, python-format
msgid "Languages"
msgstr "Γλώσσες"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search/time_range_menu_options.js:9
#, python-format
msgid "Last 30 Days"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search/time_range_menu_options.js:10
#, python-format
msgid "Last 365 Days"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search/time_range_menu_options.js:8
#, python-format
msgid "Last 7 Days"
msgstr ""

#. module: web
#: model:ir.model.fields,field_description:web.field_report_layout____last_update
msgid "Last Modified on"
msgstr "Τελευταία τροποποίηση στις"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search/time_range_menu_options.js:18
#, python-format
msgid "Last Month"
msgstr "Τελευταίος Μήνας"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search/time_range_menu_options.js:19
#, python-format
msgid "Last Quarter"
msgstr ""

#. module: web
#: model:ir.model.fields,field_description:web.field_report_layout__write_uid
msgid "Last Updated by"
msgstr "Τελευταία Ενημέρωση από"

#. module: web
#: model:ir.model.fields,field_description:web.field_report_layout__write_date
msgid "Last Updated on"
msgstr "Τελευταία Ενημέρωση στις"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search/time_range_menu_options.js:17
#, python-format
msgid "Last Week"
msgstr "Προηγ. Εβδομάδα"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search/time_range_menu_options.js:20
#, python-format
msgid "Last Year"
msgstr "Τελευταίο Έτος"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:315
#, python-format
msgid "Latest Modification Date:"
msgstr "Τελευταία Ημερ. Τροποποίησης:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:311
#, python-format
msgid "Latest Modification by:"
msgstr "Τελευταία τροποποίηση από:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:251
#, python-format
msgid "Leave the Developer Tools"
msgstr "Έξοδος από τα Εργαλεία Προγραμματιστή"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:25
#: code:addons/web/static/src/xml/base.xml:863
#: code:addons/web/static/src/xml/kanban.xml:84
#, python-format
msgid "Light blue"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/colorpicker.xml:49
#, python-format
msgid "Lightness %"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:516
#, python-format
msgid "Line Chart"
msgstr "Γραμμικό γράφημα"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list/list_view.js:21
#, python-format
msgid "List"
msgstr "Λίστα"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:42
#, python-format
msgid "Load more... ("
msgstr "Φόρτωση περισσοτέρων ... ("

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/loading.js:58
#, python-format
msgid "Loading"
msgstr "Γίνεται φόρτωση"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/loading.js:56
#, python-format
msgid "Loading (%d)"
msgstr "Φόρτωση (%d)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1062
#: code:addons/web/static/src/xml/base.xml:1466
#, python-format
msgid "Loading, please wait..."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/misc.js:13
#: code:addons/web/static/src/js/fields/relational_fields.js:144
#: code:addons/web/static/src/js/fields/relational_fields.js:230
#: code:addons/web/static/src/xml/base.xml:1459
#, python-format
msgid "Loading..."
msgstr "Φόρτωση..."

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid "Log in"
msgstr "Σύνδεση"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid "Log in as superuser"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1551
#, python-format
msgid "Log out"
msgstr "Αποσύνδεση"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_background
#: model_terms:ir.ui.view,arch_db:web.external_layout_boxed
#: model_terms:ir.ui.view,arch_db:web.external_layout_clean
#: model_terms:ir.ui.view,arch_db:web.external_layout_standard
#: model_terms:ir.ui.view,arch_db:web.login_layout
msgid "Logo"
msgstr "Λογότυπο"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search/search_inputs.js:461
#, python-format
msgid "M2O search fields do not currently handle multiple default values"
msgstr ""
"Τα πεδία αναζήτησης ΠολλάΠροςΈνα δεν μπορούν να χειριστούν πολλαπλές "
"προεπιλεγμένες τιμές"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/menu.xml:127
#, python-format
msgid "Mac"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_boxed
msgid "Mail:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/tools/debug_manager.js:364
#: code:addons/web/static/src/xml/base.xml:271
#, python-format
msgid "Manage Attachments"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login_layout
msgid "Manage Databases"
msgstr "Διαχείριση Βάσεων Δεδομένων"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/tools/debug_manager.js:300
#: code:addons/web/static/src/xml/base.xml:259
#, python-format
msgid "Manage Filters"
msgstr "Διαχείριση Φίλτρων"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:639
#, python-format
msgid "Match"
msgstr "Αντιστοίχιση "

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:646
#, python-format
msgid "Match records with"
msgstr "Αντιστοίχιση εγγραφών με"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:644
#, python-format
msgid "Match records with the following rule:"
msgstr "Τα αρχεία αντιστοιχίζονται με τον ακόλουθο κανόνα:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/misc.js:19
#, python-format
msgid "Maybe you should consider reloading the application by pressing F5..."
msgstr ""
"Ίσως θα πρέπει να εξετάσετε την επαναφόρτωση της εφαρμογής πατώντας F5 ..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:503
#: code:addons/web/static/src/xml/base.xml:532
#, python-format
msgid "Measures"
msgstr "Μετρήσεις"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:28
#: code:addons/web/static/src/xml/base.xml:866
#: code:addons/web/static/src/xml/kanban.xml:87
#, python-format
msgid "Medium blue"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/tools/debug_manager.js:390
#, python-format
msgid "Metadata (%s)"
msgstr "Μεταδομένα (%s)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:107
#, python-format
msgid "Method:"
msgstr "Μέθοδος:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:16
#, python-format
msgid "Missing Record"
msgstr "Λείπει εγγραφή"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1570
#, python-format
msgid "Mobile support"
msgstr "Υποστήριξη κινητού"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:358
#, python-format
msgid "Modified by :"
msgstr "Τροποποιήθηκε από :"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:45
#: code:addons/web/static/src/xml/base.xml:94
#, python-format
msgid "Modifiers:"
msgstr "Τροποποιητές:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:560
#: code:addons/web/static/src/xml/web_calendar.xml:82
#, python-format
msgid "Month"
msgstr "Μήνας"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form/form_renderer.js:401
#: code:addons/web/static/src/xml/base.xml:903
#, python-format
msgid "More"
msgstr "Περισσότερα"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1415
#, python-format
msgid "Move Down"
msgstr "Μετακίνηση Κάτω"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1414
#, python-format
msgid "Move Up"
msgstr "Μετακίνηση Επάνω"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1550
#, python-format
msgid "My Odoo.com account"
msgstr "Ο Odoo.com λογαριασμός μου"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:634
#, python-format
msgid "NONE"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1470
#, python-format
msgid "Name:"
msgstr "Όνομα:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/basic/basic_model.js:686
#, python-format
msgid "New"
msgstr "Νέα"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:169
#, python-format
msgid "New Password"
msgstr "Νέος Κωδικός Πρόσβασης"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1569
#, python-format
msgid "New design"
msgstr "Νέα σχεδίαση"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1505
#: code:addons/web/static/src/xml/base.xml:1506
#: code:addons/web/static/src/xml/web_calendar.xml:77
#, python-format
msgid "Next"
msgstr "Επόμενο"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search/search_inputs.js:338
#, python-format
msgid "No"
msgstr "Όχι"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:294
#, python-format
msgid "No Update:"
msgstr "Μη Ανανέωση:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/tools/debug_manager.js:359
#, python-format
msgid "No attachment available"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:21
#: code:addons/web/static/src/xml/kanban.xml:80
#, python-format
msgid "No color"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph/graph_renderer.js:111
#: code:addons/web/static/src/js/views/graph/graph_renderer.js:307
#: code:addons/web/static/src/xml/base.xml:575
#, python-format
msgid "No data to display"
msgstr "Δεν υπάρχουν δεδομένα προς εμφάνιση"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/tools/debug_manager.js:374
#, python-format
msgid "No metadata available"
msgstr "Μη διαθέσιμα μεταδομένα"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/field_utils.js:281
#, python-format
msgid "No records"
msgstr "Δεν υπάρχουν αρχεία"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:540
#, python-format
msgid "No results to show..."
msgstr "Δεν υπάρχουν αποτελέσματα για προβολή..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:624
#, python-format
msgid "None"
msgstr "Κανένα"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:923
#, python-format
msgid "Not active state"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:927
#, python-format
msgid "Not active state, click to change it"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:21
#: code:addons/web/static/src/xml/base.xml:86
#, python-format
msgid "Object:"
msgstr "Αντικείμενο:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/dialog.js:59
#: code:addons/web/static/src/xml/menu.xml:100
#, python-format
msgid "Odoo"
msgstr "Odoo"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/apps.js:148
#, python-format
msgid "Odoo Apps will be available soon"
msgstr "Οι Εφαρμογές Odoo θα είναι σύντομα διαθέσιμες"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/abstract_web_client.js:201
#: code:addons/web/static/src/js/chrome/abstract_web_client.js:209
#: code:addons/web/static/src/js/services/crash_manager.js:153
#, python-format
msgid "Odoo Client Error"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/upgrade_fields.js:86
#, python-format
msgid "Odoo Enterprise"
msgstr "Odoo Enterprise"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:115
#, python-format
msgid "Odoo Error"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:212
#, python-format
msgid "Odoo Session Expired"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:105
#: code:addons/web/static/src/js/services/crash_manager.js:194
#, python-format
msgid "Odoo Warning"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:2060
#, python-format
msgid "Off"
msgstr "Κλειστό"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/dialog.js:64
#: code:addons/web/static/src/js/core/dialog.js:304
#: code:addons/web/static/src/js/core/dialog.js:323
#: code:addons/web/static/src/js/core/dialog.js:375
#: code:addons/web/static/src/js/views/kanban/kanban_column.js:299
#: code:addons/web/static/src/xml/base.xml:1443
#, python-format
msgid "Ok"
msgstr "ΟΚ"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:165
#, python-format
msgid "Old Password"
msgstr "Παλιός Κωδικός Πρόσβασης"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:2059
#, python-format
msgid "On"
msgstr "Ενεργό"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:53
#, python-format
msgid "On change:"
msgstr "Σε αλλαγή:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:2283
#, python-format
msgid "Only Integer Value should be valid."
msgstr "Μόνο ακέραιη τιμή θα πρέπει να είναι έγκυρη."

#. module: web
#: code:addons/web/controllers/main.py:502
#, python-format
msgid ""
"Only employee can access this database. Please contact the administrator."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:487
#, python-format
msgid "Only you"
msgstr "Μόνο εσείς"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/colorpicker.xml:55
#, python-format
msgid "Opacity %"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:2052
#, python-format
msgid "Open"
msgstr "Ανοιχτό"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:231
#, python-format
msgid "Open Developer Tools"
msgstr "Ενεργοποίηση των Εργαλείων Προγραμματιστή"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:241
#, python-format
msgid "Open View"
msgstr "Άνοιγμα Προβολής"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/menu.xml:195
#, python-format
msgid "Open the next record"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/menu.xml:186
#, python-format
msgid "Open the previous record"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/menu.xml:177
#, python-format
msgid "Open to kanban view"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/menu.xml:168
#, python-format
msgid "Open to list view"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:639
#: code:addons/web/static/src/js/views/calendar/calendar_controller.js:323
#: code:addons/web/static/src/js/views/form/form_controller.js:616
#: code:addons/web/static/src/js/views/form/form_controller.js:638
#, python-format
msgid "Open: "
msgstr "Άνοιγμα: "

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:23
#: code:addons/web/static/src/xml/base.xml:861
#: code:addons/web/static/src/xml/kanban.xml:82
#, python-format
msgid "Orange"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_background
msgid ""
"Page:\n"
"                    <span class=\"page\"/>\n"
"                    of\n"
"                    <span class=\"topage\"/>"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_boxed
#: model_terms:ir.ui.view,arch_db:web.external_layout_standard
msgid "Page: <span class=\"page\"/> / <span class=\"topage\"/>"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid "Password"
msgstr "Κωδικός Πρόσβασης"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_standard
msgid "Phone:"
msgstr "Τηλέφωνο"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/colorpicker.js:33
#, python-format
msgid "Pick a color"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:517
#, python-format
msgid "Pie Chart"
msgstr "Κυκλικό Διάγραμμα"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph/graph_renderer.js:274
#, python-format
msgid ""
"Pie chart cannot display all zero numbers.. Try to change your domain to "
"display positive results"
msgstr ""
"Το γράφημα πίτας δεν μπορεί να εμφανιστεί όταν όλες οι τιμές είναι μηδέν. "
"Προσπαθήστε να αλλάξετε τον τομέα έτσι ώστε να εμφανίζει μόνο θετικά "
"αποτελέσματα"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph/graph_renderer.js:259
#, python-format
msgid ""
"Pie chart cannot mix positive and negative numbers. Try to change your "
"domain to only display positive results"
msgstr ""
"Το γράφημα πίτας δεν μπορεί να περιέχει και αρνητικές και θετικές τιμές. "
"Προσπαθήστε να αλλάξετε τον τομέα έτσι ώστε να εμφανίζει μόνο θετικά "
"αποτελέσματα"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/pivot/pivot_view.js:23
#, python-format
msgid "Pivot"
msgstr "Συγκεντρωτικό"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list/list_renderer.js:352
#, python-format
msgid "Please click on the \"save\" button first."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/data_export.js:177
#, python-format
msgid "Please enter save field list name"
msgstr "Παρακαλούμε εισάγετε τον όνομα της λίστας των πεδίων για αποθήκευση"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/data_export.js:478
#, python-format
msgid "Please select fields to export..."
msgstr "Παρακαλώ επιλέξτε πεδία για εξαγωγή..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/data_export.js:464
#, python-format
msgid "Please select fields to save export list..."
msgstr "Παρακαλώ επιλέξτε πεδία για να αποθηκεύσετε τη λίστα εξαγωγής..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1525
#, python-format
msgid "Please update translations of :"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:150
#, python-format
msgid ""
"Please use the copy button to report the error to your support service."
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login_layout
msgid "Powered by <span>Odoo</span>"
msgstr "Δημιουργήθηκε με <span>Odoo</span>"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1549
#, python-format
msgid "Preferences"
msgstr "Προτιμήσεις"

#. module: web
#: model:ir.actions.report,name:web.action_report_externalpreview
msgid "Preview External Report"
msgstr ""

#. module: web
#: model:ir.actions.report,name:web.action_report_internalpreview
msgid "Preview Internal Report"
msgstr ""

#. module: web
#: model:ir.model.fields,field_description:web.field_report_layout__image
msgid "Preview image src"
msgstr ""

#. module: web
#: model:ir.model.fields,field_description:web.field_report_layout__pdf
msgid "Preview pdf src"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:741
#: code:addons/web/static/src/xml/web_calendar.xml:75
#, python-format
msgid "Previous"
msgstr "Προηγούμενο"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search/time_range_menu_options.js:24
#, python-format
msgid "Previous Period"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search/time_range_menu_options.js:25
#, python-format
msgid "Previous Year"
msgstr "Προηγούμενο Έτος"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/sidebar.js:33
#: code:addons/web/static/src/xml/report.xml:11
#, python-format
msgid "Print"
msgstr "Εκτύπωση"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:32
#: code:addons/web/static/src/xml/base.xml:870
#: code:addons/web/static/src/xml/kanban.xml:91
#, python-format
msgid "Purple"
msgstr "Μωβ"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:561
#, python-format
msgid "Quarter"
msgstr "Τρίμηνο"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:32
#, python-format
msgid "Quick add"
msgstr ""

#. module: web
#: model:ir.model,name:web.model_ir_qweb_field_image
msgid "Qweb Field Image"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1160
#, python-format
msgid "Range"
msgstr "Εύρος"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:22
#: code:addons/web/static/src/xml/base.xml:860
#: code:addons/web/static/src/xml/colorpicker.xml:27
#: code:addons/web/static/src/xml/kanban.xml:81
#, python-format
msgid "Red"
msgstr "Κόκκινο"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:249
#, python-format
msgid "Regenerate Assets Bundles"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/model_field_selector.js:467
#, python-format
msgid "Relation not allowed"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:771
#, python-format
msgid "Relation to follow"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:57
#, python-format
msgid "Relation:"
msgstr "Σχέση:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/view_dialogs.js:157
#: code:addons/web/static/src/xml/base.xml:1238
#: code:addons/web/static/src/xml/base.xml:1412
#, python-format
msgid "Remove"
msgstr "Αφαίρεση"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1413
#, python-format
msgid "Remove All"
msgstr "Αφαίρεση όλων"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:1966
#, python-format
msgid "Remove from Favorites"
msgstr "Κατάργηση από τα Αγαπημένα"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:696
#, python-format
msgid "Remove tag"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/web_calendar.xml:65
#, python-format
msgid "Remove this favorite from the list"
msgstr "Απομάκρυνση του αγαπημένου από τη λίστα"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/action_manager_report.js:112
#, python-format
msgid "Report"
msgstr "Αναφορά"

#. module: web
#: model:ir.model,name:web.model_report_layout
msgid "Report Layout"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:223
#, python-format
msgid "Request timeout"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:2048
#, python-format
msgid "Restore"
msgstr "Επαναφορά"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:240
#, python-format
msgid "Run Click Everywhere Test"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:239
#, python-format
msgid "Run JS Mobile Tests"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:238
#, python-format
msgid "Run JS Tests"
msgstr "Εκτέλεση JS Tests"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1226
#, python-format
msgid "SEE RESULT"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:27
#: code:addons/web/static/src/xml/base.xml:865
#: code:addons/web/static/src/xml/kanban.xml:86
#, python-format
msgid "Salmon pink"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/colorpicker.xml:45
#, python-format
msgid "Saturation %"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/view_dialogs.js:137
#: code:addons/web/static/src/js/widgets/domain_selector_dialog.js:28
#: code:addons/web/static/src/xml/base.xml:390
#: code:addons/web/static/src/xml/base.xml:412
#: code:addons/web/static/src/xml/base.xml:1375
#: code:addons/web/static/src/xml/report.xml:17
#, python-format
msgid "Save"
msgstr "Αποθήκευση"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/view_dialogs.js:137
#, python-format
msgid "Save & Close"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/view_dialogs.js:146
#, python-format
msgid "Save & New"
msgstr "Αποθήκευση  & Δημιουργία Νέου"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:1577
#, python-format
msgid "Save As..."
msgstr "Αποθήκευση ως…"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/menu.xml:132
#, python-format
msgid "Save a record"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1443
#, python-format
msgid "Save as:"
msgstr "Αποθήκευση ως:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1358
#, python-format
msgid "Save current search"
msgstr "Αποθήκευση τρέχουσας αναζήτησης"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/tools/debug_manager.js:491
#, python-format
msgid "Save default"
msgstr "Αποθήκευση προεπιλογής"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1419
#, python-format
msgid "Save fields list"
msgstr "Αποθήκευση λίστας πεδίων"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1446
#, python-format
msgid "Saved exports:"
msgstr "Αποθήκευση εξαγωγών:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1592
#, python-format
msgid "Search"
msgstr "Αναζήτηση"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search/search_inputs.js:366
#, python-format
msgid "Search %(field)s at: %(value)s"
msgstr "Αναζήτηση %(field)s στο: %(value)s"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search/search_inputs.js:199
#: code:addons/web/static/src/js/views/search/search_inputs.js:218
#: code:addons/web/static/src/js/views/search/search_inputs.js:407
#, python-format
msgid "Search %(field)s for: %(value)s"
msgstr "Αναζήτηση σε %(field)s για: %(value)s"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:496
#, python-format
msgid "Search More..."
msgstr "Αναζήτηση Περισσότερων..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1231
#, python-format
msgid "Search..."
msgstr "Αναζήτηση..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:563
#, python-format
msgid "Search: "
msgstr "Αναζήτηση: "

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:154
#, python-format
msgid "See details"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/view_dialogs.js:410
#: code:addons/web/static/src/xml/base.xml:974
#: code:addons/web/static/src/xml/base.xml:975
#: code:addons/web/static/src/xml/base.xml:1005
#: code:addons/web/static/src/xml/base.xml:1006
#, python-format
msgid "Select"
msgstr "Επιλογή"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid ""
"Select <i class=\"fa fa-database\" role=\"img\" aria-label=\"Database\" "
"title=\"Database\"/>"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:610
#, python-format
msgid "Select a model to add a filter."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/tools/debug_manager.js:169
#, python-format
msgid "Select a view"
msgstr "Επιλογή προβολής"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:2746
#, python-format
msgid "Selected records"
msgstr "Επιλεγμένες εγγραφές"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:61
#, python-format
msgid "Selection:"
msgstr "Επιλογή:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/tools/debug_manager.js:488
#, python-format
msgid "Set Default"
msgstr "Ορισμός ως προεπιλογή"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:268
#, python-format
msgid "Set Defaults"
msgstr "Ορισμός προεπιλογών"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:19
#, python-format
msgid "Settings"
msgstr "Ρυθμίσεις"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1371
#, python-format
msgid "Share with all users"
msgstr "Κοινό με όλους τους χρήστες"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/menu.xml:114
#, python-format
msgid "Shortcuts"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/apps.js:148
#, python-format
msgid "Showing locally available modules"
msgstr "Προβολή τοπικά διαθέσιμων εφαρμογών"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:33
#, python-format
msgid "Size:"
msgstr "Μέγεθος:"

#. module: web
#: code:addons/web/controllers/main.py:1169
#, python-format
msgid "Something horrible happened"
msgstr "Κάτι τρομερό συνέβη"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:98
#, python-format
msgid "Special:"
msgstr "Ειδικά:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/misc.js:14
#, python-format
msgid "Still loading..."
msgstr "Ακόμα φορτώνει..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/misc.js:15
#, python-format
msgid "Still loading...<br />Please be patient."
msgstr "Ακόμη Φορτώνει...<br />Παρακαλώ Περιμένετε"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/web_calendar.xml:96
#, python-format
msgid "Summary:"
msgstr "Σύνοψη:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1547
#, python-format
msgid "Support"
msgstr "Υποστήριξη"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:2061
#, python-format
msgid "Switch Off"
msgstr "Κλειστό"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:2062
#, python-format
msgid "Switch On"
msgstr "Ανοιχτό"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector.js:583
#, python-format
msgid "Syntax error"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/misc.js:18
#, python-format
msgid "Take a minute to get a coffee,<br />because it's loading..."
msgstr "Κάνε ένα λεπτό διάλειμα,<br />γιατί ακόμη φορτώνει..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/switch_company_menu.js:44
#, python-format
msgid "Tap on the list to change company"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:260
#, python-format
msgid "Technical Translation"
msgstr "Τεχνική Μετάφραση"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_boxed
#: model_terms:ir.ui.view,arch_db:web.external_layout_clean
msgid "Tel:"
msgstr "Τηλ:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector.js:537
#: code:addons/web/static/src/js/widgets/domain_selector.js:583
#, python-format
msgid "The domain you entered is not properly formed"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/model_field_selector.js:406
#, python-format
msgid ""
"The field chain is not valid. Did you maybe use a non-existing field name or"
" followed a non-relational field?"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:1577
#, python-format
msgid "The field is empty, there's nothing to save !"
msgstr "Το πεδίο είναι άδειο, δεν υπάρχει τίποτα για αποθήκευση!"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/basic/basic_controller.js:398
#: code:addons/web/static/src/js/widgets/attach_document.js:65
#, python-format
msgid "The following fields are invalid:"
msgstr "Τα επόμενα πεδία είναι μη έγκυρα"

#. module: web
#: code:addons/web/controllers/main.py:821
#, python-format
msgid "The new password and its confirmation must be identical."
msgstr "Ο νέος κωδικός και η επιβεβαίωση του πρέπει να είναι ακριβώς ίδιοι."

#. module: web
#: code:addons/web/controllers/main.py:832
#, python-format
msgid ""
"The old password you provided is incorrect, your password was not changed."
msgstr "Ο παλιός κωδικός που δώσατε είναι λάθος, ο κωδικός σας δεν άλλαξε"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:224
#, python-format
msgid ""
"The operation was interrupted. This usually means that the current operation"
" is taking too much time."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/basic/basic_controller.js:75
#, python-format
msgid ""
"The record has been modified, your changes will be discarded. Do you want to"
" proceed?"
msgstr ""
"Η εγγραφή έχει τροποποιηθεί, οι αλλαγές σας θα απορριφθούν. Θέλετε να "
"συνεχίσετε?"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:1395
#, python-format
msgid "The selected file exceed the maximum file size of %s."
msgstr "Το συγκεκριμένο αρχείο ξεπερνάει το μέγιστο μέγεθως των %s."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:1643
#, python-format
msgid ""
"The type of the field '%s' must be a many2many field with a relation to "
"'ir.attachment' model."
msgstr ""
"Ο τύπος του πεδίου '%s' πρέπει να έχει μία σχέση ΠολλάΠροςΠολλά με το "
"μοντέλο 'ir.attachment'."

#. module: web
#: code:addons/web/controllers/main.py:1513
#, python-format
msgid ""
"There are too many rows (%s rows, limit: 65535) to export as Excel 97-2003 "
"(.xls) format. Consider splitting the export."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:1416
#, python-format
msgid "There was a problem while uploading your file"
msgstr "Υπήρξε κάποιο πρόβλημα κατά το ανέβασμα του αρχείου σας"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search/time_range_menu_options.js:13
#, python-format
msgid "This Month"
msgstr "Αυτόν τον μήνα"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search/time_range_menu_options.js:14
#, python-format
msgid "This Quarter"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search/time_range_menu_options.js:12
#, python-format
msgid "This Week"
msgstr "Της Εβδομάδας"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search/time_range_menu_options.js:15
#, python-format
msgid "This Year"
msgstr "Αυτό το Έτος"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/date_picker.js:181
#, python-format
msgid "This date is on the future. Make sure it is what you expected."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector.js:472
#, python-format
msgid "This domain is not supported."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search/favorites_menu.js:272
#, python-format
msgid ""
"This filter is global and will be removed for everybody if you continue."
msgstr "Το φίλτρο είναι καθολικό και θα αφαιρεθεί για όλους εάν συνεχίσετε."

#. module: web
#: model_terms:ir.ui.view,arch_db:web.preview_externalreport
msgid "This is a sample of an external report."
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.preview_internalreport
msgid "This is a sample of an internal report."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search/search_view.js:1039
#: code:addons/web/static/src/js/views/search/time_range_menu.js:128
#, python-format
msgid "Time Range"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1139
#, python-format
msgid "Time Ranges"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/special_fields.js:94
#, python-format
msgid ""
"Timezone Mismatch : The timezone of your browser doesn't match the selected "
"one. The time in Odoo is displayed according to the timezone set on your "
"user's preferences."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search/time_range_menu_options.js:11
#: code:addons/web/static/src/xml/web_calendar.xml:76
#: code:addons/web/static/src/xml/web_calendar.xml:89
#, python-format
msgid "Today"
msgstr "Σήμερα"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:244
#, python-format
msgid "Toggle Timelines"
msgstr "Εναλλαγή Χρονολογικών Σειρών"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/pivot/pivot_model.js:622
#: code:addons/web/static/src/js/views/pivot/pivot_model.js:770
#, python-format
msgid "Total"
msgstr "Σύνολο"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/abstract_web_client.js:211
#, python-format
msgid "Traceback:"
msgstr "Ανίχνευση προς τα πίσω:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/field_utils.js:62
#, python-format
msgid "True"
msgstr "Αληθές"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:577
#, python-format
msgid ""
"Try to add some records, or make sure\n"
"                that there is at least one measure and no active filter in the search bar."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph/graph_renderer.js:112
#, python-format
msgid ""
"Try to add some records, or make sure that there is no active filter in the "
"search bar."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/abstract_web_client.js:338
#, python-format
msgid "Trying to reconnect..."
msgstr "Προσπάθεια επανασύνδεσης..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:25
#, python-format
msgid "Type:"
msgstr "Τύπος:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/action_manager_report.js:24
#, python-format
msgid ""
"Unable to find Wkhtmltopdf on this system. The report will be shown in html."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list/list_controller.js:162
#, python-format
msgid "Unarchive"
msgstr "Μη αρχειοθετημένο"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:28
#, python-format
msgid "Unarchive All"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_model.js:578
#: code:addons/web/static/src/js/views/graph/graph_renderer.js:547
#: code:addons/web/static/src/js/views/kanban/kanban_column.js:76
#: code:addons/web/static/src/js/views/kanban/kanban_column.js:79
#: code:addons/web/static/src/js/views/kanban/kanban_renderer_mobile.js:182
#: code:addons/web/static/src/js/views/list/list_renderer.js:435
#: code:addons/web/static/src/js/views/list/list_renderer.js:438
#: code:addons/web/static/src/js/views/pivot/pivot_model.js:1078
#, python-format
msgid "Undefined"
msgstr "Μη ορισμένο"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:17
#, python-format
msgid "Unfold"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/abstract_web_client.js:202
#, python-format
msgid "Unknown CORS error"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/py_utils.js:299
#, python-format
msgid "Unknown nonliteral type "
msgstr "Άγνωστος μη γραμματικός τύπου"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list/list_editable_renderer.js:556
#, python-format
msgid "Unlink row "
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/_deprecated/data.js:739
#, python-format
msgid "Unnamed"
msgstr "Χωρίς όνομα"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/pivot/pivot_view.js:106
#, python-format
msgid "Untitled"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1393
#, python-format
msgid "Update data (import-compatible export)"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/upgrade_fields.js:69
#, python-format
msgid "Upgrade now"
msgstr "Αναβάθμιση τώρα"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1577
#, python-format
msgid "Upgrade to enterprise"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1571
#, python-format
msgid "Upgrade to future versions"
msgstr "Αναβάθμιση σε μελλοντικές εκδόσεις"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:974
#: code:addons/web/static/src/xml/base.xml:1005
#, python-format
msgid "Upload your file"
msgstr "Μεταφόρτωση του αρχείου σας"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:1807
#, python-format
msgid "Uploading Error"
msgstr "Σφάλμα αποστολής"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:938
#: code:addons/web/static/src/xml/base.xml:978
#: code:addons/web/static/src/xml/base.xml:1004
#: code:addons/web/static/src/xml/base.xml:1060
#, python-format
msgid "Uploading..."
msgstr "Αποστολή..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1365
#, python-format
msgid "Use by default"
msgstr "Χρησιμοποίησε ως προεπιλογή"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1387
#, python-format
msgid "Use data in a spreadsheet (export all data)"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1371
#, python-format
msgid "Users"
msgstr "Χρήστες"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:17
#, python-format
msgid "Validation Error"
msgstr "Σφάλμα Επικύρωσης"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/tools/debug_manager.js:287
#: code:addons/web/static/src/xml/base.xml:258
#, python-format
msgid "View Fields"
msgstr "Προβολή Πεδίων"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:270
#, python-format
msgid "View Metadata"
msgstr "Προβολή Μεταδομένων"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/action_manager.js:580
#: code:addons/web/static/src/js/chrome/action_manager_report.js:69
#: code:addons/web/static/src/js/services/crash_manager.js:13
#: code:addons/web/static/src/js/services/crash_manager.js:14
#: code:addons/web/static/src/js/views/basic/basic_controller.js:78
#: code:addons/web/static/src/js/views/list/list_renderer.js:352
#: code:addons/web/static/src/xml/base.xml:606
#, python-format
msgid "Warning"
msgstr "Προσοχή"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/attach_document.js:87
#, python-format
msgid "Warning : You have to save first before attaching a file."
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.benchmark_suite
msgid "Web Benchmarks"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.qunit_mobile_suite
msgid "Web Mobile Tests"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.qunit_suite
msgid "Web Tests"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_boxed
#: model_terms:ir.ui.view,arch_db:web.external_layout_standard
msgid "Web:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:559
#: code:addons/web/static/src/xml/web_calendar.xml:81
#, python-format
msgid "Week"
msgstr "Εβδομάδα"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/rainbow_man.js:37
#, python-format
msgid "Well Done!"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1383
#, python-format
msgid "What do you want to do?"
msgstr "Τι θέλετε να κάνετε;"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:29
#, python-format
msgid "Widget:"
msgstr "Γραφικό Στοιχείο:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/menu.xml:126
#, python-format
msgid "Windows/Linux"
msgstr ""

#. module: web
#: code:addons/web/controllers/main.py:497
#, python-format
msgid "Wrong login/password"
msgstr "Λανθασμένο όνομα χρήστη/κωδικός πρόσβασης"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:2283
#, python-format
msgid "Wrong value entered!"
msgstr "Έχει εισαχθεί λάθος τιμή!"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:290
#, python-format
msgid "XML ID:"
msgstr "XML ID:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:562
#, python-format
msgid "Year"
msgstr "Έτος"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:24
#: code:addons/web/static/src/xml/base.xml:862
#: code:addons/web/static/src/xml/kanban.xml:83
#, python-format
msgid "Yellow"
msgstr "Κίτρινο"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search/search_inputs.js:337
#: code:addons/web/static/src/xml/base.xml:49
#, python-format
msgid "Yes"
msgstr "Ναι"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search/time_range_menu_options.js:16
#, python-format
msgid "Yesterday"
msgstr "Χθες"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/abstract_web_client.js:352
#, python-format
msgid "You are back online"
msgstr "Έχετε επανασυνδεθεί"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:71
#, python-format
msgid "You are creating a new %s, are you sure it does not exist yet?"
msgstr ""
"Θέλετε να δημιουργήσετε μία νέα %s, είσαστε σίγουροι ότι δεν υπάρχει ήδη;"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/model_field_selector.js:467
#, python-format
msgid "You cannot follow relations for this field chain construction"
msgstr ""
"Δεν μπορείτε να ακολουθήσετε τις σχέσεις για αυτή την κατασκευή αλυσίδας "
"πεδίων"

#. module: web
#: code:addons/web/controllers/main.py:819
#, python-format
msgid "You cannot leave any password empty."
msgstr "Δεν μπορείτε να αφήσετε κανένα συνθηματικό κενό"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/misc.js:17
#, python-format
msgid ""
"You may not believe it,<br />but the application is actually loading..."
msgstr "Μπορεί να μην το πιστεύετε,<br/>αλλά η εφαρμογή φορτώνει..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/action_manager_report.js:29
#, python-format
msgid ""
"You need to start Odoo with at least two workers to print a pdf version of "
"the reports."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/action_manager_report.js:26
#, python-format
msgid ""
"You should upgrade your version of Wkhtmltopdf to at least 0.12.0 in order "
"to get a correct display of headers and footers as well as support for "
"table-breaking between pages."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:212
#, python-format
msgid "Your Odoo session expired. Please refresh the current web page."
msgstr ""
"Έληξε η Odoo συνεδρία σας. Παρακαλούμε ανανεώστε την τρέχουσα ιστοσελίδα."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/action_manager_report.js:22
#, python-format
msgid ""
"Your installation of Wkhtmltopdf seems to be broken. The report will be "
"shown in html."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:246
#, python-format
msgid "[No widget %s]"
msgstr "[Μη γραφικά στοιχεία %s]"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/translation.js:113
#, python-format
msgid "a day ago"
msgstr "Μια ημέρα πριν"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/translation.js:109
#, python-format
msgid "about a minute ago"
msgstr "πριν ένα λεπτό περίπου"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/translation.js:115
#, python-format
msgid "about a month ago"
msgstr "περίπου πριν από ένα μήνα"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/translation.js:117
#, python-format
msgid "about a year ago"
msgstr "περίπου ένα έτος πριν"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/translation.js:111
#, python-format
msgid "about an hour ago"
msgstr "περίπου πριν από μια ώρα"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:639
#, python-format
msgid "all records"
msgstr "όλες οι εγγραφές"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector.js:28
#, python-format
msgid "child of"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search/search_filters.js:168
#: code:addons/web/static/src/js/widgets/domain_selector.js:23
#, python-format
msgid "contains"
msgstr "περιέχει"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector.js:24
#, python-format
msgid "does not contain"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search/search_filters.js:169
#, python-format
msgid "doesn't contain"
msgstr "δεν περιέχει"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search/search_filters.js:275
#: code:addons/web/static/src/js/views/search/search_filters.js:304
#, python-format
msgid "greater than"
msgstr "μεγαλύτερη από"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search/search_filters.js:277
#: code:addons/web/static/src/js/views/search/search_filters.js:306
#, python-format
msgid "greater than or equal to"
msgstr "Μεγαλύτερο ή ίσο του"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector.js:25
#, python-format
msgid "in"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search/search_filters.js:296
#: code:addons/web/static/src/js/views/search/search_filters.js:331
#: code:addons/web/static/src/js/widgets/domain_selector.js:863
#: code:addons/web/static/src/xml/base.xml:727
#, python-format
msgid "is"
msgstr "είναι"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search/search_filters.js:188
#, python-format
msgid "is after"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search/search_filters.js:189
#, python-format
msgid "is before"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search/search_filters.js:190
#, python-format
msgid "is between"
msgstr "είναι ανάμεσα"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search/search_filters.js:170
#: code:addons/web/static/src/js/views/search/search_filters.js:186
#: code:addons/web/static/src/js/views/search/search_filters.js:273
#: code:addons/web/static/src/js/views/search/search_filters.js:302
#, python-format
msgid "is equal to"
msgstr "είναι ίσο με"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search/search_filters.js:350
#, python-format
msgid "is false"
msgstr "είναι ψευδής"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search/search_filters.js:332
#: code:addons/web/static/src/js/widgets/domain_selector.js:864
#, python-format
msgid "is not"
msgstr "δεν είναι"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector.js:18
#, python-format
msgid "is not ="
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search/search_filters.js:171
#: code:addons/web/static/src/js/views/search/search_filters.js:187
#: code:addons/web/static/src/js/views/search/search_filters.js:274
#: code:addons/web/static/src/js/views/search/search_filters.js:303
#, python-format
msgid "is not equal to"
msgstr "δεν είναι ίσο με"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search/search_filters.js:173
#: code:addons/web/static/src/js/views/search/search_filters.js:192
#: code:addons/web/static/src/js/views/search/search_filters.js:280
#: code:addons/web/static/src/js/views/search/search_filters.js:309
#: code:addons/web/static/src/js/views/search/search_filters.js:334
#: code:addons/web/static/src/js/widgets/domain_selector.js:37
#, python-format
msgid "is not set"
msgstr "δεν έχει οριστεί"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search/search_filters.js:172
#: code:addons/web/static/src/js/views/search/search_filters.js:191
#: code:addons/web/static/src/js/views/search/search_filters.js:279
#: code:addons/web/static/src/js/views/search/search_filters.js:308
#: code:addons/web/static/src/js/views/search/search_filters.js:333
#: code:addons/web/static/src/js/widgets/domain_selector.js:36
#, python-format
msgid "is set"
msgstr "έχει οριστεί"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search/search_filters.js:349
#, python-format
msgid "is true"
msgstr "είναι αληθές"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/utils.js:113
#, python-format
msgid "kMGTPE"
msgstr "kMGTPE"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search/search_filters.js:276
#: code:addons/web/static/src/js/views/search/search_filters.js:305
#, python-format
msgid "less than"
msgstr "μικρότερο από"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/translation.js:108
#, python-format
msgid "less than a minute ago"
msgstr "λιγότερο από ένα λεπτό πριν"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search/search_filters.js:278
#: code:addons/web/static/src/js/views/search/search_filters.js:307
#, python-format
msgid "less than or equal to"
msgstr "μικρότερο ή ίσο από"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:729
#, python-format
msgid "not"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search/search_inputs.js:237
#, python-format
msgid "not a valid integer"
msgstr "δεν είναι έγκυρος ακέραιος"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search/search_inputs.js:252
#, python-format
msgid "not a valid number"
msgstr "μη έγκυρος αριθμός"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector.js:26
#, python-format
msgid "not in"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector.js:674
#, python-format
msgid "not set (false)"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:648
#, python-format
msgid "of the following rules:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:663
#, python-format
msgid "of:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:720
#: code:addons/web/static/src/xml/base.xml:1320
#, python-format
msgid "or"
msgstr "ή"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector.js:29
#, python-format
msgid "parent of"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:604
#, python-format
msgid "record(s)"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:42
#, python-format
msgid "remaining)"
msgstr "απομένουν)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:729
#, python-format
msgid "set"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector.js:674
#, python-format
msgid "set (true)"
msgstr ""
