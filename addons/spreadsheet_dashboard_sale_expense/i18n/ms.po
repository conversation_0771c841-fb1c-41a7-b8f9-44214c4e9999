# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* spreadsheet_dashboard_sale_expense
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-12-15 12:44+0000\n"
"PO-Revision-Date: 2022-12-16 09:48+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: Malay (https://app.transifex.com/odoo/teams/41243/ms/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ms\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: spreadsheet_dashboard_sale_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "# Expenses"
msgstr ""

#. module: spreadsheet_dashboard_sale_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "Category"
msgstr "kategori"

#. module: spreadsheet_dashboard_sale_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "Employee"
msgstr "Pekerja"

#. module: spreadsheet_dashboard_sale_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "Expense"
msgstr "Perbelanjaan"

#. module: spreadsheet_dashboard_sale_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "Expenses"
msgstr "Perbelanjaan"

#. module: spreadsheet_dashboard_sale_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "Expenses Analysis"
msgstr "Analisis Perbelanjaan"

#. module: spreadsheet_dashboard_sale_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "Expenses Analysis by Customer to Reinvoice"
msgstr ""

#. module: spreadsheet_dashboard_sale_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "KPI - Expenses"
msgstr ""

#. module: spreadsheet_dashboard_sale_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "KPI - To reimburse"
msgstr ""

#. module: spreadsheet_dashboard_sale_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "KPI - To report"
msgstr ""

#. module: spreadsheet_dashboard_sale_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "KPI - To validate"
msgstr ""

#. module: spreadsheet_dashboard_sale_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "Order"
msgstr "Pesanan"

#. module: spreadsheet_dashboard_sale_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "Period"
msgstr "Tempoh"

#. module: spreadsheet_dashboard_sale_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "Product"
msgstr "Produk"

#. module: spreadsheet_dashboard_sale_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "To reimburse"
msgstr ""

#. module: spreadsheet_dashboard_sale_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "To report"
msgstr ""

#. module: spreadsheet_dashboard_sale_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "To validate"
msgstr "Untuk mengesahkan"

#. module: spreadsheet_dashboard_sale_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "Top Categories"
msgstr "Kategori Teratas"

#. module: spreadsheet_dashboard_sale_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "Top Employees"
msgstr "Pekerja Teratas"

#. module: spreadsheet_dashboard_sale_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "Top Expenses"
msgstr "Perbelanjaan Teratas"

#. module: spreadsheet_dashboard_sale_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "Top Reinvoiced Orders"
msgstr "Pesanan Diinvois Semula Teratas"

#. module: spreadsheet_dashboard_sale_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "Total"
msgstr "Jumlah"
