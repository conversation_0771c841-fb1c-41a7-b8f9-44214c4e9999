<?xml version="1.0" encoding="UTF-8" ?>
<templates xml:space="preserve">

    <t
        t-name="mail_tracking.MessageTracking"
        t-inherit="mail.Message"
        t-inherit-mode="extension"
    >
        <t t-if="messageView" position="attributes">
            <attribute
                name="t-if"
                add="&amp;&amp; (
                    !messageView.isInFailedDiscuss || (
                        messageView.isInFailedDiscuss
                        &amp;&amp; !store.reviewedMessageIds.has(messageView.message.id)
                    )
                )"
                separator=" "
            />
        </t>
        <xpath expr="//div[hasclass('o_Message_header')]" position="inside">
            <!-- Show options only for Discuss failed messages and messages from FailedMessage component -->
            <span
                t-if="!store.reviewedMessageIds.has(messageView.message.id) &amp;&amp; (
                    (
                        messageView.message.isFailed
                        &amp;&amp; messageView.isInDiscuss
                    ) || (
                        this.props.isFailedMessage
                        &amp;&amp; messageView.isFailedChatterMessageView
                    )
                )"
                t-attf-class="o_thread_icons"
            >
                <a
                    href="#"
                    class="btn btn-link btn-success o_thread_icon text-muted btn-sm o_failed_message_reviewed o_activity_link"
                    role="button"
                    t-on-click="_onMarkFailedMessageReviewed"
                    t-att-data-message-id="messageView.message.id"
                >
                    <i class="fa fa-check" role="img" aria-label="Set As Reviewed" />
                    Set as Reviewed
                </a>
                <a
                    href="#"
                    class="btn btn-link btn-success o_thread_icon text-muted btn-sm o_failed_message_retry o_activity_link"
                    role="button"
                    t-on-click="_onRetryFailedMessage"
                    t-att-data-message-id="messageView.message.id"
                >
                    <i class="fa fa-retweet" role="img" aria-label="Retry" />
                    Retry
                </a>
            </span>
        </xpath>
        <xpath expr="//div[hasclass('o_Message_header')]" position="after">
            <p
                t-if="messageView.message.hasPartnerTrackings() || messageView.message.hasEmailCc()"
                class="o_mail_tracking"
            >
                <strong>To:</strong>
                <t
                    t-foreach="messageView.message.getPartnerTrackings()"
                    t-as="tracking"
                    t-key="tracking_index"
                >
                    <t t-if="!tracking_first">
                        -
                    </t>
                    <a
                        t-if="tracking['partner_id']"
                        t-attf-class="o_mail_action_tracking_partner #{tracking['isCc'] ? 'o_mail_cc' : ''}"
                        t-att-data-partner="tracking['partner_id']"
                        data-oe-model="res.partner"
                        t-att-data-oe-id="tracking['partner_id']"
                        t-out="tracking['recipient']"
                    />
                    <span
                        t-else=""
                        t-attf-class="#{tracking['isCc'] ? 'o_mail_cc' : ''}"
                        t-out="tracking['recipient']"
                    />
                    <t
                        t-if="tracking['status'] === 'error' &amp;&amp; tracking['error_type'] === 'no_recipient'"
                        t-set="title_status"
                        t-value="tracking['error_description']"
                    />
                    <t
                        t-else=""
                        t-set="title_status"
                        t-value="tracking['status_human']"
                    />
                    <span
                        class="mail_tracking o_mail_action_tracking_status"
                        t-att-data-tracking="tracking['tracking_id']"
                        t-att-title="title_status"
                        type="button"
                        t-on-click="_onTrackingStatusClick"
                    >
                        <t t-call="mail_tracking.TrackingStatus" />
                    </span>
                </t>
            </p>
        </xpath>
    </t>

</templates>
