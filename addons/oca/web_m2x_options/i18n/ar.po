# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * web_m2x_options
#
# Translators:
# <AUTHOR> <EMAIL>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-01-03 03:50+0000\n"
"PO-Revision-Date: 2018-01-03 03:50+0000\n"
"Last-Translator: OCA Transbot <<EMAIL>>, 2017\n"
"Language-Team: Arabic (https://www.transifex.com/oca/teams/23907/ar/)\n"
"Language: ar\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 "
"&& n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: web_m2x_options
#. odoo-javascript
#: code:addons/web_m2x_options/static/src/components/base.xml:0
#, python-format
msgid ", are you sure it does not exist yet?"
msgstr ""

#. module: web_m2x_options
#. odoo-javascript
#: code:addons/web_m2x_options/static/src/components/base.xml:0
#, python-format
msgid "Create"
msgstr ""

#. module: web_m2x_options
#. odoo-javascript
#: code:addons/web_m2x_options/static/src/components/relational_utils.esm.js:0
#, python-format
msgid "Create \"%s\""
msgstr ""

#. module: web_m2x_options
#. odoo-javascript
#: code:addons/web_m2x_options/static/src/components/base.xml:0
#, python-format
msgid "Create and Edit"
msgstr ""

#. module: web_m2x_options
#. odoo-javascript
#: code:addons/web_m2x_options/static/src/components/relational_utils.esm.js:0
#, python-format
msgid "Create and edit..."
msgstr ""

#. module: web_m2x_options
#. odoo-javascript
#: code:addons/web_m2x_options/static/src/components/base.xml:0
#, python-format
msgid "Discard"
msgstr ""

#. module: web_m2x_options
#: model:ir.model,name:web_m2x_options.model_ir_http
msgid "HTTP Routing"
msgstr ""

#. module: web_m2x_options
#. odoo-javascript
#: code:addons/web_m2x_options/static/src/components/form.esm.js:0
#, python-format
msgid "New: %s"
msgstr ""

#. module: web_m2x_options
#. odoo-javascript
#: code:addons/web_m2x_options/static/src/components/relational_utils.esm.js:0
#, python-format
msgid "No records"
msgstr ""

#. module: web_m2x_options
#. odoo-javascript
#: code:addons/web_m2x_options/static/src/components/form.esm.js:0
#, python-format
msgid "Open: "
msgstr ""

#. module: web_m2x_options
#. odoo-javascript
#: code:addons/web_m2x_options/static/src/components/relational_utils.esm.js:0
#, python-format
msgid "Search More..."
msgstr "البحث عن المزيد ..."

#. module: web_m2x_options
#. odoo-javascript
#: code:addons/web_m2x_options/static/src/components/relational_utils.esm.js:0
#, python-format
msgid "Start typing..."
msgstr ""

#. module: web_m2x_options
#: model:ir.model,name:web_m2x_options.model_ir_config_parameter
msgid "System Parameter"
msgstr ""

#. module: web_m2x_options
#. odoo-javascript
#: code:addons/web_m2x_options/static/src/components/base.xml:0
#, python-format
msgid "You are creating a new"
msgstr ""

#. module: web_m2x_options
#. odoo-javascript
#: code:addons/web_m2x_options/static/src/components/base.xml:0
#, python-format
msgid "as a new"
msgstr ""

#, python-format
#~ msgid "Cancel"
#~ msgstr "إلغاء"

#, python-format
#~ msgid "Create \"<strong>%s</strong>\""
#~ msgstr "إنشاء \"<strong>%s</strong>\""

#, python-format
#~ msgid "Create and Edit..."
#~ msgstr "إنشاء وتحرير ..."
