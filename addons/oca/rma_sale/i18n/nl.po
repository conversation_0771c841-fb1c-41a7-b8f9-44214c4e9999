# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* rma_sale
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2020-12-13 23:19+0000\n"
"Last-Translator: Bosd <<EMAIL>>\n"
"Language-Team: none\n"
"Language: nl\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 4.3.2\n"

#. module: rma_sale
#. odoo-python
#: code:addons/rma_sale/controllers/sale_portal.py:0
#, python-format
msgid " (Portal)"
msgstr " (Portal)"

#. module: rma_sale
#. odoo-python
#: code:addons/rma_sale/wizard/sale_order_rma_wizard.py:0
#, python-format
msgid " has been created."
msgstr " is aangemaakt."

#. module: rma_sale
#. odoo-python
#: code:addons/rma_sale/wizard/sale_order_rma_wizard.py:0
#, python-format
msgid " have been created."
msgstr " is aangemaakt."

#. module: rma_sale
#: model_terms:ir.ui.view,arch_db:rma_sale.sale_rma_request_form
msgid "<i class=\"fa fa-check\"/> Request RMAs"
msgstr ""

#. module: rma_sale
#: model_terms:ir.ui.view,arch_db:rma_sale.sale_order_portal_template
msgid "<i class=\"fa fa-reply\"/> Request RMAs"
msgstr ""

#. module: rma_sale
#: model_terms:ir.ui.view,arch_db:rma_sale.sale_rma_request_form
msgid "<i class=\"fa fa-times\"/> Cancel"
msgstr ""

#. module: rma_sale
#: model_terms:ir.ui.view,arch_db:rma_sale.sale_rma_request_form
msgid "<i class=\"fa fa-truck\"/> Choose a delivery address"
msgstr ""

#. module: rma_sale
#: model_terms:ir.ui.view,arch_db:rma_sale.sale_rma_request_form
msgid "<option value=\"\">---</option>"
msgstr ""

#. module: rma_sale
#: model_terms:ir.ui.view,arch_db:rma_sale.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""

#. module: rma_sale
#: model_terms:ir.ui.view,arch_db:rma_sale.sale_order_portal_content
msgid ""
"<span class=\"fa fa-reply\" role=\"img\" aria-label=\"RMA\" title=\"RMA\"/>"
msgstr ""

#. module: rma_sale
#: model_terms:ir.ui.view,arch_db:rma_sale.sale_order_portal_content
msgid "<span>RMA</span>"
msgstr "<span>RMA</span>"

#. module: rma_sale
#: model_terms:ir.ui.view,arch_db:rma_sale.portal_rma_page
msgid "<strong>Requested operation</strong>"
msgstr ""

#. module: rma_sale
#: model_terms:ir.ui.view,arch_db:rma_sale.portal_rma_page
msgid "<strong>Sale order</strong>"
msgstr ""

#. module: rma_sale
#: model_terms:ir.ui.view,arch_db:rma_sale.sale_order_rma_wizard_form_view
msgid "Accept"
msgstr "Accepteer"

#. module: rma_sale
#: model:ir.model.fields,field_description:rma_sale.field_rma__allowed_move_ids
msgid "Allowed Move"
msgstr ""

#. module: rma_sale
#: model:ir.model.fields,field_description:rma_sale.field_rma__allowed_picking_ids
#: model:ir.model.fields,field_description:rma_sale.field_sale_order_line_rma_wizard__allowed_picking_ids
msgid "Allowed Picking"
msgstr ""

#. module: rma_sale
#: model:ir.model.fields,field_description:rma_sale.field_rma__allowed_product_ids
#: model:ir.model.fields,field_description:rma_sale.field_sale_order_line_rma_wizard__allowed_product_ids
msgid "Allowed Product"
msgstr ""

#. module: rma_sale
#: model:ir.model.fields,field_description:rma_sale.field_sale_order_line_rma_wizard__allowed_quantity
msgid "Allowed Quantity"
msgstr ""

#. module: rma_sale
#: model_terms:ir.ui.view,arch_db:rma_sale.sale_order_rma_wizard_form_view
msgid "Cancel"
msgstr "Annuleren"

#. module: rma_sale
#: model:ir.model.fields,field_description:rma_sale.field_sale_order_line_rma_wizard__uom_category_id
msgid "Category"
msgstr "Categorie"

#. module: rma_sale
#: model_terms:ir.ui.view,arch_db:rma_sale.sale_rma_request_form
msgid "Close"
msgstr "Sluiten"

#. module: rma_sale
#: model_terms:ir.ui.view,arch_db:rma_sale.sale_rma_request_form
msgid ""
"Comment anything relevant to the return, like serial numbers, a description "
"of the issue, etc"
msgstr ""
"Relevante opmerking voor de retour zending zoals; serie nummers, "
"probleemomschrijving, etc"

#. module: rma_sale
#: model:ir.model.fields,field_description:rma_sale.field_sale_order_rma_wizard__commercial_partner_id
msgid "Commercial entity"
msgstr ""

#. module: rma_sale
#: model:ir.model,name:rma_sale.model_res_company
msgid "Companies"
msgstr ""

#. module: rma_sale
#: model:ir.model,name:rma_sale.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: rma_sale
#: model:ir.model.fields,help:rma_sale.field_sale_order_line_rma_wizard__uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr ""

#. module: rma_sale
#. odoo-python
#: code:addons/rma_sale/models/sale.py:0
#: model_terms:ir.ui.view,arch_db:rma_sale.view_order_form
#, python-format
msgid "Create RMA"
msgstr "RMA aanmaken"

#. module: rma_sale
#: model:ir.actions.act_window,name:rma_sale.sale_order_create_rma_action
msgid "Create RMAs"
msgstr ""

#. module: rma_sale
#: model:ir.model.fields,field_description:rma_sale.field_sale_order_line_rma_wizard__create_uid
#: model:ir.model.fields,field_description:rma_sale.field_sale_order_rma_wizard__create_uid
msgid "Created by"
msgstr "Aangemaakt door"

#. module: rma_sale
#: model:ir.model.fields,field_description:rma_sale.field_sale_order_line_rma_wizard__create_date
#: model:ir.model.fields,field_description:rma_sale.field_sale_order_rma_wizard__create_date
msgid "Created on"
msgstr "Aangemaakt op"

#. module: rma_sale
#: model:ir.model.fields,field_description:rma_sale.field_sale_order_rma_wizard__custom_description
msgid "Custom Description"
msgstr ""

#. module: rma_sale
#: model_terms:ir.ui.view,arch_db:rma_sale.sale_rma_request_form
msgid "Delivery"
msgstr "Levering"

#. module: rma_sale
#: model:ir.model.fields,field_description:rma_sale.field_sale_order_line_rma_wizard__picking_id
msgid "Delivery order"
msgstr ""

#. module: rma_sale
#: model:ir.model.fields,field_description:rma_sale.field_sale_order_line_rma_wizard__description
msgid "Description"
msgstr "Omschrijving"

#. module: rma_sale
#: model:ir.model.fields,field_description:rma_sale.field_sale_order_line_rma_wizard__different_return_product
msgid "Different Return Product"
msgstr ""

#. module: rma_sale
#: model:ir.model.fields,field_description:rma_sale.field_sale_order_line_rma_wizard__display_name
#: model:ir.model.fields,field_description:rma_sale.field_sale_order_rma_wizard__display_name
msgid "Display Name"
msgstr "Weergavenaam"

#. module: rma_sale
#: model:ir.model.fields,help:rma_sale.field_res_company__show_full_page_sale_rma
#: model:ir.model.fields,help:rma_sale.field_res_config_settings__show_full_page_sale_rma
msgid ""
"From the frontend sale order page go to a single RMA page creation instead "
"of the usual popup"
msgstr ""

#. module: rma_sale
#: model:ir.model.fields,field_description:rma_sale.field_res_company__show_full_page_sale_rma
#: model:ir.model.fields,field_description:rma_sale.field_res_config_settings__show_full_page_sale_rma
msgid "Full page RMA creation"
msgstr ""

#. module: rma_sale
#: model:ir.model.fields,field_description:rma_sale.field_sale_order_line_rma_wizard__id
#: model:ir.model.fields,field_description:rma_sale.field_sale_order_rma_wizard__id
msgid "ID"
msgstr "ID"

#. module: rma_sale
#: model:ir.model.fields,help:rma_sale.field_sale_order_line_rma_wizard__different_return_product
msgid ""
"If checked, allows the return of a product different from the one originally "
"ordered. Used if the delivery is created automatically"
msgstr ""

#. module: rma_sale
#: model_terms:ir.ui.view,arch_db:rma_sale.sale_rma_request_form
msgid "If no requested operation is set, the RMA won't be correctly fulfilled"
msgstr ""

#. module: rma_sale
#: model:ir.model,name:rma_sale.model_account_move
msgid "Journal Entry"
msgstr ""

#. module: rma_sale
#: model:ir.model.fields,field_description:rma_sale.field_sale_order_line_rma_wizard____last_update
#: model:ir.model.fields,field_description:rma_sale.field_sale_order_rma_wizard____last_update
msgid "Last Modified on"
msgstr "Laatst bijgewerkt op"

#. module: rma_sale
#: model:ir.model.fields,field_description:rma_sale.field_sale_order_line_rma_wizard__write_uid
#: model:ir.model.fields,field_description:rma_sale.field_sale_order_rma_wizard__write_uid
msgid "Last Updated by"
msgstr "Laatste update door"

#. module: rma_sale
#: model:ir.model.fields,field_description:rma_sale.field_sale_order_line_rma_wizard__write_date
#: model:ir.model.fields,field_description:rma_sale.field_sale_order_rma_wizard__write_date
msgid "Last Updated on"
msgstr "Laatste update op"

#. module: rma_sale
#: model:ir.model.fields,field_description:rma_sale.field_sale_order_rma_wizard__line_ids
msgid "Lines"
msgstr "Regels"

#. module: rma_sale
#: model:ir.model.fields,field_description:rma_sale.field_sale_order_line_rma_wizard__move_id
msgid "Move"
msgstr ""

#. module: rma_sale
#: model:ir.model.fields,field_description:rma_sale.field_sale_order_line_rma_wizard__order_id
#: model:ir.model.fields,field_description:rma_sale.field_sale_order_rma_wizard__order_id
msgid "Order"
msgstr "Bestelling"

#. module: rma_sale
#: model:ir.model.fields,field_description:rma_sale.field_rma__picking_id
msgid "Origin Delivery"
msgstr "Originele levering"

#. module: rma_sale
#: model:ir.model.fields,field_description:rma_sale.field_rma__move_id
msgid "Origin move"
msgstr ""

#. module: rma_sale
#: model:ir.model.fields,field_description:rma_sale.field_rma__product_id
#: model:ir.model.fields,field_description:rma_sale.field_sale_order_line_rma_wizard__product_id
#: model_terms:ir.ui.view,arch_db:rma_sale.sale_rma_request_form
msgid "Product"
msgstr "Product"

#. module: rma_sale
#: model:ir.model.fields,help:rma_sale.field_sale_order_line_rma_wizard__return_product_id
msgid ""
"Product to be returned if it's different from the originally delivered item."
msgstr ""

#. module: rma_sale
#: model:ir.model.fields,field_description:rma_sale.field_sale_order_line_rma_wizard__quantity
#: model_terms:ir.ui.view,arch_db:rma_sale.sale_rma_request_form
msgid "Quantity"
msgstr "Aantal"

#. module: rma_sale
#: model:ir.model,name:rma_sale.model_rma
#: model_terms:ir.ui.view,arch_db:rma_sale.view_order_form
msgid "RMA"
msgstr "RMA"

#. module: rma_sale
#: model:ir.model.fields,field_description:rma_sale.field_sale_order__rma_count
msgid "RMA count"
msgstr "RMA aantal"

#. module: rma_sale
#: model:ir.model.fields,field_description:rma_sale.field_sale_order_rma_wizard__location_id
msgid "RMA location"
msgstr "RMA Locatie"

#. module: rma_sale
#: model_terms:ir.ui.view,arch_db:rma_sale.request_rma_single_page
msgid "RMA request for order"
msgstr ""

#. module: rma_sale
#: model:ir.model.fields,field_description:rma_sale.field_sale_order__rma_ids
msgid "RMAs"
msgstr "RMA's"

#. module: rma_sale
#: model:ir.model.fields,field_description:rma_sale.field_rma__refund_id
msgid "Refund"
msgstr ""

#. module: rma_sale
#: model_terms:ir.ui.view,arch_db:rma_sale.sale_rma_request_form
msgid "Request RMAs"
msgstr "aangevraagde RMA's"

#. module: rma_sale
#: model:ir.model.fields,field_description:rma_sale.field_sale_order_line_rma_wizard__operation_id
#: model:ir.model.fields,field_description:rma_sale.field_sale_order_rma_wizard__operation_id
#: model_terms:ir.ui.view,arch_db:rma_sale.sale_rma_request_form
#, fuzzy
msgid "Requested operation"
msgstr "Verzochte handeling"

#. module: rma_sale
#: model:ir.model.fields,field_description:rma_sale.field_sale_order_rma_wizard__is_return_all
msgid "Return All?"
msgstr ""

#. module: rma_sale
#: model:ir.model,name:rma_sale.model_stock_return_picking
msgid "Return Picking"
msgstr ""

#. module: rma_sale
#: model:ir.model.fields,field_description:rma_sale.field_sale_order_line_rma_wizard__return_product_id
msgid "Return Product"
msgstr ""

#. module: rma_sale
#: model:ir.model.fields,field_description:rma_sale.field_rma__sale_line_id
#: model:ir.model.fields,field_description:rma_sale.field_sale_order_line_rma_wizard__sale_line_id
msgid "Sale Line"
msgstr ""

#. module: rma_sale
#: model:ir.model.fields,field_description:rma_sale.field_rma__order_id
msgid "Sale Order"
msgstr "Verkoop order"

#. module: rma_sale
#: model:ir.model,name:rma_sale.model_sale_order_line_rma_wizard
msgid "Sale Order Line Rma Wizard"
msgstr ""

#. module: rma_sale
#: model:ir.model,name:rma_sale.model_sale_order_rma_wizard
msgid "Sale Order Rma Wizard"
msgstr "Verkooporder retour gids"

#. module: rma_sale
#: model_terms:ir.ui.view,arch_db:rma_sale.report_rma_document
msgid "Sale order"
msgstr ""

#. module: rma_sale
#: model:ir.model,name:rma_sale.model_sale_order
msgid "Sales Order"
msgstr "Verkooporder"

#. module: rma_sale
#: model:ir.model,name:rma_sale.model_sale_order_line
msgid "Sales Order Line"
msgstr "verkoopregel"

#. module: rma_sale
#: model_terms:ir.ui.view,arch_db:rma_sale.sale_rma_request_form
msgid "Select the product quantity and the requested operation"
msgstr "Selecteer het productaantal en de gewenste handeling"

#. module: rma_sale
#: model:ir.model.fields,field_description:rma_sale.field_sale_order_rma_wizard__partner_shipping_id
msgid "Shipping Address"
msgstr ""

#. module: rma_sale
#: model_terms:ir.ui.view,arch_db:rma_sale.res_config_settings_view_form
msgid "Show portal RMA request in a single page"
msgstr ""

#. module: rma_sale
#: model_terms:ir.ui.view,arch_db:rma_sale.res_config_settings_view_form
msgid "Single page RMA request"
msgstr ""

#. module: rma_sale
#: model_terms:ir.ui.view,arch_db:rma_sale.sale_rma_request_form
msgid "The limit will decrease when the units in other RMAs are confirmed"
msgstr ""

#. module: rma_sale
#: model:ir.model.fields,field_description:rma_sale.field_sale_order_line_rma_wizard__uom_id
#, fuzzy
msgid "Unit of Measure"
msgstr "Grootheid"

#. module: rma_sale
#: model_terms:ir.ui.view,arch_db:rma_sale.sale_rma_request_form
msgid ""
"Use the comment button to add relevant information regarding the RMA, like "
"returned serial numbers or a description of the issue"
msgstr ""
"Gebruik de onderstaande opmerkingen knop om relevante informatie over de RMA "
"in te vullen, zoals serienummer of een omschrijving van het probleem"

#. module: rma_sale
#: model:ir.model.fields,help:rma_sale.field_sale_order_rma_wizard__custom_description
msgid "Values coming from portal RMA request form custom fields"
msgstr ""

#. module: rma_sale
#: model_terms:ir.ui.view,arch_db:rma_sale.res_config_settings_view_form
msgid ""
"When we hit the RMA request button from the portal sale page, open in a "
"single page instead of a popup."
msgstr ""

#. module: rma_sale
#: model:ir.model.fields,help:rma_sale.field_sale_order_rma_wizard__partner_shipping_id
msgid "Will be used to return the goods when the RMA is completed"
msgstr ""

#. module: rma_sale
#: model:ir.model.fields,field_description:rma_sale.field_sale_order_line_rma_wizard__wizard_id
msgid "Wizard"
msgstr "gids"

#. module: rma_sale
#: model_terms:ir.ui.view,arch_db:rma_sale.sale_rma_request_form
msgid ""
"You can only return as much product units as you received for this order"
msgstr ""
"U kunt maximaal het aantal producten retourneren als dat u in deze "
"bestelling heeft ontvangen"

#. module: rma_sale
#: model_terms:ir.ui.view,arch_db:rma_sale.sale_rma_request_form
#, fuzzy
msgid "You can send a message in every RMA sent"
msgstr "U kunt een bericht sturen in elke RMA zending"

#. module: rma_sale
#. odoo-python
#: code:addons/rma_sale/wizard/sale_order_rma_wizard.py:0
#, python-format
msgid ""
"You can't exceed the allowed quantity for returning product %(product)s."
msgstr ""

#. module: rma_sale
#. odoo-python
#: code:addons/rma_sale/models/sale.py:0
#, python-format
msgid "You may only create RMAs from a confirmed or done sale order."
msgstr "U mag alleen een RMA maken van bevestigde of afgehandelde verkopen."

#. module: rma_sale
#: model_terms:ir.ui.view,arch_db:rma_sale.sale_rma_request_form
#, fuzzy
msgid ""
"You're about to perform an RMA request. Our team will process it an will "
"reach you once it's validated. Keep in mind that:"
msgstr ""
"U staat op het punt een RMA verzoek in te dienen. Ons team zal de aanvraag "
"verwerken. Let op dat:"

#~ msgid "Stock Move"
#~ msgstr "Voorraad aanpassing"
