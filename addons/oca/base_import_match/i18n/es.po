# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * base_import_match
#
# Translators:
# <PERSON> <<EMAIL>>, 2017
# <AUTHOR> <EMAIL>, 2017
# <PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-12-01 02:10+0000\n"
"PO-Revision-Date: 2023-08-28 09:10+0000\n"
"Last-Translator: Ivorra78 <<EMAIL>>\n"
"Language-Team: Spanish (https://www.transifex.com/oca/teams/23907/es/)\n"
"Language: es\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 4.17\n"

#. module: base_import_match
#: model:ir.model,name:base_import_match.model_base
msgid "Base"
msgstr "Base"

#. module: base_import_match
#: model:ir.model.fields,field_description:base_import_match.field_base_import_match_field__conditional
msgid "Conditional"
msgstr "Condicional"

#. module: base_import_match
#: model:ir.model.fields,field_description:base_import_match.field_base_import_match__create_uid
#: model:ir.model.fields,field_description:base_import_match.field_base_import_match_field__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: base_import_match
#: model:ir.model.fields,field_description:base_import_match.field_base_import_match__create_date
#: model:ir.model.fields,field_description:base_import_match.field_base_import_match_field__create_date
msgid "Created on"
msgstr "Creado el"

#. module: base_import_match
#: model:ir.model,name:base_import_match.model_base_import_match
msgid "Deduplicate settings prior to CSV imports."
msgstr "Configuración para deduplicar antes de importar CSV."

#. module: base_import_match
#: model:ir.model.fields,field_description:base_import_match.field_base_import_match__display_name
#: model:ir.model.fields,field_description:base_import_match.field_base_import_match_field__display_name
msgid "Display Name"
msgstr "Nombre a mostrar"

#. module: base_import_match
#: model:ir.model.fields,help:base_import_match.field_base_import_match_field__conditional
msgid "Enable if you want to use this field only in some conditions."
msgstr "Habilítelo si quiere usar este campo sólo bajo ciertas condiciones."

#. module: base_import_match
#: model:ir.model.fields,field_description:base_import_match.field_base_import_match_field__field_id
msgid "Field"
msgstr "Campo"

#. module: base_import_match
#: model:ir.model.fields,field_description:base_import_match.field_base_import_match_field__name
msgid "Field Name"
msgstr "Nombre del campo"

#. module: base_import_match
#: model:ir.model,name:base_import_match.model_base_import_match_field
msgid "Field import match definition"
msgstr "Definición de concordancia de importación del campo"

#. module: base_import_match
#: model:ir.model.fields,help:base_import_match.field_base_import_match_field__field_id
msgid "Field that will be part of an unique key."
msgstr "Campo que formará parte de una clave única."

#. module: base_import_match
#: model:ir.model.fields,field_description:base_import_match.field_base_import_match__field_ids
msgid "Fields"
msgstr "Campos"

#. module: base_import_match
#: model:ir.model.fields,help:base_import_match.field_base_import_match__field_ids
msgid "Fields that will define an unique key."
msgstr "Campos que definirán una clave única."

#. module: base_import_match
#: model_terms:ir.ui.view,arch_db:base_import_match.match_search_view
msgid "Group By"
msgstr "Agrupar por"

#. module: base_import_match
#: model:ir.model.fields,field_description:base_import_match.field_base_import_match__id
#: model:ir.model.fields,field_description:base_import_match.field_base_import_match_field__id
msgid "ID"
msgstr "ID"

#. module: base_import_match
#: model:ir.model.fields,help:base_import_match.field_base_import_match_field__imported_value
msgid ""
"If the imported value is not this, the whole matching rule will be "
"discarded. Be careful, this data is always treated as a string, and "
"comparison is case-sensitive so if you set 'True', it will NOT match '1' nor "
"'true', only EXACTLY 'True'."
msgstr ""
"Si el valor importado no es éste, la regla de concordancia será descartada. "
"Tenga cuidado, ya que estos datos siempre serán tratados como una cadena, y "
"la comparación en sensible a las mayúsculas, por lo que si establece 'True', "
"no concordará con '1' ni con 'true'."

#. module: base_import_match
#: model:ir.actions.act_window,name:base_import_match.match_action
#: model:ir.ui.menu,name:base_import_match.match_menu
#: model_terms:ir.ui.view,arch_db:base_import_match.match_form_view
#: model_terms:ir.ui.view,arch_db:base_import_match.match_search_view
msgid "Import Match"
msgstr "Coincidencia de importación"

#. module: base_import_match
#: model:ir.model.fields,field_description:base_import_match.field_base_import_match_field__imported_value
msgid "Imported Value"
msgstr "Valor importado"

#. module: base_import_match
#: model:ir.model.fields,help:base_import_match.field_base_import_match__model_id
#: model:ir.model.fields,help:base_import_match.field_base_import_match_field__model_id
msgid "In this model you will apply the match."
msgstr "En este modelo se aplicará la coincidencia."

#. module: base_import_match
#: model:ir.model.fields,field_description:base_import_match.field_base_import_match____last_update
#: model:ir.model.fields,field_description:base_import_match.field_base_import_match_field____last_update
msgid "Last Modified on"
msgstr "Última actualización por"

#. module: base_import_match
#: model:ir.model.fields,field_description:base_import_match.field_base_import_match__write_uid
#: model:ir.model.fields,field_description:base_import_match.field_base_import_match_field__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: base_import_match
#: model:ir.model.fields,field_description:base_import_match.field_base_import_match__write_date
#: model:ir.model.fields,field_description:base_import_match.field_base_import_match_field__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: base_import_match
#: model:ir.model.fields,field_description:base_import_match.field_base_import_match_field__match_id
msgid "Match"
msgstr "Casar referencias"

#. module: base_import_match
#: model:ir.model.fields,field_description:base_import_match.field_base_import_match__model_id
#: model:ir.model.fields,field_description:base_import_match.field_base_import_match_field__model_id
#: model_terms:ir.ui.view,arch_db:base_import_match.match_search_view
msgid "Model"
msgstr "Modelo"

#. module: base_import_match
#: model:ir.model.fields,field_description:base_import_match.field_base_import_match__model_name
msgid "Model name"
msgstr "Nombre del modelo"

#. module: base_import_match
#: model:ir.model.fields,field_description:base_import_match.field_base_import_match__name
msgid "Name"
msgstr "Nombre"

#. module: base_import_match
#: model:ir.model.fields,field_description:base_import_match.field_base_import_match__sequence
msgid "Sequence"
msgstr "Secuencia"

#~ msgid "base"
#~ msgstr "base de datos"
