# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* rma
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: rma
#. odoo-python
#: code:addons/rma/models/rma_team.py:0
#, python-format
msgid "%s (copy)"
msgstr ""

#. module: rma
#: model:ir.actions.report,print_report_name:rma.report_rma_action
msgid "(object._get_report_base_filename())"
msgstr ""

#. module: rma
#. odoo-python
#: code:addons/rma/models/rma.py:0
#, python-format
msgid ""
"<b>E-mail subject:</b> %(subject)s<br/><br/><b>E-mail body:</b><br/>%(body)s"
msgstr ""

#. module: rma
#: model:mail.template,body_html:rma.mail_template_rma_notification
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                    Dear\n"
"                    <t t-out=\"object.partner_id.name\"></t>\n"
"                    <t t-if=\"object.partner_id.parent_id\">\n"
"                        <t t-out=\"object.partner_id.parent_id.name\"></t>\n"
"                    </t>\n"
"                    <br>\n"
"                    <br>\n"
"                    Here is the RMA\n"
"                    <strong>\n"
"                        <t t-out=\"object.name\"></t>\n"
"                    </strong>\n"
"                    from\n"
"                    <t t-out=\"object.company_id.name\"></t>\n"
"                    .\n"
"                    <br>\n"
"                    <br>\n"
"                    Do not hesitate to contact us if you have any question.\n"
"                </p>\n"
"            </div>\n"
"        "
msgstr ""

#. module: rma
#: model:mail.template,body_html:rma.mail_template_rma_receipt_notification
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                    Dear\n"
"                    <t t-out=\"object.partner_id.name\"></t>\n"
"                    <t t-if=\"object.partner_id.parent_id\">\n"
"                        <t t-out=\"object.partner_id.parent_id.name\"></t>\n"
"                    </t>\n"
"                    <br>\n"
"                    <br>\n"
"                    The products for your RMA\n"
"                    <strong>\n"
"                        <t t-out=\"object.name\"></t>\n"
"                    </strong>\n"
"                    from\n"
"                    <t t-out=\"object.company_id.name\"></t>\n"
"                    have been received in our warehouse.\n"
"                    <br>\n"
"                    <br>\n"
"                    Do not hesitate to contact us if you have any question.\n"
"                </p>\n"
"            </div>\n"
"        "
msgstr ""

#. module: rma
#: model:mail.template,body_html:rma.mail_template_rma_draft_notification
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                    Dear\n"
"                    <t t-out=\"object.partner_id.name\"></t>\n"
"                    <t t-if=\"object.partner_id.parent_id\">\n"
"                        <t t-out=\"object.partner_id.parent_id.name\"></t>\n"
"                    </t>\n"
"                    <br>\n"
"                    <br>\n"
"                    You've succesfully placed your RMA\n"
"                    <strong>\n"
"                        <t t-out=\"object.name\"></t>\n"
"                    </strong>\n"
"                    on\n"
"                    <t t-out=\"object.company_id.name\"></t>\n"
"                    . Our team will check it and will validate it as soon as possible.\n"
"                    <br>\n"
"                    <br>\n"
"                    Do not hesitate to contact us if you have any question.\n"
"                </p>\n"
"            </div>\n"
"        "
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.portal_rma_page
msgid "<i class=\"fa fa-download\" role=\"img\" aria-label=\"Download\" title=\"Download\"/>"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.rma_operation_kanban
msgid "<i class=\"fa fa-ellipsis-v\" role=\"img\" aria-label=\"Manage\" title=\"Manage\"/>"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.portal_rma_page
msgid ""
"<i class=\"fa fa-fw fa-check\"/>\n"
"                                <b>Paid</b>"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.portal_rma_page
msgid ""
"<i class=\"fa fa-fw fa-clock-o\"/>\n"
"                                <b>Waiting Payment</b>"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.portal_rma_page
msgid ""
"<i class=\"fa fa-pencil-square-o mr-1\" role=\"img\" aria-label=\"Download\""
" title=\"Download\"/>"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.portal_rma_page
msgid ""
"<i class=\"fa fa-truck mr-1\" role=\"img\" aria-label=\"Download\" "
"title=\"Download\"/>"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.portal_rma_page
msgid ""
"<span class=\"badge badge-danger label-text-align\">\n"
"                                            <i class=\"fa fa-fw fa-times\"/>\n"
"                                            Cancelled\n"
"                                        </span>"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.portal_rma_page
msgid ""
"<span class=\"badge badge-danger label-text-align\">\n"
"                                    <i class=\"fa fa-fw fa-times\"/>\n"
"                                    Cancelled\n"
"                                </span>"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.portal_rma_page
msgid ""
"<span class=\"badge badge-info label-text-align\">\n"
"                                            <i class=\"fa fa-fw fa-clock-o\"/>\n"
"                                            Preparation\n"
"                                        </span>"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.portal_rma_page
msgid ""
"<span class=\"badge badge-info label-text-align\">\n"
"                                    <i class=\"fa fa-fw fa-clock-o\"/>\n"
"                                    Preparation\n"
"                                </span>"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.portal_rma_page
msgid ""
"<span class=\"badge badge-success label-text-align\">\n"
"                                            <i class=\"fa fa-fw fa-truck\"/>\n"
"                                            Shipped\n"
"                                        </span>"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.portal_rma_page
msgid ""
"<span class=\"badge badge-success label-text-align\">\n"
"                                    <i class=\"fa fa-fw fa-truck\"/>\n"
"                                    Shipped\n"
"                                </span>"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.portal_rma_page
msgid ""
"<span class=\"badge badge-warning label-text-align\">\n"
"                                            <i class=\"fa fa-fw fa-clock-o\"/>\n"
"                                            Partially Available\n"
"                                        </span>"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.portal_rma_page
msgid ""
"<span class=\"badge badge-warning label-text-align\">\n"
"                                    <i class=\"fa fa-fw fa-clock-o\"/>\n"
"                                    Partially Available\n"
"                                </span>"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.rma_operation_kanban
msgid "<span>Awaiting action</span>"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.rma_operation_kanban
msgid "<span>Draft</span>"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.rma_operation_kanban
msgid "<span>New</span>"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.rma_operation_kanban
msgid "<span>Processed</span>"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.portal_rma_page
msgid "<strong class=\"d-block mb-1\">Delivery</strong>"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.portal_rma_page
msgid "<strong class=\"d-block mb-1\">Reception</strong>"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.portal_rma_page
msgid "<strong class=\"d-block mb-1\">Refund</strong>"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.portal_rma_page
msgid "<strong>Customer:</strong>"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.report_rma_document
msgid "<strong>Date:</strong>"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.report_rma_document
msgid "<strong>Deadline:</strong>"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.portal_rma_page
msgid "<strong>Deadline</strong>"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.portal_rma_page
msgid "<strong>Delivered quantity</strong>"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.portal_rma_page
msgid "<strong>Origin delivery</strong>"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.report_rma_document
msgid "<strong>Origin:</strong>"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.portal_rma_page
msgid "<strong>Origin</strong>"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.portal_rma_page
msgid "<strong>Product</strong>"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.portal_rma_page
msgid "<strong>Quantity</strong>"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.portal_rma_page
msgid "<strong>RMA Date</strong>"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.report_rma_document
msgid "<strong>RMA Note:</strong>"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.portal_rma_page
#: model_terms:ir.ui.view,arch_db:rma.report_rma_document
msgid "<strong>Responsible:</strong>"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.report_rma_document
msgid "<strong>Shipping Address:</strong>"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.portal_rma_page
msgid "<strong>Shipping address:</strong>"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.report_rma_document
msgid "<strong>State:</strong>"
msgstr ""

#. module: rma
#: model:ir.model.fields,help:rma.field_rma_team__alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.rma_team_view_form
msgid "Accept Emails From"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__access_warning
msgid "Access warning"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__message_needaction
#: model:ir.model.fields,field_description:rma.field_rma_team__message_needaction
msgid "Action Needed"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma_finalization__active
#: model:ir.model.fields,field_description:rma.field_rma_operation__active
#: model:ir.model.fields,field_description:rma.field_rma_tag__active
#: model:ir.model.fields,field_description:rma.field_rma_team__active
#: model_terms:ir.ui.view,arch_db:rma.rma_finalization_view_search
#: model_terms:ir.ui.view,arch_db:rma.rma_tag_view_search
msgid "Active"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__activity_ids
msgid "Activities"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__activity_state
msgid "Activity State"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__activity_type_icon
msgid "Activity Type Icon"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma_team__alias_id
msgid "Alias"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma_team__alias_contact
msgid "Alias Contact Security"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma_team__alias_name
msgid "Alias Name"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma_team__alias_domain
msgid "Alias domain"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma_team__alias_model_id
msgid "Aliased Model"
msgstr ""

#. module: rma
#: model:res.groups,name:rma.group_rma_manual_finalization
msgid "Allow RMA manual finalization"
msgstr ""

#. module: rma
#: model:ir.model.fields,help:rma.field_res_config_settings__group_rma_manual_finalization
msgid "Allow to finish an RMA without returning back a product or refunding"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.rma_finalization_view_search
#: model_terms:ir.ui.view,arch_db:rma.rma_tag_view_search
#: model_terms:ir.ui.view,arch_db:rma.view_rma_finalization_form
msgid "Archived"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.rma_view_form
msgid "Are you sure you want to cancel this RMA"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__message_attachment_count
#: model:ir.model.fields,field_description:rma.field_rma_team__message_attachment_count
msgid "Attachment Count"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma_operation__auto_confirm_reception
msgid "Auto Confirm Reception"
msgstr ""

#. module: rma
#: model:ir.model.fields.selection,name:rma.selection__rma_operation__action_create_delivery__automatic_after_receipt
#: model:ir.model.fields.selection,name:rma.selection__rma_operation__action_create_refund__automatic_after_receipt
msgid "Automatically After Receipt"
msgstr ""

#. module: rma
#: model:ir.model.fields.selection,name:rma.selection__rma_operation__action_create_delivery__automatic_on_confirm
#: model:ir.model.fields.selection,name:rma.selection__rma_operation__action_create_receipt__automatic_on_confirm
#: model:ir.model.fields.selection,name:rma.selection__rma_operation__action_create_refund__automatic_on_confirm
msgid "Automatically on Confirm"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.rma_team_view_form
msgid "Avatar"
msgstr ""

#. module: rma
#. odoo-python
#: code:addons/rma/models/rma_operation.py:0
#: model_terms:ir.ui.view,arch_db:rma.rma_view_search
#, python-format
msgid "Awaiting Action"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__can_be_finished
msgid "Can Be Finished"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__can_be_locked
msgid "Can Be Locked"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__can_be_refunded
msgid "Can Be Refunded"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__can_be_replaced
msgid "Can Be Replaced"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__can_be_returned
msgid "Can Be Returned"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__can_be_split
msgid "Can Be Split"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.rma_finalization_wizard_view_form
#: model_terms:ir.ui.view,arch_db:rma.rma_redelivery_wizard_view_form
#: model_terms:ir.ui.view,arch_db:rma.rma_split_wizard_view_form2
#: model_terms:ir.ui.view,arch_db:rma.rma_view_form
msgid "Cancel"
msgstr ""

#. module: rma
#: model:ir.model.fields.selection,name:rma.selection__rma__state__cancelled
msgid "Canceled"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma_delivery_wizard__uom_category_id
msgid "Category"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__uom_category_id
msgid "Category UoM"
msgstr ""

#. module: rma
#: model_terms:ir.actions.act_window,help:rma.rma_action
#: model_terms:ir.actions.act_window,help:rma.rma_team_action
msgid "Click to add a new RMA."
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.rma_view_search
msgid "Closed"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma_operation__color
msgid "Color"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma_tag__color
msgid "Color Index"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__commercial_partner_id
msgid "Commercial Entity"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.portal_rma_page
msgid "Communication"
msgstr ""

#. module: rma
#: model:ir.model,name:rma.model_res_company
msgid "Companies"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__company_id
#: model:ir.model.fields,field_description:rma.field_rma_finalization__company_id
#: model:ir.model.fields,field_description:rma.field_rma_team__company_id
msgid "Company"
msgstr ""

#. module: rma
#: model:ir.model,name:rma.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: rma
#: model:ir.ui.menu,name:rma.rma_configuration_menu
#: model_terms:ir.ui.view,arch_db:rma.rma_operation_kanban
msgid "Configuration"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.rma_view_form
msgid "Confirm"
msgstr ""

#. module: rma
#: model:ir.model.fields.selection,name:rma.selection__rma__state__confirmed
#: model_terms:ir.ui.view,arch_db:rma.rma_view_search
msgid "Confirmed"
msgstr ""

#. module: rma
#: model:ir.model,name:rma.model_res_partner
#: model_terms:ir.ui.view,arch_db:rma.portal_rma_page
msgid "Contact"
msgstr ""

#. module: rma
#: model:ir.model.fields,help:rma.field_rma__uom_category_id
#: model:ir.model.fields,help:rma.field_rma_delivery_wizard__uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma_operation__count_rma_awaiting_action
msgid "Count Rma Awaiting Action"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma_operation__count_rma_draft
msgid "Count Rma Draft"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma_operation__count_rma_processed
msgid "Count Rma Processed"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_stock_return_picking__create_rma
msgid "Create RMAs"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma_operation__action_create_receipt
#: model_terms:ir.ui.view,arch_db:rma.rma_view_form
msgid "Create Receipt"
msgstr ""

#. module: rma
#: model_terms:ir.actions.act_window,help:rma.action_rma_finalization
msgid "Create a new RMA finalization"
msgstr ""

#. module: rma
#: model_terms:ir.actions.act_window,help:rma.rma_dashboard_action
msgid "Create a new RMA operation"
msgstr ""

#. module: rma
#: model_terms:ir.actions.act_window,help:rma.action_rma_tag
msgid "Create a new RMA tag"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__create_uid
#: model:ir.model.fields,field_description:rma.field_rma_delivery_wizard__create_uid
#: model:ir.model.fields,field_description:rma.field_rma_finalization__create_uid
#: model:ir.model.fields,field_description:rma.field_rma_finalization_wizard__create_uid
#: model:ir.model.fields,field_description:rma.field_rma_operation__create_uid
#: model:ir.model.fields,field_description:rma.field_rma_split_wizard__create_uid
#: model:ir.model.fields,field_description:rma.field_rma_tag__create_uid
#: model:ir.model.fields,field_description:rma.field_rma_team__create_uid
msgid "Created by"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__create_date
#: model:ir.model.fields,field_description:rma.field_rma_delivery_wizard__create_date
#: model:ir.model.fields,field_description:rma.field_rma_finalization__create_date
#: model:ir.model.fields,field_description:rma.field_rma_finalization_wizard__create_date
#: model:ir.model.fields,field_description:rma.field_rma_operation__create_date
#: model:ir.model.fields,field_description:rma.field_rma_split_wizard__create_date
#: model:ir.model.fields,field_description:rma.field_rma_tag__create_date
#: model:ir.model.fields,field_description:rma.field_rma_team__create_date
msgid "Created on"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma_team__alias_bounced_content
msgid "Custom Bounced Message"
msgstr ""

#. module: rma
#. odoo-python
#: code:addons/rma/models/rma.py:0
#: model:ir.model.fields,field_description:rma.field_rma__partner_id
#, python-format
msgid "Customer"
msgstr ""

#. module: rma
#: model:ir.model.fields,help:rma.field_rma__access_url
msgid "Customer Portal URL"
msgstr ""

#. module: rma
#. odoo-python
#: code:addons/rma/controllers/main.py:0
#: model:ir.model.fields,field_description:rma.field_rma__date
#: model_terms:ir.ui.view,arch_db:rma.portal_my_rmas
#: model_terms:ir.ui.view,arch_db:rma.rma_view_search
#, python-format
msgid "Date"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.portal_rma_page
msgid "Date:"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__deadline
#: model_terms:ir.ui.view,arch_db:rma.rma_view_search
msgid "Deadline"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma_team__alias_defaults
msgid "Default Values"
msgstr ""

#. module: rma
#: model:ir.model.fields,help:rma.field_rma_operation__action_create_delivery
msgid "Define how the delivery action should be handled."
msgstr ""

#. module: rma
#: model:ir.model.fields,help:rma.field_rma_operation__action_create_receipt
msgid "Define how the receipt action should be handled."
msgstr ""

#. module: rma
#: model:ir.model.fields,help:rma.field_rma_operation__action_create_refund
msgid "Define how the refund action should be handled."
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.rma_redelivery_wizard_view_form
msgid "Deliver"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__delivered_qty
msgid "Delivered Qty"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__delivered_qty_done
msgid "Delivered Qty Done"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.report_rma_document
msgid "Delivered Quantity"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.rma_view_form
msgid "Delivery"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma_operation__action_create_delivery
msgid "Delivery Action"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__delivery_picking_count
msgid "Delivery count"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__delivery_move_ids
msgid "Delivery reservation"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__description
#: model_terms:ir.ui.view,arch_db:rma.portal_rma_page
msgid "Description"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__different_return_product
#: model:ir.model.fields,field_description:rma.field_rma_operation__different_return_product
#: model:ir.model.fields,field_description:rma.field_stock_return_picking_line__different_return_product
msgid "Different Return Product"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__display_name
#: model:ir.model.fields,field_description:rma.field_rma_delivery_wizard__display_name
#: model:ir.model.fields,field_description:rma.field_rma_finalization__display_name
#: model:ir.model.fields,field_description:rma.field_rma_finalization_wizard__display_name
#: model:ir.model.fields,field_description:rma.field_rma_operation__display_name
#: model:ir.model.fields,field_description:rma.field_rma_split_wizard__display_name
#: model:ir.model.fields,field_description:rma.field_rma_tag__display_name
#: model:ir.model.fields,field_description:rma.field_rma_team__display_name
msgid "Display Name"
msgstr ""

#. module: rma
#. odoo-python
#: code:addons/rma/models/rma_operation.py:0
#: model:ir.model.fields.selection,name:rma.selection__rma__state__draft
#: model_terms:ir.ui.view,arch_db:rma.rma_view_search
#, python-format
msgid "Draft"
msgstr ""

#. module: rma
#: model:mail.message.subtype,name:rma.mt_rma_draft
#: model:mail.message.subtype,name:rma.mt_rma_team_rma_draft
msgid "Draft RMA"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.rma_team_view_form
msgid "Email"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.rma_team_view_form
msgid "Email Alias"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.res_config_settings_view_form
msgid "Email Template"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_res_company__rma_mail_confirmation_template_id
#: model:ir.model.fields,field_description:rma.field_res_config_settings__rma_mail_confirmation_template_id
msgid "Email Template confirmation for RMA"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_res_company__rma_mail_draft_confirmation_template_id
#: model:ir.model.fields,field_description:rma.field_res_config_settings__rma_mail_draft_confirmation_template_id
msgid "Email Template draft notification for RMA"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_res_company__rma_mail_receipt_confirmation_template_id
#: model:ir.model.fields,field_description:rma.field_res_config_settings__rma_mail_receipt_confirmation_template_id
msgid "Email Template receipt confirmation for RMA"
msgstr ""

#. module: rma
#: model:ir.model.fields,help:rma.field_res_company__rma_mail_confirmation_template_id
#: model:ir.model.fields,help:rma.field_res_config_settings__rma_mail_confirmation_template_id
msgid "Email sent to the customer once the RMA is confirmed."
msgstr ""

#. module: rma
#: model:ir.model.fields,help:rma.field_res_company__rma_mail_receipt_confirmation_template_id
#: model:ir.model.fields,help:rma.field_res_config_settings__rma_mail_receipt_confirmation_template_id
msgid "Email sent to the customer once the RMA products are received."
msgstr ""

#. module: rma
#: model:ir.model.fields,help:rma.field_res_company__rma_mail_draft_confirmation_template_id
#: model:ir.model.fields,help:rma.field_res_config_settings__rma_mail_draft_confirmation_template_id
msgid "Email sent to the customer when they place an RMA from the portal"
msgstr ""

#. module: rma
#: model:ir.model.fields,help:rma.field_rma_operation__auto_confirm_reception
msgid ""
"Enable this option to automatically confirm the reception when the RMA is "
"confirmed."
msgstr ""

#. module: rma
#. odoo-python
#: code:addons/rma/wizard/rma_split.py:0
#, python-format
msgid "Extracted RMA"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__origin_split_rma_id
msgid "Extracted from"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__finalization_id
msgid "Finalization Reason"
msgstr ""

#. module: rma
#: model:ir.model.constraint,message:rma.constraint_rma_finalization_name_company_uniq
msgid "Finalization name already exists !"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.rma_finalization_form
msgid "Finish"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.rma_finalization_wizard_view_form
msgid "Finish RMA"
msgstr ""

#. module: rma
#: model:ir.actions.act_window,name:rma.rma_finalization_wizard_action
msgid "Finish RMA Manualy"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_res_config_settings__group_rma_manual_finalization
msgid "Finish RMA manually choosing a reason"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.res_config_settings_view_form
msgid "Finish RMAs manually"
msgstr ""

#. module: rma
#: model:ir.model.fields.selection,name:rma.selection__rma__state__finished
msgid "Finished"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__message_follower_ids
#: model:ir.model.fields,field_description:rma.field_rma_team__message_follower_ids
msgid "Followers"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__message_partner_ids
#: model:ir.model.fields,field_description:rma.field_rma_team__message_partner_ids
msgid "Followers (Partners)"
msgstr ""

#. module: rma
#: model:ir.model.fields,help:rma.field_rma__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.rma_view_search
msgid "Group By"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_res_company__rma_return_grouping
#: model:ir.model.fields,field_description:rma.field_res_config_settings__rma_return_grouping
#: model:ir.model.fields,field_description:rma.field_rma_delivery_wizard__rma_return_grouping
msgid "Group RMA returns by customer address and warehouse"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.res_config_settings_view_form
msgid "Group RMA returns by customer and warehouse."
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__has_message
#: model:ir.model.fields,field_description:rma.field_rma_team__has_message
msgid "Has Message"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__id
#: model:ir.model.fields,field_description:rma.field_rma_delivery_wizard__id
#: model:ir.model.fields,field_description:rma.field_rma_finalization__id
#: model:ir.model.fields,field_description:rma.field_rma_finalization_wizard__id
#: model:ir.model.fields,field_description:rma.field_rma_operation__id
#: model:ir.model.fields,field_description:rma.field_rma_split_wizard__id
#: model:ir.model.fields,field_description:rma.field_rma_tag__id
#: model:ir.model.fields,field_description:rma.field_rma_team__id
msgid "ID"
msgstr ""

#. module: rma
#: model:ir.model.fields,help:rma.field_rma_team__alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__activity_exception_icon
msgid "Icon"
msgstr ""

#. module: rma
#: model:ir.model.fields,help:rma.field_rma__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr ""

#. module: rma
#: model:ir.model.fields,help:rma.field_rma__different_return_product
#: model:ir.model.fields,help:rma.field_rma_operation__different_return_product
#: model:ir.model.fields,help:rma.field_stock_return_picking_line__different_return_product
msgid ""
"If checked, allows the return of a product different from the one originally"
" ordered. Used if the delivery is created automatically"
msgstr ""

#. module: rma
#: model:ir.model.fields,help:rma.field_rma__message_needaction
#: model:ir.model.fields,help:rma.field_rma_team__message_needaction
msgid "If checked, new messages require your attention."
msgstr ""

#. module: rma
#: model:ir.model.fields,help:rma.field_rma__message_has_error
#: model:ir.model.fields,help:rma.field_rma_team__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: rma
#: model:ir.model.fields,help:rma.field_rma_team__alias_bounced_content
msgid ""
"If set, this content will automatically be sent out to unauthorized users "
"instead of the default message."
msgstr ""

#. module: rma
#: model:ir.model.fields,help:rma.field_rma_team__active
msgid ""
"If the active field is set to false, it will allow you to hide the RMA Team "
"without removing it."
msgstr ""

#. module: rma
#. odoo-python
#: code:addons/rma/models/rma.py:0
#, python-format
msgid "Incoming e-mail"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__partner_invoice_id
msgid "Invoice Address"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.report_rma_document
msgid "Invoicing Address:"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.report_rma_document
msgid "Invoicing and Shipping Address:"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__message_is_follower
#: model:ir.model.fields,field_description:rma.field_rma_team__message_is_follower
msgid "Is Follower"
msgstr ""

#. module: rma
#: model:ir.model,name:rma.model_account_move
msgid "Journal Entry"
msgstr ""

#. module: rma
#: model:ir.model,name:rma.model_account_move_line
msgid "Journal Item"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma____last_update
#: model:ir.model.fields,field_description:rma.field_rma_delivery_wizard____last_update
#: model:ir.model.fields,field_description:rma.field_rma_finalization____last_update
#: model:ir.model.fields,field_description:rma.field_rma_finalization_wizard____last_update
#: model:ir.model.fields,field_description:rma.field_rma_operation____last_update
#: model:ir.model.fields,field_description:rma.field_rma_split_wizard____last_update
#: model:ir.model.fields,field_description:rma.field_rma_tag____last_update
#: model:ir.model.fields,field_description:rma.field_rma_team____last_update
msgid "Last Modified on"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__write_uid
#: model:ir.model.fields,field_description:rma.field_rma_delivery_wizard__write_uid
#: model:ir.model.fields,field_description:rma.field_rma_finalization__write_uid
#: model:ir.model.fields,field_description:rma.field_rma_finalization_wizard__write_uid
#: model:ir.model.fields,field_description:rma.field_rma_operation__write_uid
#: model:ir.model.fields,field_description:rma.field_rma_split_wizard__write_uid
#: model:ir.model.fields,field_description:rma.field_rma_tag__write_uid
#: model:ir.model.fields,field_description:rma.field_rma_team__write_uid
msgid "Last Updated by"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__write_date
#: model:ir.model.fields,field_description:rma.field_rma_delivery_wizard__write_date
#: model:ir.model.fields,field_description:rma.field_rma_finalization__write_date
#: model:ir.model.fields,field_description:rma.field_rma_finalization_wizard__write_date
#: model:ir.model.fields,field_description:rma.field_rma_operation__write_date
#: model:ir.model.fields,field_description:rma.field_rma_split_wizard__write_date
#: model:ir.model.fields,field_description:rma.field_rma_tag__write_date
#: model:ir.model.fields,field_description:rma.field_rma_team__write_date
msgid "Last Updated on"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.rma_view_search
msgid "Late RMAs"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__location_id
msgid "Location"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.rma_view_form
msgid "Lock"
msgstr ""

#. module: rma
#: model:ir.model.fields.selection,name:rma.selection__rma__state__locked
msgid "Locked"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__message_main_attachment_id
#: model:ir.model.fields,field_description:rma.field_rma_team__message_main_attachment_id
msgid "Main Attachment"
msgstr ""

#. module: rma
#: model_terms:ir.actions.act_window,help:rma.action_rma_finalization
msgid ""
"Manage RMA finalization reasons to better classify them for tracking and "
"analysis purposes."
msgstr ""

#. module: rma
#: model_terms:ir.actions.act_window,help:rma.action_rma_tag
msgid ""
"Manage RMA tags to better classify them for tracking and analysis purposes."
msgstr ""

#. module: rma
#: model:ir.module.category,description:rma.rma_module_category
msgid "Manage Return Merchandise Authorizations (RMAs)."
msgstr ""

#. module: rma
#: model:res.groups,name:rma.rma_group_manager
msgid "Manager"
msgstr ""

#. module: rma
#: model:ir.model.fields.selection,name:rma.selection__rma_operation__action_create_delivery__manual_after_receipt
#: model:ir.model.fields.selection,name:rma.selection__rma_operation__action_create_refund__manual_after_receipt
msgid "Manually After Receipt"
msgstr ""

#. module: rma
#: model:ir.model.fields.selection,name:rma.selection__rma_operation__action_create_delivery__manual_on_confirm
#: model:ir.model.fields.selection,name:rma.selection__rma_operation__action_create_receipt__manual_on_confirm
#: model:ir.model.fields.selection,name:rma.selection__rma_operation__action_create_refund__manual_on_confirm
msgid "Manually on Confirm"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__message_has_error
#: model:ir.model.fields,field_description:rma.field_rma_team__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__message_ids
#: model:ir.model.fields,field_description:rma.field_rma_team__message_ids
msgid "Messages"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr ""

#. module: rma
#. odoo-python
#: code:addons/rma/controllers/main.py:0
#: model:ir.model.fields,field_description:rma.field_rma__name
#: model:ir.model.fields,field_description:rma.field_rma_operation__name
#: model:ir.model.fields,field_description:rma.field_rma_team__name
#, python-format
msgid "Name"
msgstr ""

#. module: rma
#. odoo-python
#: code:addons/rma/models/rma.py:0 code:addons/rma/models/rma.py:0
#: code:addons/rma/models/rma.py:0 code:addons/rma/models/rma.py:0
#, python-format
msgid "New"
msgstr ""

#. module: rma
#: model:ir.actions.act_window,name:rma.action_rma_form
msgid "New RMA"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__activity_date_deadline
msgid "Next Activity Deadline"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__activity_summary
msgid "Next Activity Summary"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__activity_type_id
msgid "Next Activity Type"
msgstr ""

#. module: rma
#. odoo-python
#: code:addons/rma/models/rma.py:0
#, python-format
msgid "None of the selected RMAs can perform a replacement."
msgstr ""

#. module: rma
#. odoo-python
#: code:addons/rma/models/rma.py:0
#, python-format
msgid "None of the selected RMAs can perform a return."
msgstr ""

#. module: rma
#: model:ir.model.fields.selection,name:rma.selection__rma__priority__0
msgid "Normal"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__message_needaction_counter
#: model:ir.model.fields,field_description:rma.field_rma_team__message_needaction_counter
msgid "Number of Actions"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__message_has_error_counter
#: model:ir.model.fields,field_description:rma.field_rma_team__message_has_error_counter
msgid "Number of errors"
msgstr ""

#. module: rma
#: model:ir.model.fields,help:rma.field_rma__message_needaction_counter
#: model:ir.model.fields,help:rma.field_rma_team__message_needaction_counter
msgid "Number of messages requiring action"
msgstr ""

#. module: rma
#: model:ir.model.fields,help:rma.field_rma__message_has_error_counter
#: model:ir.model.fields,help:rma.field_rma_team__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_stock_return_picking_line__rma_operation_id
msgid "Operation"
msgstr ""

#. module: rma
#: model:ir.actions.act_window,name:rma.rma_operation_act_window
#: model:ir.ui.menu,name:rma.rma_operation_menu
msgid "Operations"
msgstr ""

#. module: rma
#: model:ir.model.fields,help:rma.field_rma_team__alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr ""

#. module: rma
#: model:ir.ui.menu,name:rma.rma_orders_menu
msgid "Orders"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__picking_id
msgid "Origin Delivery"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.report_rma_document
msgid "Origin delivery"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__move_id
msgid "Origin move"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.rma_view_form
msgid "Other Information"
msgstr ""

#. module: rma
#: model:ir.ui.menu,name:rma.rma_dashboard_menu
msgid "Overview"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma_team__alias_user_id
msgid "Owner"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma_team__alias_parent_model_id
msgid "Parent Model"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma_team__alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr ""

#. module: rma
#: model:ir.model.fields,help:rma.field_rma_team__alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.rma_view_search
msgid "Partner"
msgstr ""

#. module: rma
#: model:ir.model.fields,help:rma.field_rma_team__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__access_url
msgid "Portal Access URL"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.rma_view_form
msgid "Preview"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__priority
msgid "Priority"
msgstr ""

#. module: rma
#. odoo-python
#: code:addons/rma/models/rma_operation.py:0
#: model_terms:ir.ui.view,arch_db:rma.rma_view_search
#, python-format
msgid "Processed"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__procurement_group_id
msgid "Procurement group"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__product_id
#: model_terms:ir.ui.view,arch_db:rma.portal_my_rmas
#: model_terms:ir.ui.view,arch_db:rma.report_rma_document
msgid "Product"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma_delivery_wizard__product_uom_qty
msgid "Product qty"
msgstr ""

#. module: rma
#: model:ir.model.fields,help:rma.field_rma__return_product_id
#: model:ir.model.fields,help:rma.field_stock_return_picking_line__return_product_id
msgid ""
"Product to be returned if it's different from the originally delivered item."
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma_tag__is_public
msgid "Public Tag"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__product_uom_qty
#: model_terms:ir.ui.view,arch_db:rma.portal_my_rmas
#: model_terms:ir.ui.view,arch_db:rma.report_rma_document
msgid "Quantity"
msgstr ""

#. module: rma
#. odoo-python
#: code:addons/rma/wizard/rma_delivery.py:0
#: model:ir.model.constraint,message:rma.constraint_rma_split_wizard_check_product_uom_qty_positive
#, python-format
msgid "Quantity must be greater than 0."
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma_split_wizard__product_uom_qty
msgid "Quantity to extract"
msgstr ""

#. module: rma
#. odoo-python
#: code:addons/rma/models/rma.py:0
#, python-format
msgid ""
"Quantity to extract cannot be greater than remaining delivery quantity "
"(%(remaining_qty)s %(product_uom)s)"
msgstr ""

#. module: rma
#: model:ir.model.fields,help:rma.field_rma_split_wizard__product_uom_qty
msgid "Quantity to extract to a new RMA."
msgstr ""

#. module: rma
#: model:ir.actions.act_window,name:rma.rma_action
#: model:ir.model,name:rma.model_rma
#: model:ir.model.fields,field_description:rma.field_account_move_line__rma_id
#: model:ir.model.fields,field_description:rma.field_rma_split_wizard__rma_id
#: model:ir.model.fields,field_description:rma.field_stock_warehouse__rma
#: model:ir.module.category,name:rma.rma_module_category
#: model:ir.ui.menu,name:rma.rma_menu
#: model_terms:ir.ui.view,arch_db:rma.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:rma.rma_operation_kanban
#: model_terms:ir.ui.view,arch_db:rma.view_partner_form
#: model_terms:ir.ui.view,arch_db:rma.view_picking_form
msgid "RMA"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.portal_my_rmas
#: model_terms:ir.ui.view,arch_db:rma.report_rma_document
msgid "RMA #"
msgstr ""

#. module: rma
#. odoo-python
#: code:addons/rma/models/res_company.py:0
#, python-format
msgid "RMA Code"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.res_config_settings_view_form
msgid "RMA Confirmation Email"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.rma_view_search
msgid "RMA Date"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.rma_view_search
msgid "RMA Deadline"
msgstr ""

#. module: rma
#. odoo-python
#: code:addons/rma/models/stock_warehouse.py:0
#, python-format
msgid "RMA Delivery Orders"
msgstr ""

#. module: rma
#: model:ir.model,name:rma.model_rma_delivery_wizard
msgid "RMA Delivery Wizard"
msgstr ""

#. module: rma
#: model:mail.template,name:rma.mail_template_rma_draft_notification
msgid "RMA Draft Notification"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.view_rma_finalization_form
msgid "RMA Finalization"
msgstr ""

#. module: rma
#: model:ir.model,name:rma.model_rma_finalization
msgid "RMA Finalization Reason"
msgstr ""

#. module: rma
#: model:ir.actions.act_window,name:rma.action_rma_finalization
#: model:ir.ui.menu,name:rma.rma_configuration_rma_finalization_menu
#: model_terms:ir.ui.view,arch_db:rma.rma_finalization_view_search
msgid "RMA Finalization Reasons"
msgstr ""

#. module: rma
#: model:ir.model,name:rma.model_rma_finalization_wizard
msgid "RMA Finalization Wizard"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_stock_warehouse__rma_in_type_id
msgid "RMA In Type"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_stock_warehouse__rma_loc_id
msgid "RMA Location"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.res_config_settings_view_form
msgid "RMA Manual Finalization"
msgstr ""

#. module: rma
#: model:mail.message.subtype,name:rma.mt_rma_notification
#: model:mail.message.subtype,name:rma.mt_rma_team_rma_notification
#: model:mail.template,name:rma.mail_template_rma_notification
msgid "RMA Notification"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.portal_rma_page
msgid "RMA Order -"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.portal_my_home_menu_rma
#: model_terms:ir.ui.view,arch_db:rma.portal_my_home_rma
#: model_terms:ir.ui.view,arch_db:rma.portal_my_rmas
msgid "RMA Orders"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_stock_warehouse__rma_out_type_id
msgid "RMA Out Type"
msgstr ""

#. module: rma
#: model:ir.actions.act_window,name:rma.rma_dashboard_action
msgid "RMA Overview"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.res_config_settings_view_form
msgid "RMA Receipt Confirmation Email"
msgstr ""

#. module: rma
#: model:mail.template,name:rma.mail_template_rma_receipt_notification
msgid "RMA Receipt Notification"
msgstr ""

#. module: rma
#. odoo-python
#: code:addons/rma/models/stock_warehouse.py:0
#, python-format
msgid "RMA Receipts"
msgstr ""

#. module: rma
#: model:ir.actions.report,name:rma.report_rma_action
msgid "RMA Report"
msgstr ""

#. module: rma
#: model:ir.model,name:rma.model_rma_split_wizard
msgid "RMA Split Wizard"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.view_rma_tag_form
msgid "RMA Tag"
msgstr ""

#. module: rma
#: model:ir.actions.act_window,name:rma.action_rma_tag
#: model:ir.model,name:rma.model_rma_tag
#: model:ir.ui.menu,name:rma.rma_configuration_rma_tag_menu
#: model_terms:ir.ui.view,arch_db:rma.rma_tag_view_search
msgid "RMA Tags"
msgstr ""

#. module: rma
#: model:ir.model,name:rma.model_rma_team
#: model:ir.model.fields,field_description:rma.field_res_users__rma_team_id
#: model:ir.ui.menu,name:rma.rma_configuration_rma_team_menu
#: model_terms:ir.ui.view,arch_db:rma.rma_team_view_form
msgid "RMA Team"
msgstr ""

#. module: rma
#: model:ir.model.fields,help:rma.field_res_users__rma_team_id
msgid "RMA Team the user is member of."
msgstr ""

#. module: rma
#: model:mail.message.subtype,description:rma.mt_rma_notification
msgid "RMA automatic customer notifications"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_res_partner__rma_count
#: model:ir.model.fields,field_description:rma.field_res_users__rma_count
#: model:ir.model.fields,field_description:rma.field_stock_picking__rma_count
msgid "RMA count"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.res_config_settings_view_form
msgid "RMA draft notification Email"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_stock_warehouse__rma_in_route_id
msgid "RMA in Route"
msgstr ""

#. module: rma
#: model:mail.message.subtype,description:rma.mt_rma_draft
msgid "RMA in draft state"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_stock_warehouse__rma_out_replace_route_id
msgid "RMA out Replace Route"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_stock_warehouse__rma_out_route_id
msgid "RMA out Route"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_stock_move__rma_receiver_ids
msgid "RMA receivers"
msgstr ""

#. module: rma
#: model:ir.model.fields,help:rma.field_stock_warehouse__rma
msgid "RMA related products can be stored in this warehouse."
msgstr ""

#. module: rma
#: model:ir.model,name:rma.model_rma_operation
msgid "RMA requested operation"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_stock_move__rma_id
msgid "RMA return"
msgstr ""

#. module: rma
#: model:ir.actions.act_window,name:rma.rma_team_action
#: model:ir.model.fields,field_description:rma.field_rma__team_id
msgid "RMA team"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_res_partner__rma_ids
#: model:ir.model.fields,field_description:rma.field_res_users__rma_ids
#: model:ir.model.fields,field_description:rma.field_stock_move__rma_ids
msgid "RMAs"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.rma_view_search
msgid "RMAs which deadline has passed"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.rma_view_search
msgid "RMAs yet to be fully processed"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma_finalization_wizard__finalization_id
msgid "Reason"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma_finalization__name
msgid "Reason Name"
msgstr ""

#. module: rma
#. odoo-python
#: code:addons/rma/models/rma.py:0
#: model_terms:ir.ui.view,arch_db:rma.rma_view_form
#, python-format
msgid "Receipt"
msgstr ""

#. module: rma
#: model:ir.model.fields.selection,name:rma.selection__rma__state__received
#: model_terms:ir.ui.view,arch_db:rma.rma_view_search
msgid "Received"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__reception_move_id
msgid "Reception move"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma_team__alias_force_thread_id
msgid "Record Thread ID"
msgstr ""

#. module: rma
#: model:ir.model.fields,help:rma.field_rma__origin
msgid "Reference of the document that generated this RMA."
msgstr ""

#. module: rma
#. odoo-python
#: code:addons/rma/models/rma.py:0
#: model:ir.model.fields,field_description:rma.field_rma__refund_id
#: model:rma.operation,name:rma.rma_operation_refund
#: model_terms:ir.ui.view,arch_db:rma.rma_view_form
#, python-format
msgid "Refund"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma_operation__action_create_refund
msgid "Refund Action"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__refund_line_id
msgid "Refund Line"
msgstr ""

#. module: rma
#: model:ir.model.fields,help:rma.field_rma__partner_invoice_id
msgid "Refund address for current RMA."
msgstr ""

#. module: rma
#: model:ir.model.fields.selection,name:rma.selection__rma__state__refunded
msgid "Refunded"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__remaining_qty
msgid "Remaining delivered qty"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__remaining_qty_to_done
msgid "Remaining delivered qty to done"
msgstr ""

#. module: rma
#: model:rma.operation,name:rma.rma_operation_return
msgid "Repair"
msgstr ""

#. module: rma
#: model:ir.model.fields.selection,name:rma.selection__rma_delivery_wizard__type__replace
#: model:rma.operation,name:rma.rma_operation_replace
#: model_terms:ir.ui.view,arch_db:rma.rma_view_form
msgid "Replace"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma_delivery_wizard__product_id
msgid "Replace Product"
msgstr ""

#. module: rma
#: model:ir.model.fields.selection,name:rma.selection__rma__state__replaced
msgid "Replaced"
msgstr ""

#. module: rma
#. odoo-python
#: code:addons/rma/models/rma.py:0
#, python-format
msgid ""
"Replacement: Move <a href=\"#\" data-oe-model=\"stock.move\" data-oe-"
"id=\"%(move_id)d\">%(move_name)s</a> (Picking <a href=\"#\" data-oe-"
"model=\"stock.picking\" data-oe-id=\"%(picking_id)d\"> %(picking_name)s</a>)"
" has been created."
msgstr ""

#. module: rma
#. odoo-python
#: code:addons/rma/models/rma.py:0
#, python-format
msgid ""
"Replacement:<br/>Product <a href=\"#\" data-oe-model=\"product.product\" "
"data-oe-id=\"%(id)d\">%(name)s</a><br/>Quantity %(qty)s %(uom)s<br/>This "
"replacement did not create a new move, but one of the previously created "
"moves was updated with this data."
msgstr ""

#. module: rma
#: model:ir.ui.menu,name:rma.rma_reporting_menu
msgid "Reporting"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__operation_id
#: model:ir.model.fields,field_description:rma.field_stock_return_picking__rma_operation_id
#: model_terms:ir.ui.view,arch_db:rma.report_rma_document
msgid "Requested operation"
msgstr ""

#. module: rma
#. odoo-python
#: code:addons/rma/models/rma.py:0
#, python-format
msgid "Required field(s):%s"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__user_id
#: model_terms:ir.ui.view,arch_db:rma.rma_view_search
msgid "Responsible"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__activity_user_id
msgid "Responsible User"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_stock_return_picking__location_id
msgid "Return Location"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.res_config_settings_view_form
msgid "Return Merchandise Authorization Management"
msgstr ""

#. module: rma
#: model:ir.model,name:rma.model_stock_return_picking
msgid "Return Picking"
msgstr ""

#. module: rma
#: model:ir.model,name:rma.model_stock_return_picking_line
msgid "Return Picking Line"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__return_product_id
#: model:ir.model.fields,field_description:rma.field_stock_return_picking_line__return_product_id
msgid "Return Product"
msgstr ""

#. module: rma
#: model:ir.actions.act_window,name:rma.rma_delivery_wizard_action
#: model:ir.model.fields.selection,name:rma.selection__rma_delivery_wizard__type__return
#: model_terms:ir.ui.view,arch_db:rma.rma_view_form
msgid "Return to customer"
msgstr ""

#. module: rma
#. odoo-python
#: code:addons/rma/models/rma.py:0
#, python-format
msgid ""
"Return: <a href=\"#\" data-oe-model=\"stock.picking\" data-oe-"
"id=\"%(id)d\">%(name)s</a> has been created."
msgstr ""

#. module: rma
#: model:ir.model.fields.selection,name:rma.selection__rma__state__returned
msgid "Returned"
msgstr ""

#. module: rma
#. odoo-python
#: code:addons/rma/wizard/stock_picking_return.py:0
#, python-format
msgid "Returned Picking"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma_tag__rma_ids
msgid "Rma"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma_delivery_wizard__rma_count
msgid "Rma Count"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_stock_return_picking__rma_location_ids
msgid "Rma Location"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma_delivery_wizard__scheduled_date
msgid "Scheduled Date"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__access_token
msgid "Security Token"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_res_company__send_rma_confirmation
#: model:ir.model.fields,field_description:rma.field_res_config_settings__send_rma_confirmation
msgid "Send RMA Confirmation"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_res_company__send_rma_receipt_confirmation
#: model:ir.model.fields,field_description:rma.field_res_config_settings__send_rma_receipt_confirmation
msgid "Send RMA Receipt Confirmation"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_res_company__send_rma_draft_confirmation
#: model:ir.model.fields,field_description:rma.field_res_config_settings__send_rma_draft_confirmation
msgid "Send RMA draft Confirmation"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.res_config_settings_view_form
msgid "Send automatic RMA info to customer"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.res_config_settings_view_form
msgid "Send automatic RMA products reception notification to customer"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.res_config_settings_view_form
msgid "Send automatic notification when the customer places an RMA"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.rma_view_form
msgid "Send by Email"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.rma_view_form
msgid "Send by Mail"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__sent
msgid "Sent"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma_team__sequence
msgid "Sequence"
msgstr ""

#. module: rma
#. odoo-python
#: code:addons/rma/models/stock_warehouse.py:0
#, python-format
msgid "Sequence RMA in"
msgstr ""

#. module: rma
#. odoo-python
#: code:addons/rma/models/stock_warehouse.py:0
#, python-format
msgid "Sequence RMA out"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.rma_view_form
msgid "Set to draft"
msgstr ""

#. module: rma
#: model:ir.actions.act_window,name:rma.action_rma_config_settings
#: model:ir.ui.menu,name:rma.menu_rma_general_settings
#: model_terms:ir.ui.view,arch_db:rma.rma_operation_form_view
msgid "Settings"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.rma_view_form
msgid "Share"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__partner_shipping_id
msgid "Shipping Address"
msgstr ""

#. module: rma
#: model:ir.model.fields,help:rma.field_rma__partner_shipping_id
msgid "Shipping address for current RMA."
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__show_create_receipt
msgid "Show Create Receipt Button"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__show_create_return
msgid "Show Create Return Button"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__show_create_refund
msgid "Show Create refund Button"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__show_create_replace
msgid "Show Create replace Button"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__origin
msgid "Source Document"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.rma_split_wizard_view_form2
#: model_terms:ir.ui.view,arch_db:rma.rma_view_form
msgid "Split"
msgstr ""

#. module: rma
#: model:ir.actions.act_window,name:rma.rma_split_wizard_action
msgid "Split RMA"
msgstr ""

#. module: rma
#. odoo-python
#: code:addons/rma/models/rma.py:0
#, python-format
msgid ""
"Split: <a href=\"#\" data-oe-model=\"rma\" data-oe-"
"id=\"%(id)d\">%(name)s</a> has been created."
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__state
#: model_terms:ir.ui.view,arch_db:rma.rma_view_search
msgid "State"
msgstr ""

#. module: rma
#. odoo-python
#: code:addons/rma/controllers/main.py:0
#: model_terms:ir.ui.view,arch_db:rma.portal_my_rmas
#, python-format
msgid "Status"
msgstr ""

#. module: rma
#: model:ir.model.fields,help:rma.field_rma__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""

#. module: rma
#: model:ir.model,name:rma.model_stock_move
msgid "Stock Move"
msgstr ""

#. module: rma
#: model:ir.model,name:rma.model_stock_rule
msgid "Stock Rule"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma_tag__name
msgid "Tag Name"
msgstr ""

#. module: rma
#: model:ir.model.constraint,message:rma.constraint_rma_tag_name_uniq
msgid "Tag name already exists !"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__tag_ids
msgid "Tags"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.rma_view_form
msgid "Tags..."
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma_team__user_id
msgid "Team Leader"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma_team__member_ids
#: model_terms:ir.ui.view,arch_db:rma.rma_team_view_form
msgid "Team Members"
msgstr ""

#. module: rma
#: model:ir.model.constraint,message:rma.constraint_rma_operation_name_uniq
msgid "That operation name already exists !"
msgstr ""

#. module: rma
#: model_terms:ir.actions.act_window,help:rma.rma_dashboard_action
msgid ""
"The RMA operation system allows you to configure each return operation\n"
"                with specific settings that will adjust its behavior."
msgstr ""

#. module: rma
#: model:ir.model.fields,help:rma.field_rma_tag__active
msgid "The active field allows you to hide the category without removing it."
msgstr ""

#. module: rma
#: model:ir.model.fields,help:rma.field_rma_team__alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr ""

#. module: rma
#: model:ir.model.fields,help:rma.field_rma_team__alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr ""

#. module: rma
#: model:ir.model.fields,help:rma.field_rma_team__alias_user_id
msgid ""
"The owner of records created upon receiving emails on this alias. If this "
"field is not set the system will attempt to find the right owner based on "
"the sender (From) address, or will use the Administrator account if no "
"system user is found for that address."
msgstr ""

#. module: rma
#. odoo-python
#: code:addons/rma/models/stock_move.py:0
#, python-format
msgid ""
"The quantity done for the product '%(id)s' must be equal to its initial "
"demand because the stock move is linked to an RMA (%(name)s)."
msgstr ""

#. module: rma
#. odoo-python
#: code:addons/rma/models/rma.py:0
#, python-format
msgid "The quantity to return is greater than remaining quantity."
msgstr ""

#. module: rma
#. odoo-python
#: code:addons/rma/models/rma.py:0
#, python-format
msgid ""
"The selected operation requires a return product different from the "
"originally delivered item. Please select the product to return."
msgstr ""

#. module: rma
#: model:ir.model.fields,help:rma.field_rma_tag__is_public
msgid "The tag is visible in the portal view"
msgstr ""

#. module: rma
#. odoo-python
#: code:addons/rma/models/account_move.py:0
#, python-format
msgid ""
"There is at least one invoice lines whose quantity is less than the quantity"
" specified in its linked RMA."
msgstr ""

#. module: rma
#. odoo-python
#: code:addons/rma/models/rma.py:0
#, python-format
msgid "This RMA cannot be split."
msgstr ""

#. module: rma
#. odoo-python
#: code:addons/rma/models/rma.py:0
#, python-format
msgid "This RMA cannot perform a replacement."
msgstr ""

#. module: rma
#. odoo-python
#: code:addons/rma/models/rma.py:0
#, python-format
msgid "This RMA cannot perform a return."
msgstr ""

#. module: rma
#: model:ir.actions.server,name:rma.rma_refund_action_server
#: model_terms:ir.ui.view,arch_db:rma.rma_view_form
msgid "To Refund"
msgstr ""

#. module: rma
#: model:ir.model,name:rma.model_stock_picking
msgid "Transfer"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma_delivery_wizard__type
msgid "Type"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_stock_return_picking__picking_type_code
msgid "Type of Operation"
msgstr ""

#. module: rma
#: model:ir.model.fields,help:rma.field_rma__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.rma_view_search
msgid "Unassigned RMAs"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma_delivery_wizard__product_uom
#: model:ir.model.fields,field_description:rma.field_rma_split_wizard__product_uom
msgid "Unit of measure"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.rma_view_form
msgid "Unlock"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.rma_view_search
msgid "Unresolved RMAs"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__product_uom
msgid "UoM"
msgstr ""

#. module: rma
#: model:ir.model.fields.selection,name:rma.selection__rma_operation__action_create_refund__update_quantity
msgid "Update Quantities"
msgstr ""

#. module: rma
#: model:ir.model.fields.selection,name:rma.selection__rma__priority__1
msgid "Urgent"
msgstr ""

#. module: rma
#: model:ir.model,name:rma.model_res_users
msgid "User"
msgstr ""

#. module: rma
#: model:res.groups,name:rma.rma_group_user_all
msgid "User: All Documents"
msgstr ""

#. module: rma
#: model:res.groups,name:rma.rma_group_user_own
msgid "User: Own Documents Only"
msgstr ""

#. module: rma
#: model:ir.model.fields.selection,name:rma.selection__rma__state__waiting_replacement
msgid "Waiting for replacement"
msgstr ""

#. module: rma
#: model:ir.model.fields.selection,name:rma.selection__rma__state__waiting_return
msgid "Waiting for return"
msgstr ""

#. module: rma
#: model:ir.model,name:rma.model_stock_warehouse
#: model:ir.model.fields,field_description:rma.field_rma__warehouse_id
#: model:ir.model.fields,field_description:rma.field_rma_delivery_wizard__warehouse_id
msgid "Warehouse"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__website_message_ids
#: model:ir.model.fields,field_description:rma.field_rma_team__website_message_ids
msgid "Website Messages"
msgstr ""

#. module: rma
#: model:ir.model.fields,help:rma.field_rma__website_message_ids
#: model:ir.model.fields,help:rma.field_rma_team__website_message_ids
msgid "Website communication history"
msgstr ""

#. module: rma
#: model:ir.model.fields,help:rma.field_res_company__send_rma_draft_confirmation
#: model:ir.model.fields,help:rma.field_res_config_settings__send_rma_draft_confirmation
msgid "When a customer places an RMA, send a notification with it"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.res_config_settings_view_form
msgid ""
"When customers themselves place an RMA from the portal, send an automatic "
"notification acknowleging it."
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.res_config_settings_view_form
msgid "When the RMA is confirmed, send an automatic information email."
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.res_config_settings_view_form
msgid ""
"When the RMA is receive, allow to finsish it manually choosing\n"
"                            a finalization reason."
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.res_config_settings_view_form
msgid ""
"When the RMA products are received, send an automatic information email."
msgstr ""

#. module: rma
#: model:ir.model.fields,help:rma.field_res_company__send_rma_receipt_confirmation
#: model:ir.model.fields,help:rma.field_res_config_settings__send_rma_receipt_confirmation
msgid ""
"When the RMA receipt is confirmed, send a confirmation email to the "
"customer."
msgstr ""

#. module: rma
#: model:ir.model.fields,help:rma.field_res_company__send_rma_confirmation
#: model:ir.model.fields,help:rma.field_res_config_settings__send_rma_confirmation
msgid ""
"When the delivery is confirmed, send a confirmation email to the customer."
msgstr ""

#. module: rma
#. odoo-python
#: code:addons/rma/models/rma.py:0
#, python-format
msgid "You cannot delete RMAs that are not in draft state"
msgstr ""

#. module: rma
#. odoo-python
#: code:addons/rma/wizard/stock_picking_return.py:0
#, python-format
msgid ""
"You must specify the 'Customer' in the 'Stock Picking' from which RMAs will "
"be created"
msgstr ""

#. module: rma
#: model:res.groups,comment:rma.rma_group_user_all
msgid ""
"the user will have access to all records of everyone in the RMA application."
msgstr ""

#. module: rma
#: model:res.groups,comment:rma.rma_group_user_own
msgid "the user will have access to his own data in the RMA application."
msgstr ""

#. module: rma
#: model:res.groups,comment:rma.rma_group_manager
msgid ""
"the user will have an access to the RMA configuration as well as statistic "
"reports."
msgstr ""

#. module: rma
#: model:mail.template,report_name:rma.mail_template_rma_draft_notification
#: model:mail.template,report_name:rma.mail_template_rma_notification
#: model:mail.template,report_name:rma.mail_template_rma_receipt_notification
msgid "{{(object.name or '')}}"
msgstr ""

#. module: rma
#: model:mail.template,subject:rma.mail_template_rma_notification
msgid "{{object.company_id.name}} RMA (Ref {{object.name or 'n/a' }})"
msgstr ""

#. module: rma
#: model:mail.template,subject:rma.mail_template_rma_receipt_notification
msgid ""
"{{object.company_id.name}} RMA (Ref {{object.name or 'n/a' }}) products "
"received"
msgstr ""

#. module: rma
#: model:mail.template,subject:rma.mail_template_rma_draft_notification
msgid ""
"{{object.company_id.name}} Your RMA has been succesfully created (Ref "
"{{object.name or 'n/a' }})"
msgstr ""
