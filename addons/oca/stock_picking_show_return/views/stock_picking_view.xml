<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <record id="view_picking_form" model="ir.ui.view">
        <field name="model">stock.picking</field>
        <field name="inherit_id" ref="stock.view_picking_form" />
        <field name="arch" type="xml">
            <notebook position="inside">
                <page
                    string="Returns"
                    attrs="{'invisible': [('returned_ids', '=', [])]}"
                >
                    <field name="returned_ids" />
                    <field name="source_picking_id" invisible="1" />
                </page>
            </notebook>
            <div name="button_box" position="inside">
                <button
                    name="action_show_source_picking"
                    type="object"
                    class="oe_stat_button"
                    icon="fa-arrow-left"
                    attrs="{'invisible': [('source_picking_id', '=', False)]}"
                >
                    <div class="o_form_field o_stat_info">
                        <span class="o_stat_text">Source picking</span>
                    </div>
                </button>
            </div>
        </field>
    </record>
</odoo>
