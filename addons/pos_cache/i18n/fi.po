# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_cache
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <kari.l<PERSON><PERSON>@emsystems.fi>, 2022
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:32+0000\n"
"PO-Revision-Date: 2022-09-22 05:54+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Finnish (https://app.transifex.com/odoo/teams/41243/fi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: pos_cache
#. odoo-javascript
#: code:addons/pos_cache/static/src/js/Chrome.js:0
#, python-format
msgid "All products are loaded."
msgstr "Kaikki tuotteet on ladattu."

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_cache__cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_config__cache_ids
msgid "Cache"
msgstr "Välimuisti"

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_cache__compute_user_id
msgid "Cache compute user"
msgstr "Välimuistiin haussa käytettävä käyttäjätunnus"

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_cache__config_id
msgid "Config"
msgstr "Konfiguraatio"

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_cache__create_uid
msgid "Created by"
msgstr "Luonut"

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_cache__create_date
msgid "Created on"
msgstr "Luotu"

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_cache__display_name
msgid "Display Name"
msgstr "Näyttönimi"

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_cache__id
msgid "ID"
msgstr "ID"

#. module: pos_cache
#: model_terms:ir.ui.view,arch_db:pos_cache.view_pos_config_kanban
msgid "Invalidate cache"
msgstr "Mitätöi välimuisti"

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_cache____last_update
msgid "Last Modified on"
msgstr "Viimeksi muokattu"

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_cache__write_uid
msgid "Last Updated by"
msgstr "Viimeksi päivittänyt"

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_cache__write_date
msgid "Last Updated on"
msgstr "Viimeksi päivitetty"

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_config__limit_products_per_request
msgid "Limit Products Per Request"
msgstr "Rajoita tuotteita per pyyntö"

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_config__oldest_cache_time
msgid "Oldest cache time"
msgstr "Vanhin välimuistiaika"

#. module: pos_cache
#: model:ir.actions.server,name:pos_cache.refresh_pos_cache_cron_ir_actions_server
#: model:ir.cron,cron_name:pos_cache.refresh_pos_cache_cron
msgid "PoS: refresh cache"
msgstr "Kassa: päivitä välimuisti"

#. module: pos_cache
#: model:ir.model,name:pos_cache.model_pos_cache
msgid "Point of Sale Cache"
msgstr "Kassan välimuisti"

#. module: pos_cache
#: model:ir.model,name:pos_cache.model_pos_config
msgid "Point of Sale Configuration"
msgstr "Kassapäätteen asetukset"

#. module: pos_cache
#: model:ir.model,name:pos_cache.model_pos_session
msgid "Point of Sale Session"
msgstr "Kassapäätteen istunto"

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_cache__product_domain
msgid "Product Domain"
msgstr "Tuotealue"

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_cache__product_fields
msgid "Product Fields"
msgstr "Tuotekentät"
