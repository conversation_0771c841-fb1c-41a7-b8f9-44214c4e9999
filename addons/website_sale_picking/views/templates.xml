<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <template id="checkout_delivery" inherit_id="website_sale_delivery.payment_delivery_methods">
        <xpath expr="//input[@name='delivery_type']" position="attributes">
            <attribute name="t-att-data-delivery-type">delivery.delivery_type</attribute>
        </xpath>
    </template>

    <template id="checkout_payment" inherit_id="payment.checkout">
        <xpath expr="//input[@name='o_payment_radio']" position="attributes">
            <attribute name="t-att-data-is-onsite">1 if provider.custom_mode == 'onsite' else 0</attribute>
        </xpath>
    </template>

    <template id="payment_confirmation_status" inherit_id="website_sale.payment_confirmation_status">
        <xpath expr="(//div[hasclass('card-body')])[1]" position="replace">
            <t t-if="payment_tx_id.provider_id.custom_mode == 'onsite'">
                <div class="card-body">
                    <div class="o_header_carrier_message">
                        <b t-out="order.carrier_id.name"/><span class="text-muted"> (On site picking)</span>
                    </div>
                    <div class="o_body_carrier_message">
                        <t t-out="order.carrier_id.website_description"/>
                    </div>
                </div>
            </t>
            <t t-else="">
                <t>$0</t> <!-- Replace by old content -->
            </t>
        </xpath>
    </template>
</odoo>
