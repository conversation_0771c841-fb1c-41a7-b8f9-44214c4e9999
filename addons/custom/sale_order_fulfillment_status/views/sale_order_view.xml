<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_quotation_tree_fulfillment_status" model="ir.ui.view">
        <field name="name">sale.quotation.tree.fulfillment.status</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale.view_quotation_tree" />
        <field name="arch" type="xml">
            <xpath expr="//field[@name='state']" position="after">
                <field name="next_picking_operation_type"/>
                <field name="next_picking_state"/>
            </xpath>
        </field>
    </record>
    <record id="sale_view_order_tree_fulfillment_status" model="ir.ui.view">
        <field name="name">sale.order.tree.fulfillment.status</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale.view_order_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='invoice_status']" position="after">
                <field name="next_picking_operation_type"/>
                <field name="next_picking_state"/>
            </xpath>
        </field>
    </record>
</odoo>
