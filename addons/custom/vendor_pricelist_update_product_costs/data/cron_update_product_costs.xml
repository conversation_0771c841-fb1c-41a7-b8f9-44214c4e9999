<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record model="ir.cron" id="ir_cron_vendor_pricelist_update_product_costs">
            <field name="name">Update Product Costs from Vendor Pricelist</field>
            <field eval="False" name="active"/>
            <field name="user_id" ref="base.user_root"/>
            <field name="interval_number">6</field>
            <field name="interval_type">hours</field>
            <field name="numbercall">-1</field>
            <field name="priority">30</field>
            <field eval="False" name="doall"/>
            <field ref="product.model_product_supplierinfo" name="model_id"/>
            <field name="state">code</field>
            <field name="code">
CATEGORY_FILTER = ["DETK", "DEJA", "DEDT", "DEAS", "DEMF"]
# Filter supplierinfo records by the relevant product categories, sort by write_date
supplier_infos = model.search([
'|',
'&amp;',
    ('product_tmpl_id.categ_id.name', 'in', CATEGORY_FILTER),
    ('product_tmpl_id.active', '=', True),
'&amp;',
    ('product_id.categ_id.name', 'in', CATEGORY_FILTER),
    ('product_id.active', '=', True),
], order='write_date asc')

# Map products from supplierinfos (both product templates and specific variants)
product_variants = supplier_infos.mapped('product_id')
template_variants = supplier_infos.mapped('product_tmpl_id.product_variant_ids')
products = product_variants | template_variants

products.update_cost_from_vendor_pricelist()
            </field>
        </record>
    </data>
</odoo>
