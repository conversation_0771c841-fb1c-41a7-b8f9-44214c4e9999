# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * note
# 
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <AUTHOR> <EMAIL>, 2018
# <PERSON> <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-21 13:17+0000\n"
"PO-Revision-Date: 2018-09-21 13:17+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Greek (https://www.transifex.com/odoo/teams/41243/el/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: el\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__message_needaction
msgid "Action Needed"
msgstr "Απαιτείται ενέργεια"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__open
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
msgid "Active"
msgstr "Σε Ισχύ"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__activity_ids
msgid "Activities"
msgstr "Δραστηριότητες"

#. module: note
#: model:ir.model,name:note.model_mail_activity
msgid "Activity"
msgstr "Δραστηριότητα"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__activity_state
msgid "Activity State"
msgstr "Κατάσταση Δραστηριότητας"

#. module: note
#: model:ir.model,name:note.model_mail_activity_type
msgid "Activity Type"
msgstr "Τύπος Δραστηριότητας"

#. module: note
#: model_terms:ir.actions.act_window,help:note.action_note_note
msgid "Add a new personal note"
msgstr ""

#. module: note
#: model_terms:ir.actions.act_window,help:note.note_tag_action
msgid "Add a new tag"
msgstr ""

#. module: note
#. openerp-web
#: code:addons/note/static/src/xml/systray.xml:14
#, python-format
msgid "Add a note"
msgstr ""

#. module: note
#. openerp-web
#: code:addons/note/static/src/xml/systray.xml:6
#, python-format
msgid "Add new note"
msgstr ""

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
msgid "Archive"
msgstr "Αρχειοθετήθηκαν"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__message_attachment_count
msgid "Attachment Count"
msgstr ""

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
msgid "By sticky note Category"
msgstr "Ανά Κατηγορία αυτοκόλλητης σημείωσης"

#. module: note
#: model:ir.model.fields,help:note.field_mail_activity_type__category
msgid "Categories may trigger specific behavior like opening calendar view"
msgstr ""
"Οι κατηγορίες ενδέχεται να ενεργοποιήσουν συγκεκριμένη συμπεριφορά όπως την "
"προβολή ημερολογίου"

#. module: note
#: model:ir.model.fields,field_description:note.field_mail_activity_type__category
msgid "Category"
msgstr "Κατηγορία"

#. module: note
#. openerp-web
#: code:addons/note/static/src/xml/systray.xml:10
#, python-format
msgid "Channel"
msgstr "Κανάλι"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__color
#: model:ir.model.fields,field_description:note.field_note_tag__color
msgid "Color Index"
msgstr "Χρωματισμός Ευρετήριου"

#. module: note
#: model:ir.ui.menu,name:note.menu_note_configuration
msgid "Configuration"
msgstr "Διαμόρφωση"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__create_uid
#: model:ir.model.fields,field_description:note.field_note_stage__create_uid
#: model:ir.model.fields,field_description:note.field_note_tag__create_uid
msgid "Created by"
msgstr "Δημιουργήθηκε από"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__create_date
#: model:ir.model.fields,field_description:note.field_note_stage__create_date
#: model:ir.model.fields,field_description:note.field_note_tag__create_date
msgid "Created on"
msgstr "Δημιουργήθηκε στις"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__date_done
msgid "Date done"
msgstr "Ημερομηνία ολοκλήρωσης"

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_kanban
msgid "Delete"
msgstr "Διαγραφή"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__display_name
#: model:ir.model.fields,field_description:note.field_note_stage__display_name
#: model:ir.model.fields,field_description:note.field_note_tag__display_name
msgid "Display Name"
msgstr "Εμφάνιση Ονόματος"

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_kanban
msgid "Dropdown menu"
msgstr ""

#. module: note
#: model:ir.model.fields,field_description:note.field_note_stage__fold
msgid "Folded by Default"
msgstr "Φάκελος από προεπιλογή"

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_kanban
msgid "Follower"
msgstr ""

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__message_follower_ids
msgid "Followers"
msgstr "Ακόλουθοι"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__message_channel_ids
msgid "Followers (Channels)"
msgstr "Ακόλουθοι (Κανάλια)"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__message_partner_ids
msgid "Followers (Partners)"
msgstr "Ακόλουθοι (Συνεργάτες)"

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
msgid "Future Activities"
msgstr "Μελλοντικές Δραστηριότητες"

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
msgid "Group By"
msgstr "Ομαδοποίηση κατά"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__id
#: model:ir.model.fields,field_description:note.field_note_stage__id
#: model:ir.model.fields,field_description:note.field_note_tag__id
msgid "ID"
msgstr "Κωδικός"

#. module: note
#: model:ir.model.fields,help:note.field_note_note__message_unread
msgid "If checked new messages require your attention."
msgstr "Εάν επιλεγεί τα νέα μηνύματα χρειάζονται την προσοχή σας"

#. module: note
#: model:ir.model.fields,help:note.field_note_note__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Εάν επιλεγεί τα νέα μηνύματα χρειάζονται την προσοχή σας."

#. module: note
#: model:ir.model.fields,help:note.field_note_note__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__message_is_follower
msgid "Is Follower"
msgstr "Είναι Ακόλουθος"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note____last_update
#: model:ir.model.fields,field_description:note.field_note_stage____last_update
#: model:ir.model.fields,field_description:note.field_note_tag____last_update
msgid "Last Modified on"
msgstr "Τελευταία τροποποίηση στις"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__write_uid
#: model:ir.model.fields,field_description:note.field_note_stage__write_uid
#: model:ir.model.fields,field_description:note.field_note_tag__write_uid
msgid "Last Updated by"
msgstr "Τελευταία Ενημέρωση από"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__write_date
#: model:ir.model.fields,field_description:note.field_note_stage__write_date
#: model:ir.model.fields,field_description:note.field_note_tag__write_date
msgid "Last Updated on"
msgstr "Τελευταία Ενημέρωση στις"

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
msgid "Late Activities"
msgstr "Καθυστερημένες Δραστηριότητες"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__message_main_attachment_id
msgid "Main Attachment"
msgstr ""

#. module: note
#: selection:mail.activity.type,category:0
msgid "Meeting"
msgstr "Συνάντηση"

#. module: note
#: model:note.stage,name:note.note_stage_01
msgid "Meeting Minutes"
msgstr ""

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__message_ids
msgid "Messages"
msgstr "Μηνύματα"

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
msgid "My Activities"
msgstr "Οι Δραστηριότητες μου"

#. module: note
#: model:note.stage,name:note.note_stage_00
msgid "New"
msgstr "Νέα"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Επόμενη Προθεσμία Δραστηριότητας"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__activity_summary
msgid "Next Activity Summary"
msgstr "Σύνοψη Επόμενης Δραστηριότητας"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__activity_type_id
msgid "Next Activity Type"
msgstr "Επόμενος Τύπος Δραστηριότητας"

#. module: note
#: model:ir.model,name:note.model_note_note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
#: model_terms:ir.ui.view,arch_db:note.view_note_note_form
msgid "Note"
msgstr "Σημείωση"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__memo
msgid "Note Content"
msgstr "Περιεχόμενο Σημείωσης"

#. module: note
#: model:ir.model,name:note.model_note_stage
msgid "Note Stage"
msgstr "Στάδιο Σημείωσης"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__name
msgid "Note Summary"
msgstr "Σύνοψη Σημείωσης"

#. module: note
#: model:ir.model,name:note.model_note_tag
msgid "Note Tag"
msgstr "Ετικέτα Σημείωσης"

#. module: note
#: code:addons/note/models/res_users.py:61
#: model:ir.actions.act_window,name:note.action_note_note
#: model:ir.ui.menu,name:note.menu_note_notes
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
#: model:note.stage,name:note.note_stage_02
#, python-format
msgid "Notes"
msgstr "Σημειώσεις"

#. module: note
#: model_terms:ir.actions.act_window,help:note.action_note_note
msgid ""
"Notes are private, unless you share them by inviting follower on a note.\n"
"            (Useful for meeting minutes)."
msgstr ""

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__message_needaction_counter
msgid "Number of Actions"
msgstr "Πλήθος ενεργειών"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__message_has_error_counter
msgid "Number of error"
msgstr ""

#. module: note
#: model:ir.model.fields,help:note.field_note_note__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Πλήθος μηνυμάτων που απαιτούν ενέργεια"

#. module: note
#: model:ir.model.fields,help:note.field_note_note__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: note
#: model:ir.model.fields,help:note.field_note_note__message_unread_counter
msgid "Number of unread messages"
msgstr "Πλήθος μη αναγνωσμένων μηνυμάτων"

#. module: note
#: selection:mail.activity.type,category:0
msgid "Other"
msgstr "Άλλο"

#. module: note
#: selection:note.note,activity_state:0
msgid "Overdue"
msgstr "Εκπρόθεσμο"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__user_id
#: model:ir.model.fields,field_description:note.field_note_stage__user_id
msgid "Owner"
msgstr "Ιδιοκτήτης"

#. module: note
#: model:ir.model.fields,help:note.field_note_stage__user_id
msgid "Owner of the note stage"
msgstr "Ιδιοκτήτης κατάστασης σημείωσης"

#. module: note
#: selection:note.note,activity_state:0
msgid "Planned"
msgstr "Προγραμματισμένη"

#. module: note
#: model:ir.model.fields,field_description:note.field_mail_activity__note_id
msgid "Related Note"
msgstr ""

#. module: note
#. openerp-web
#: code:addons/note/static/src/xml/systray.xml:23
#, python-format
msgid "Remember..."
msgstr ""

#. module: note
#: selection:mail.activity.type,category:0
#: model:mail.activity.type,name:note.mail_activity_data_reminder
msgid "Reminder"
msgstr ""

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__activity_user_id
msgid "Responsible User"
msgstr "Υπεύθυνος Χρήστης"

#. module: note
#. openerp-web
#: code:addons/note/static/src/xml/systray.xml:25
#, python-format
msgid "SAVE"
msgstr ""

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__sequence
#: model:ir.model.fields,field_description:note.field_note_stage__sequence
msgid "Sequence"
msgstr "Ακολουθία"

#. module: note
#. openerp-web
#: code:addons/note/static/src/xml/systray.xml:18
#, python-format
msgid "Set date and time"
msgstr ""

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
msgid "Show all records which has next action date is before today"
msgstr ""
"Εμφάνιση όλων των εγγραφών όπου η ημερομηνία επόμενης δράσης είναι πριν από "
"σήμερα"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__stage_id
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
msgid "Stage"
msgstr "Στάδιο"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_stage__name
msgid "Stage Name"
msgstr "Όνομα Σταδίου"

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_stage_form
msgid "Stage of Notes"
msgstr "Κατάσταση των Σημειώσεων"

#. module: note
#: model:ir.actions.act_window,name:note.action_note_stage
#: model:ir.ui.menu,name:note.menu_notes_stage
#: model_terms:ir.ui.view,arch_db:note.view_note_note_tree
msgid "Stages"
msgstr "Στάδια"

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_stage_tree
msgid "Stages of Notes"
msgstr "Κατάστασεις των Σημειώσεων"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__stage_ids
msgid "Stages of Users"
msgstr "Κατάστασεις των Χρηστών"

#. module: note
#: model:ir.model.fields,help:note.field_note_note__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Κατάσταση βασισμένη σε δραστηριότητες\n"
"Καθυστερημένη: Η ημερομηνία λήξης έχει ήδη περάσει\n"
"Σήμερα: Η ημερομηνία δραστηριότητας είναι σήμερα\n"
"Προγραμματισμένες: Μελλοντικές δραστηριότητες."

#. module: note
#: model:ir.model.fields,field_description:note.field_note_tag__name
msgid "Tag Name"
msgstr "Όνομα Ετικέτας"

#. module: note
#: sql_constraint:note.tag:0
msgid "Tag name already exists !"
msgstr "Το όνομα ετικέτας υπάρχει ήδη !"

#. module: note
#: model:ir.actions.act_window,name:note.note_tag_action
#: model:ir.model.fields,field_description:note.field_note_note__tag_ids
#: model:ir.ui.menu,name:note.notes_tag_menu
#: model_terms:ir.ui.view,arch_db:note.note_tag_view_form
#: model_terms:ir.ui.view,arch_db:note.note_tag_view_tree
#: model_terms:ir.ui.view,arch_db:note.view_note_note_form
msgid "Tags"
msgstr "Ετικέτες"

#. module: note
#. openerp-web
#: code:addons/note/static/src/js/systray_activity_menu.js:97
#: selection:note.note,activity_state:0
#, python-format
msgid "Today"
msgstr "Σήμερα"

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
msgid "Today Activities"
msgstr "Σημερινές Δραστηριότητες"

#. module: note
#: model:note.stage,name:note.note_stage_03
msgid "Todo"
msgstr "Εκκρεμότητες"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__message_unread
msgid "Unread Messages"
msgstr "Μη αναγνωσμένα μηνύματα"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Μετρητής μη αναγνωσμένων μηνυμάτων"

#. module: note
#: model:ir.model.fields,help:note.field_note_stage__sequence
msgid "Used to order the note stages"
msgstr "Χρησιμοποιείται για να ορίσετε την σειρά των καταστάσεων σημειώσεων"

#. module: note
#: model:ir.model,name:note.model_res_users
msgid "Users"
msgstr "Χρήστες"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__website_message_ids
msgid "Website Messages"
msgstr "Μηνύματα Ιστότοπου"

#. module: note
#: model:ir.model.fields,help:note.field_note_note__website_message_ids
msgid "Website communication history"
msgstr "Ιστορικό επικοινωνίας ιστότοπου"
