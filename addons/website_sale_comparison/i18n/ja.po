# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_sale_comparison
# 
# Translators:
# <PERSON><PERSON>, 2022
# <PERSON>, 2022
# <PERSON>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-12-15 12:45+0000\n"
"PO-Revision-Date: 2022-09-22 05:57+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website_sale_comparison
#: model:product.attribute.value,name:website_sale_comparison.product_attribute_value_8
msgid "134.7 x 200 x 7.2 mm"
msgstr "134.7 x 200 x 7.2 mm"

#. module: website_sale_comparison
#: model:product.attribute.value,name:website_sale_comparison.product_attribute_value_7
msgid "308 g"
msgstr "308 g"

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_compare
msgid ""
"<i class=\"fa fa-chevron-circle-down o_product_comparison_collpase\" "
"role=\"img\" aria-label=\"Collapse\" title=\"Collapse\"/>"
msgstr ""
"<i class=\"fa fa-chevron-circle-down o_product_comparison_collpase\" "
"role=\"img\" aria-label=\"Collapse\" title=\"Collapse\"/>"

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_compare
msgid "<i class=\"fa fa-shopping-cart me-2\"/>Add to Cart"
msgstr "<i class=\"fa fa-shopping-cart me-2\"/>カートに入れる"

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_product
msgid "<i class=\"fa fa-trash\" role=\"img\" aria-label=\"Remove\"/>"
msgstr "<i class=\"fa fa-trash\" role=\"img\" aria-label=\"Remove\"/>"

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_add_to_compare
msgid "<span class=\"fa fa-exchange me-2\"/>Compare"
msgstr "<span class=\"fa fa-exchange me-2\"/>比較"

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_compare
msgid "<strong>Price:</strong>"
msgstr "<strong>価格:</strong>"

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_compare
msgid "<strong>x</strong>"
msgstr "<strong>x</strong>"

#. module: website_sale_comparison
#: model:product.attribute.value,name:website_sale_comparison.product_attribute_value_1
msgid "Apple"
msgstr "りんご"

#. module: website_sale_comparison
#: model:ir.actions.act_window,name:website_sale_comparison.product_attribute_category_action
#: model:ir.ui.menu,name:website_sale_comparison.menu_attribute_category_action
msgid "Attribute Categories"
msgstr "属性カテゴリ"

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.snippet_options
msgid "Bottom of Page"
msgstr "ページ下"

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute__category_id
msgid "Category"
msgstr "カテゴリ"

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category__name
msgid "Category Name"
msgstr "カテゴリ名"

#. module: website_sale_comparison
#. odoo-javascript
#: code:addons/website_sale_comparison/static/src/xml/comparison.xml:0
#: code:addons/website_sale_comparison/static/src/xml/comparison.xml:0
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.add_to_compare
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_add_to_compare
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.snippet_options
#, python-format
msgid "Compare"
msgstr "比較"

#. module: website_sale_comparison
#. odoo-javascript
#: code:addons/website_sale_comparison/static/src/js/website_sale_comparison.js:0
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_compare
#, python-format
msgid "Compare Products"
msgstr "プロダクトを比較"

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_compare
msgid "Contact Us"
msgstr "お問い合わせ"

#. module: website_sale_comparison
#: model_terms:ir.actions.act_window,help:website_sale_comparison.product_attribute_category_action
msgid "Create a new attribute category"
msgstr "新しい属性カテゴリを作成"

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category__create_uid
msgid "Created by"
msgstr "作成者"

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category__create_date
msgid "Created on"
msgstr "作成日"

#. module: website_sale_comparison
#: model:product.attribute,name:website_sale_comparison.product_attribute_8
#: model:product.attribute.category,name:website_sale_comparison.product_attribute_category_2
msgid "Dimensions"
msgstr "ディメンション"

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category__display_name
msgid "Display Name"
msgstr "表示名"

#. module: website_sale_comparison
#: model:product.attribute.category,name:website_sale_comparison.product_attribute_category_duration
msgid "Duration"
msgstr "所要時間"

#. module: website_sale_comparison
#: model:product.attribute.category,name:website_sale_comparison.product_attribute_category_general_features
msgid "General Features"
msgstr "一般機能"

#. module: website_sale_comparison
#: model_terms:ir.actions.act_window,help:website_sale_comparison.product_attribute_category_action
msgid ""
"Group attributes by category that will appear in the specification\n"
"                part of a product page."
msgstr ""
"プロダクトページの仕様部分に表示される属性をカテゴリ別に\n"
"　　　　グループ化する。"

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category__id
msgid "ID"
msgstr "ID"

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category____last_update
msgid "Last Modified on"
msgstr "最終更新日"

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category__write_uid
msgid "Last Updated by"
msgstr "最終更新者"

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category__write_date
msgid "Last Updated on"
msgstr "最終更新日"

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.snippet_options
msgid "None"
msgstr "なし"

#. module: website_sale_comparison
#. odoo-javascript
#: code:addons/website_sale_comparison/static/src/xml/comparison.xml:0
#: code:addons/website_sale_comparison/static/src/xml/comparison.xml:0
#, python-format
msgid "Product"
msgstr "プロダクト"

#. module: website_sale_comparison
#: model:ir.model,name:website_sale_comparison.model_product_attribute
msgid "Product Attribute"
msgstr "プロダクト属性"

#. module: website_sale_comparison
#: model:ir.model,name:website_sale_comparison.model_product_attribute_category
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_attribute_category_tree_view
msgid "Product Attribute Category"
msgstr "プロダクト属性カテゴリ"

#. module: website_sale_comparison
#: model:ir.model,name:website_sale_comparison.model_product_template_attribute_line
msgid "Product Template Attribute Line"
msgstr "プロダクトテンプレート属性明細"

#. module: website_sale_comparison
#: model:ir.model,name:website_sale_comparison.model_product_product
msgid "Product Variant"
msgstr "プロダクトバリアント"

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_compare
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_product
msgid "Product image"
msgstr "プロダクト画像"

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category__attribute_ids
msgid "Related Attributes"
msgstr "関連属性"

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_product
msgid "Remove"
msgstr "削除"

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category__sequence
msgid "Sequence"
msgstr "付番"

#. module: website_sale_comparison
#: model:ir.model.fields,help:website_sale_comparison.field_product_attribute__category_id
msgid ""
"Set a category to regroup similar attributes under the same section in the "
"Comparison page of eCommerce"
msgstr "eコマースの比較ページで、類似した属性を同じセクションの下に再グループ化するためのカテゴリを設定します。"

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_compare
msgid "Shop Comparator"
msgstr "店舗比較機能"

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.snippet_options
msgid "Specification"
msgstr "仕様設計"

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_attributes_body
msgid "Specifications"
msgstr "仕様設計"

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_attributes_body
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_compare
msgid "Uncategorized"
msgstr "未分類"

#. module: website_sale_comparison
#. odoo-javascript
#: code:addons/website_sale_comparison/static/src/xml/comparison.xml:0
#: code:addons/website_sale_comparison/static/src/xml/comparison.xml:0
#, python-format
msgid "Warning"
msgstr "警告"

#. module: website_sale_comparison
#: model:product.attribute,name:website_sale_comparison.product_attribute_7
msgid "Weight"
msgstr "重量"

#. module: website_sale_comparison
#. odoo-javascript
#: code:addons/website_sale_comparison/static/src/xml/comparison.xml:0
#, python-format
msgid "You can compare max 4 products."
msgstr "最大で4プロダクトを比較できます。"

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_attributes_body
msgid "or"
msgstr "または"
