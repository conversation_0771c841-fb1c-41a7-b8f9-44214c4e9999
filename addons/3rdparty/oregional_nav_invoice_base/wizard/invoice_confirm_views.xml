<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- View form -->
    <record id="invoice_confirm_wizard_view_form"
            model="ir.ui.view"
    >
        <field name="name">oregional.nav.invoice.base.nav.invoice.confirm.wizard.view.form</field>
        <field name="model">oregional.nav.invoice.confirm.wizard</field>
        <field name="arch"
               type="xml"
        >
            <form string="Confirm">
                <!-- Invisible fields -->
                <field name="has_error"
                       invisible="1"
                />
                <field name="eu_oss_eligible"
                       invisible="1"
                />
                <field name="invoice"
                       invisible="1"
                />
                <field name="invoice_type"
                       invisible="1"
                />
                <field name="journal"
                       invisible="1"
                />
                <field name="journal_partner_vat_validation"
                       invisible="1"
                />
                <!-- Inbound invoice info -->
                <div name="inbound_invoice_info_div"
                     attrs="{'invisible': [
                         ('invoice_type', 'not in', ['in_invoice', 'in_refund'])
                     ]}"
                     class="card text-white bg-info mt-2 mb-2"
                     colspan="2"
                >
                    <div class="card-header">
                        <i class="fa fa-info-circle"/> Information
                    </div>
                    <div class="card-body">
                        <div>Confirm inbound invoice?</div>
                    </div>
                </div>
                <!-- Error details -->
                <div name="error_details_div"
                     attrs="{'invisible': [
                         ('error_details', '=', '')
                     ]}"
                     class="card text-white bg-danger mt-2 mb-2"
                     colspan="2"
                >
                    <div class="card-header">
                        <i class="fa fa-exclamation-circle"/> Error
                    </div>
                    <div class="card-body">
                        <div>
                            <field name="error_details"/>
                        </div>
                    </div>
                </div>
                <!-- Warning details -->
                <div name="warning_details_div"
                     attrs="{'invisible': [
                         ('warning_details', '=', '')
                     ]}"
                     class="card text-white bg-warning mt-2 mb-2"
                     colspan="2"
                >
                    <div class="card-header">
                        <i class="fa fa-exclamation-triangle"/> Warning
                    </div>
                    <div class="card-body">
                        <div>
                            <field name="warning_details"/>
                        </div>
                    </div>
                </div>
                <!-- EU OSS  info -->
                <div name="eu_oss_info"
                     attrs="{'invisible': [
                         ('eu_oss_eligible', '=', False),
                     ]}"
                     class="card text-white bg-info mt-2 mb-2"
                     colspan="2"
                     groups="oregional_account_base.invoice_oss_group"
                >
                    <div class="card-header">
                        <i class="fa fa-info-circle"/> EU OSS
                        <span class="ml-4 font-weight-bold font-weight-bold text-uppercase"
                              attrs="{
                                   'invisible': [
                                       ('eu_oss_eligible', '=', False)
                                   ]
                               }"
                        >
                            Enabled
                        </span>
                        <field name="eu_oss_enabled"
                               attrs="{
                                   'invisible': [
                                       ('eu_oss_eligible', '=', False)
                                   ],
                                   'required': [
                                       ('eu_oss_eligible', '=', True)
                                   ],
                               }"
                               string="Report invoice in EU OSS"
                               widget="boolean_toggle"
                        />
                    </div>
                </div>
                <!-- Vat position info -->
                <div name="vat_position_info"
                     attrs="{'invisible': [
                         ('journal_partner_vat_validation', 'not in', ['info', 'confirm']),
                     ]}"
                     class="card text-white bg-info mt-2 mb-2"
                     colspan="2"
                >
                    <div class="card-header">
                        <i class="fa fa-info-circle"/> VAT Position
                        <span class="ml-4 font-weight-bold font-weight-bold text-uppercase"
                              attrs="{
                                   'invisible': [
                                       ('journal_partner_vat_validation', '!=', 'confirm')
                                   ]
                               }"
                        >
                            Confirm
                        </span>
                        <field name="is_partner_vat_position_confirmed"
                               attrs="{
                                   'invisible': [
                                       ('journal_partner_vat_validation', '!=', 'confirm')
                                   ],
                                   'required': [
                                       ('journal_partner_vat_validation', '=', 'confirm')
                                   ],
                               }"
                               string="Confirm VAT position"
                               widget="boolean_toggle"
                        />
                    </div>
                    <div class="card-body">
                        <table class="table table-borderless table-sm">
                            <tbody>
                                <tr>
                                    <td colspan="2">
                                        <h4>
                                            <field name="partner_contact_address"/>
                                        </h4>
                                    </td>
                                </tr>
                                <tr>
                                    <td style="width:50%;">
                                        <span>Country</span>
                                        <span class="mr-1">:</span>
                                        <field name="partner_country"
                                               options="{'no_open': True}"
                                               class="oe_inline ml-1"
                                        />
                                    </td>
                                    <td style="width:50%;">
                                        <span>Fiscal Position</span>
                                        <span class="mr-1">:</span>
                                        <field name="fiscal_position"
                                               class="oe_inline"
                                        />
                                        <span attrs="{'invisible': [('fiscal_position', '!=', False)]}">None</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <span>NAV VAT Status</span>
                                        <span class="mr-1">:</span>
                                        <field name="nav_vat_status"
                                               attrs="{
                                                   'required': [
                                                       ('journal_partner_vat_validation', '=', 'confirm'),
                                                   ],
                                               }"
                                               widget="radio"
                                        />
                                    </td>
                                    <td>
                                        <div name="nav_incorporation"
                                             attrs="{
                                                 'invisible': [
                                                     ('nav_vat_status', '!=', 'domestic'),
                                                 ],
                                             }"
                                        >
                                            <span>NAV Taxpayer Type</span>
                                            <span class="mr-1">:</span>
                                            <field name="nav_incorporation"
                                                   attrs="{
                                                       'required': [
                                                           ('journal_partner_vat_validation', '=', 'confirm'),
                                                           ('nav_vat_status', '=', 'domestic'),
                                                       ],
                                                   }"
                                                   widget="radio"
                                            />
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <div name="partner_l10n_hu_vat"
                                             attrs="{'invisible': [('nav_vat_status', '!=', 'domestic')]}"
                                        >
                                            <span>Hungarian VAT number</span>
                                            <span class="mr-1">:</span>
                                            <field name="partner_l10n_hu_vat"
                                                   class="oe_inline"
                                            />
                                        </div>
                                    </td>
                                    <td>
                                        <div name="partner_vat"
                                             attrs="{'invisible': [('nav_vat_status', 'in', [False, 'private_person'])]}"
                                        >
                                            <span>Tax ID (Community/Third Country)</span>
                                            <span class="mr-1">:</span>
                                            <field name="partner_vat"
                                                   class="oe_inline"
                                            />
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <footer>
                    <button name="action_retry"
                            attrs="{'invisible': [
                                '|',
                                ('has_error', '=', False),
                                ('journal_partner_vat_validation', 'in', [False, 'none']),
                            ]}"
                            class="btn-primary"
                            icon="fa-refresh"
                            string="Retry"
                            type="object"
                    />
                    <button name="action_confirm"
                            attrs="{'invisible': [
                                '|',
                                ('has_error', '=', True),
                                ('is_partner_vat_position_confirmed', '=', False)
                            ]}"
                            class="btn-primary"
                            default_focus="1"
                            string="Confirm"
                            type="object"
                    />
                    <button special="cancel"
                            class="btn-secondary"
                            string="Cancel"
                    />
                </footer>
            </form>
        </field>
    </record>
</odoo>
