# -*- coding: utf-8 -*-
# 1 : imports of python lib

# 2 : imports of odoo
from odoo import _, api, exceptions, fields, models  # alphabetically ordered

# 3 : imports from odoo modules

# 4 : variable declarations


# Class
class OregionalNavInvoiceApiResConfigSettings(models.TransientModel):
    # Private attributes
    _inherit = 'res.config.settings'

    # Default methods

    # Field declarations
    oregional_nav_invoice_api_batch_limit = fields.Integer(
        readonly=False,
        related='company_id.oregional_nav_invoice_api_batch_limit',
    )

    # Compute and search fields, in the same order of field declarations

    # Constraints and onchanges

    # CRUD methods (and name_get, name_search, ...) overrides

    # Action methods

    # Business methods
