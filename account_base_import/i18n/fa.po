# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_base_import
# 
# Translators:
# <PERSON><PERSON><PERSON> <Farvas<PERSON>@gmail.com>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON>, 2023
# <PERSON>, 2023
# <PERSON><PERSON><PERSON> Bar<PERSON>hory <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-12-15 13:28+0000\n"
"PO-Revision-Date: 2022-09-22 05:45+0000\n"
"Last-Translator: Mostafa Barmshory <<EMAIL>>, 2024\n"
"Language-Team: Persian (https://app.transifex.com/odoo/teams/41243/fa/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fa\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: account_base_import
#: model_terms:ir.ui.view,arch_db:account_base_import.res_config_settings_import_view_form
msgid "<span class=\"o_form_label\">Initial Setup</span>"
msgstr ""

#. module: account_base_import
#: model_terms:ir.ui.view,arch_db:account_base_import.res_config_settings_import_view_form
msgid "<span class=\"text-muted\">(end of year balances)</span>"
msgstr "<span class=\"text-muted\">(پایان ترازهای شما)</span>"

#. module: account_base_import
#: model_terms:ir.ui.view,arch_db:account_base_import.res_config_settings_import_view_form
msgid "<span class=\"text-muted\">(for full history)</span>"
msgstr "<span class=\"text-muted\">(for full history)</span>"

#. module: account_base_import
#: model:ir.model,name:account_base_import.model_account_account
msgid "Account"
msgstr "حساب"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
#, python-format
msgid "Account Winbooks Import module"
msgstr "ماژول وارد کردن Winbook حساب"

#. module: account_base_import
#: model_terms:ir.ui.view,arch_db:account_base_import.res_config_settings_import_view_form
msgid "Accounting Import"
msgstr "ورودی حسابداری"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/js/account_import_guide.js:0
#: model:ir.actions.client,name:account_base_import.action_open_import_guide
#, python-format
msgid "Accounting Import Guide"
msgstr "دستورالعمل واردکردن حسابداری"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
#, python-format
msgid "Accounting Import Options"
msgstr "گزینه‌های واردکردن حسابداری"

#. module: account_base_import
#: model:ir.model,name:account_base_import.model_base_import_import
msgid "Base Import"
msgstr "وارد کردن پایه"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/js/account_setup_import.js:0
#: model:ir.actions.act_window,name:account_base_import.action_open_coa_setup
#, python-format
msgid "Chart of Accounts"
msgstr "جدول حساب ها"

#. module: account_base_import
#: model_terms:ir.ui.view,arch_db:account_base_import.res_config_settings_import_view_form
msgid "Choose how you want to setup your CoA"
msgstr "روش راه‌اندازی CoA خود را انتخاب کنید"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/js/account_setup_import.js:0
#, python-format
msgid "Customers"
msgstr "مشتریان"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
#, python-format
msgid "Download"
msgstr "دانلود"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
#, python-format
msgid "Excel Import"
msgstr "وارد کردن فایل اکسل"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
#, python-format
msgid "FEC"
msgstr "FEC"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
#, python-format
msgid "FEC Import module"
msgstr "ماژول وارد کردن FEC"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
#: model_terms:ir.ui.view,arch_db:account_base_import.res_config_settings_import_view_form
#, python-format
msgid "Import"
msgstr "ورود"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
#: model:ir.actions.client,name:account_base_import.action_account_import
#, python-format
msgid "Import Chart of Accounts"
msgstr "وارد کردن جدول حساب‌ها"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
#, python-format
msgid "Import CoA"
msgstr "وارد کردن CoA"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
#, python-format
msgid "Import Contacts"
msgstr "وارد کردن مخاطبین"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
#: model:ir.actions.client,name:account_base_import.action_account_move_line_import
#, python-format
msgid "Import Journal Items"
msgstr "وارد کردن آیتم‌های دفتر روزنامه"

#. module: account_base_import
#: model:ir.actions.client,name:account_base_import.action_partner_import
msgid "Import Partners"
msgstr "وارد کردن همکاران"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
#, python-format
msgid "Import contacts"
msgstr "وارد کردن مخاطبین"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
#, python-format
msgid "Import customers or suppliers (partners) and their contacts using a"
msgstr ""
"وارد کردن مشتریان یا عرضه‌کنندگان (همکاران) و مخاطبین آنها با استفاده از"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
#, python-format
msgid "Import the Chart of Accounts and initial balances using a"
msgstr "وارد کردن جدول حساب‌ها و ترازهای اولیه با استفاده از "

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/js/account_import_guide.js:0
#, python-format
msgid "Install a module"
msgstr "نصب یک ماژول"

#. module: account_base_import
#: model:ir.model,name:account_base_import.model_account_move_line
msgid "Journal Item"
msgstr "آیتم روزنامه"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/js/account_setup_import.js:0
#, python-format
msgid "Journal Items"
msgstr "آیتم های روزنامه"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
#, python-format
msgid ""
"Most accounting software in France support exporting FEC file for audit purposes.\n"
"                            Use the"
msgstr ""
"اکثر نرم‌افزارهای موجود در فرانسه، انتقال فایل FEC را برای حسابرسی پشتیبانی "
"می‌کنند. از ... استفاده کنید"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
#, python-format
msgid "Optional, but useful to import open receivables & payables using a"
msgstr ""
"اختیاری است، اما برای واردکردن حساب‌های دریافتی و پرداختی باز با استفاده از "
"... مفید است"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
#: model_terms:ir.ui.view,arch_db:account_base_import.res_config_settings_import_view_form
#, python-format
msgid "Review Manually"
msgstr "مرور دستی"

#. module: account_base_import
#. odoo-python
#: code:addons/account_base_import/models/account_move_line.py:0
#, python-format
msgid "The import file is missing the following required columns: %s"
msgstr "فایل وارد شده فاقد ستون‌های مورد نیاز زیر است: %s"

#. module: account_base_import
#. odoo-python
#: code:addons/account_base_import/models/account_account.py:0
#, python-format
msgid "The import file must contain the 'code' column"
msgstr "فایل وارد شده باید حاوی ستون «کد» باشد"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
#, python-format
msgid ""
"Tip: we recommend importing your initial balances using the Chart of Account"
" import. Only use the Journal Items import for unreconciled entries in your "
"Payable and Receivable Accounts."
msgstr ""
"نکته: توصیه می‌کنیم ترازهای اولیه‌ی خود را با استفاده از نمودار وارد کردن "
"حساب، وارد کنید. تنها از آیتم‌های دفتر روزنامه برای اسناد تطبیق داده نشده در"
" حساب‌های دریافتی و پرداختی خود استفاده کنید."

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
#, python-format
msgid "Use predefined format to import your data faster."
msgstr ""
"از فرمت از پیش تعیین شده برای وارد کردن سریع‌تر داده‌های خود استفاده کنید."

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
#, python-format
msgid "Use templates to import CSV or Excel for your accounting setup."
msgstr ""
"از قالب‌ها برای وارد کردن فایل  CSV یا اکسل برای شروع حسابداری خود استفاده "
"کنید."

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
#, python-format
msgid "Winbooks"
msgstr "Winbooks"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
#, python-format
msgid ""
"Winbooks is an old school Belgian accounting software acquired by Exact.\n"
"                            Use the"
msgstr ""
"Winbook یک نرم‌افزار حسابداری بلژیکی است تحت مالکیت شرکت Exact است. از ... "
"استفاده کنید"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
#, python-format
msgid "or"
msgstr "یا"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
#, python-format
msgid "template."
msgstr "قالب"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
#, python-format
msgid ""
"to import a Winbooks full back-up (Maintenance > Backup) to get the chart of accounts, contacts, taxes, history of journal entries, and documents.\n"
"                            Support versions: Winbooks Desktop 5.50, 6, 7, 8."
msgstr ""
" وارد کردن نسخه‌ی Back-up (نگهداری<Backup) به منظور دریافت جدول حساب‌ها، مخاطبین، مالیات‎‌ها، تاریخچه‌ی اسناد دفتر روزنامه و اسناد \n"
"نسخه‌های تحت پشتیبانی:  Winbooks Desktop 5.50, 6, 7, 8"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
#, python-format
msgid ""
"to import the FEC file. We will setup your charts of accounts and the "
"history of journal entries."
msgstr ""
"برای وارد کردن فایل FEC، جدول‌ حساب‌ها و تاریخچه‌ی اسناد دفتر روزنامه‌‌ را "
"برای شما ایجاد می‌کنیم. "
