# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_base_import
# 
# Translators:
# <PERSON><PERSON>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# NoaFarkash, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON>, 2022
# ya<PERSON> terner, 2023
# <PERSON> <<EMAIL>>, 2024
# or balmas, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-12-15 13:28+0000\n"
"PO-Revision-Date: 2022-09-22 05:45+0000\n"
"Last-Translator: or balmas, 2025\n"
"Language-Team: Hebrew (https://app.transifex.com/odoo/teams/41243/he/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: he\n"
"Plural-Forms: nplurals=3; plural=(n == 1 && n % 1 == 0) ? 0 : (n == 2 && n % 1 == 0) ? 1: 2;\n"

#. module: account_base_import
#: model_terms:ir.ui.view,arch_db:account_base_import.res_config_settings_import_view_form
msgid "<span class=\"o_form_label\">Initial Setup</span>"
msgstr "<span class=\"o_form_label\">הגדרה ראשוונית</span>"

#. module: account_base_import
#: model_terms:ir.ui.view,arch_db:account_base_import.res_config_settings_import_view_form
msgid "<span class=\"text-muted\">(end of year balances)</span>"
msgstr "<span class=\"text-muted\">(מאזני סוף שנה)</span>"

#. module: account_base_import
#: model_terms:ir.ui.view,arch_db:account_base_import.res_config_settings_import_view_form
msgid "<span class=\"text-muted\">(for full history)</span>"
msgstr "<span class=\"text-muted\">(להיסטוריה מלאה)</span>"

#. module: account_base_import
#: model:ir.model,name:account_base_import.model_account_account
msgid "Account"
msgstr "חשבון"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
#, python-format
msgid "Account Winbooks Import module"
msgstr ""

#. module: account_base_import
#: model_terms:ir.ui.view,arch_db:account_base_import.res_config_settings_import_view_form
msgid "Accounting Import"
msgstr "יבוא הנהלת חשבונות"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/js/account_import_guide.js:0
#: model:ir.actions.client,name:account_base_import.action_open_import_guide
#, python-format
msgid "Accounting Import Guide"
msgstr "מדריך ייבוא הנהלת חשבונות"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
#, python-format
msgid "Accounting Import Options"
msgstr "אפשרויות ייבוא הנהלת חשבונות"

#. module: account_base_import
#: model:ir.model,name:account_base_import.model_base_import_import
msgid "Base Import"
msgstr "ייבוא בסיס"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/js/account_setup_import.js:0
#: model:ir.actions.act_window,name:account_base_import.action_open_coa_setup
#, python-format
msgid "Chart of Accounts"
msgstr "לוח חשבונות"

#. module: account_base_import
#: model_terms:ir.ui.view,arch_db:account_base_import.res_config_settings_import_view_form
msgid "Choose how you want to setup your CoA"
msgstr ""

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/js/account_setup_import.js:0
#, python-format
msgid "Customers"
msgstr "לקוחות"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
#, python-format
msgid "Download"
msgstr "הורד"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
#, python-format
msgid "Excel Import"
msgstr "ייבוא אקסל"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
#, python-format
msgid "FEC"
msgstr ""

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
#, python-format
msgid "FEC Import module"
msgstr ""

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
#: model_terms:ir.ui.view,arch_db:account_base_import.res_config_settings_import_view_form
#, python-format
msgid "Import"
msgstr "ייבא"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
#: model:ir.actions.client,name:account_base_import.action_account_import
#, python-format
msgid "Import Chart of Accounts"
msgstr "ייבוא לוח חשבונות"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
#, python-format
msgid "Import CoA"
msgstr ""

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
#, python-format
msgid "Import Contacts"
msgstr "ייבא אנשי קשר"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
#: model:ir.actions.client,name:account_base_import.action_account_move_line_import
#, python-format
msgid "Import Journal Items"
msgstr "יבוא תנועות יומן"

#. module: account_base_import
#: model:ir.actions.client,name:account_base_import.action_partner_import
msgid "Import Partners"
msgstr "ייבוא שותפים"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
#, python-format
msgid "Import contacts"
msgstr "ייבוא אנשי קשר"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
#, python-format
msgid "Import customers or suppliers (partners) and their contacts using a"
msgstr "ייבוא חשבונות שותפים (לקוחות או ספקים) ופרטי ההתקשרות שלהם באמצעות"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
#, python-format
msgid "Import the Chart of Accounts and initial balances using a"
msgstr "ייבוא לוח חשבונות ויתרות פתיחה באמצעות"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/js/account_import_guide.js:0
#, python-format
msgid "Install a module"
msgstr "התקן מודול"

#. module: account_base_import
#: model:ir.model,name:account_base_import.model_account_move_line
msgid "Journal Item"
msgstr "תנועת יומן"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/js/account_setup_import.js:0
#, python-format
msgid "Journal Items"
msgstr "תנועות יומן"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
#, python-format
msgid ""
"Most accounting software in France support exporting FEC file for audit purposes.\n"
"                            Use the"
msgstr ""

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
#, python-format
msgid "Optional, but useful to import open receivables & payables using a"
msgstr ""

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
#: model_terms:ir.ui.view,arch_db:account_base_import.res_config_settings_import_view_form
#, python-format
msgid "Review Manually"
msgstr "סקירה ידנית"

#. module: account_base_import
#. odoo-python
#: code:addons/account_base_import/models/account_move_line.py:0
#, python-format
msgid "The import file is missing the following required columns: %s"
msgstr "בקובץ הייבוא חסר את העמודות  הדרושות להלן: %s"

#. module: account_base_import
#. odoo-python
#: code:addons/account_base_import/models/account_account.py:0
#, python-format
msgid "The import file must contain the 'code' column"
msgstr "בקובץ המיובא חייבים עמודת 'קוד'"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
#, python-format
msgid ""
"Tip: we recommend importing your initial balances using the Chart of Account"
" import. Only use the Journal Items import for unreconciled entries in your "
"Payable and Receivable Accounts."
msgstr ""
"טיפ: אנחנו ממליצים לייבא יתרות פתיחב באמצעות ייבוא לוח חשבונות. יש לייבוא "
"תנועות יומן לרשומות שצריכות התאמה מול חשבונות תקבולים ותשלומים."

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
#, python-format
msgid "Use predefined format to import your data faster."
msgstr "השתמשו בתבנית מוכנה מראש כדי לייבוא את הנתונים שלך במהירות."

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
#, python-format
msgid "Use templates to import CSV or Excel for your accounting setup."
msgstr "השתמש בתבנית כדי לייבא קובץ CSV או Excel להגדרות הנהלת החשבונות שלך."

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
#, python-format
msgid "Winbooks"
msgstr ""

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
#, python-format
msgid ""
"Winbooks is an old school Belgian accounting software acquired by Exact.\n"
"                            Use the"
msgstr ""

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
#, python-format
msgid "or"
msgstr "או"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
#, python-format
msgid "template."
msgstr "תבנית."

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
#, python-format
msgid ""
"to import a Winbooks full back-up (Maintenance > Backup) to get the chart of accounts, contacts, taxes, history of journal entries, and documents.\n"
"                            Support versions: Winbooks Desktop 5.50, 6, 7, 8."
msgstr ""

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
#, python-format
msgid ""
"to import the FEC file. We will setup your charts of accounts and the "
"history of journal entries."
msgstr ""
