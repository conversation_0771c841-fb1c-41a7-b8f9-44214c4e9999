# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_co_edi
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.1alpha1+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-12-08 11:47+0000\n"
"PO-Revision-Date: 2020-12-08 11:47+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_co_edi
#: model_terms:ir.ui.view,arch_db:l10n_co_edi.electronic_invoice_body
msgid "1.00"
msgstr ""

#. module: l10n_co_edi
#: code:addons/l10n_co_edi/models/account_invoice.py:0
#, python-format
msgid "<br/>Error message: %s"
msgstr ""

#. module: l10n_co_edi
#: code:addons/l10n_co_edi/models/account_invoice.py:0
#, python-format
msgid "<br/>Government response: %s"
msgstr ""

#. module: l10n_co_edi
#: code:addons/l10n_co_edi/models/account_invoice.py:0
#, python-format
msgid "<br/>Legal status: %s"
msgstr ""

#. module: l10n_co_edi
#: model_terms:ir.ui.view,arch_db:l10n_co_edi.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Carvajal Configuration</span>"
msgstr ""

#. module: l10n_co_edi
#: model_terms:ir.ui.view,arch_db:l10n_co_edi.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Report Configuration</span>"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_02
msgid "ABEJORRAL"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_03
msgid "ABRIAQUÍ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_690
msgid "ACACÍAS"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_578
msgid "ACANDÍ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_608
msgid "ACEVEDO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_152
msgid "ACHÍ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_609
msgid "AGRADO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_461
msgid "AGUA DE DIOS"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_407
msgid "AGUACHICA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_849
msgid "AGUADA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_322
msgid "AGUADAS"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1061
msgid "AGUAZUL"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_408
msgid "AGUSTÍN CODAZZI"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_610
msgid "AIPE"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields.selection,name:l10n_co_edi.selection__account_move__l10n_co_edi_operation_type__09
msgid "AIU"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_349
#: model:res.city,name:l10n_co_edi.city_co_645
#: model:res.city,name:l10n_co_edi.city_co_850
msgid "ALBANIA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_462
#: model:res.city,name:l10n_co_edi.city_co_719
msgid "ALBÁN"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1010
msgid "ALCALÁ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_720
msgid "ALDANA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_04
msgid "ALEJANDRÍA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_660
msgid "ALGARROBO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_611
msgid "ALGECIRAS"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_365
msgid "ALMAGUER"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_199
msgid "ALMEIDA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_963
msgid "ALPUJARRA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_612
msgid "ALTAMIRA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_579
msgid "ALTO BAUDÓ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_153
msgid "ALTOS DEL ROSARIO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_964
msgid "ALVARADO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_05
msgid "AMAGÁ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_06
msgid "AMALFI"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_965
msgid "AMBALEMA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_463
msgid "ANAPOIMA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_721
msgid "ANCUYÁ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1011
#: model:res.city,name:l10n_co_edi.city_co_1012
msgid "ANDALUCÍA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_07
msgid "ANDES"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_08
msgid "ANGELÓPOLIS"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_09
msgid "ANGOSTURA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_464
msgid "ANOLAIMA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_10
msgid "ANORÍ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_323
msgid "ANSERMA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1013
msgid "ANSERMANUEVO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_966
msgid "ANZOÁTEGUI"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_12
msgid "ANZÁ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_13
msgid "APARTADÓ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_535
msgid "APULO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_835
msgid "APÍA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_200
msgid "AQUITANIA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_661
msgid "ARACATACA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_324
msgid "ARANZAZU"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_851
msgid "ARATOCA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1053
msgid "ARAUCA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1054
msgid "ARAUQUITA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_465
msgid "ARBELÁEZ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_722
msgid "ARBOLEDA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_784
msgid "ARBOLEDAS"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_14
msgid "ARBOLETES"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_201
msgid "ARCABUCO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_154
msgid "ARENAL"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1014
#: model:res.city,name:l10n_co_edi.city_co_15
#: model:res.city,name:l10n_co_edi.city_co_366
msgid "ARGELIA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_662
msgid "ARIGUANÍ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_155
msgid "ARJONA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_16
#: model:res.city,name:l10n_co_edi.city_co_822
msgid "ARMENIA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_967
msgid "ARMERO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_156
msgid "ARROYOHONDO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_409
msgid "ASTREA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_968
msgid "ATACO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_580
msgid "ATRATO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_432
msgid "AYAPEL"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields,field_description:l10n_co_edi.field_res_company__l10n_co_edi_account
#: model:ir.model.fields,field_description:l10n_co_edi.field_res_config_settings__l10n_co_edi_account
msgid "Account ID"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model,name:l10n_co_edi.model_account_move_reversal
msgid "Account Move Reversal"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields.selection,name:l10n_co_edi.selection__account_move__l10n_co_edi_invoice_status__accepted
msgid "Aceptado"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields,field_description:l10n_co_edi.field_res_company__l10n_co_edi_header_actividad_economica
#: model:ir.model.fields,field_description:l10n_co_edi.field_res_config_settings__l10n_co_edi_header_actividad_economica
msgid "Actividad Económica"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model,name:l10n_co_edi.model_account_debit_note
msgid "Add Debit Note wizard"
msgstr ""

#. module: l10n_co_edi
#: code:addons/l10n_co_edi/models/account_invoice.py:0
#, python-format
msgid "All the information on the Customer Fiscal Data section needs to be set."
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields.selection,name:l10n_co_edi.selection__account_move__l10n_co_edi_description_code_credit__2
#: model:ir.model.fields.selection,name:l10n_co_edi.selection__account_move_reversal__l10n_co_edi_description_code_credit__2
msgid "Anulación de factura electrónica"
msgstr ""

#. module: l10n_co_edi
#: model:account.tax.group,name:l10n_co_edi.l10n_co_tax_group_auto_rentencion
msgid "Autorentencion"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields,field_description:l10n_co_edi.field_res_company__l10n_co_edi_header_autorretenedores
#: model:ir.model.fields,field_description:l10n_co_edi.field_res_config_settings__l10n_co_edi_header_autorretenedores
msgid "Autorretenedores"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_581
msgid "BAGADÓ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_582
msgid "BAHÍA SOLANO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_583
msgid "BAJO BAUDÓ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_367
#: model:res.city,name:l10n_co_edi.city_co_836
msgid "BALBOA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_128
msgid "BARANOA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_613
msgid "BARAYA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_723
msgid "BARBACOAS"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_17
#: model:res.city,name:l10n_co_edi.city_co_852
msgid "BARBOSA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_853
msgid "BARICHARA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_691
msgid "BARRANCA DE UPÍA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_854
msgid "BARRANCABERMEJA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_646
msgid "BARRANCAS"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_157
msgid "BARRANCO DE LOBA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1106
msgid "BARRANCO MINAS"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_127
msgid "BARRANQUILLA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_410
msgid "BECERRIL"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_325
msgid "BELALCÁZAR"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_19
msgid "BELLO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_18
msgid "BELMIRA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_466
msgid "BELTRÁN"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_202
#: model:res.city,name:l10n_co_edi.city_co_724
msgid "BELÉN"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_350
msgid "BELÉN DE LOS ANDAQUÍES"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_837
msgid "BELÉN DE UMBRÍA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_203
msgid "BERBEO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_20
msgid "BETANIA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_21
#: model:res.city,name:l10n_co_edi.city_co_855
msgid "BETULIA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_204
msgid "BETÉITIVA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_467
msgid "BITUIMA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_205
msgid "BOAVITA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_785
msgid "BOCHALEMA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_468
msgid "BOJACÁ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_584
msgid "BOJAYÁ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1015
#: model:res.city,name:l10n_co_edi.city_co_368
#: model:res.city,name:l10n_co_edi.city_co_856
msgid "BOLÍVAR"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_411
msgid "BOSCONIA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_206
msgid "BOYACÁ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_207
#: model:res.city,name:l10n_co_edi.city_co_23
msgid "BRICEÑO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_848
msgid "BUCARAMANGA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_786
msgid "BUCARASICA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1016
msgid "BUENAVENTURA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_208
#: model:res.city,name:l10n_co_edi.city_co_433
#: model:res.city,name:l10n_co_edi.city_co_823
#: model:res.city,name:l10n_co_edi.city_co_936
msgid "BUENAVISTA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_369
msgid "BUENOS AIRES"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_725
msgid "BUESACO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1018
msgid "BUGALAGRANDE"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_24
msgid "BURITICÁ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_209
msgid "BUSBANZÁ"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields,field_description:l10n_co_edi.field_res_company__l10n_co_edi_header_bank_information
#: model:ir.model.fields,field_description:l10n_co_edi.field_res_config_settings__l10n_co_edi_header_bank_information
msgid "Bank Information"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_150
msgid "Bogotá D.C."
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields,field_description:l10n_co_edi.field_product_product__l10n_co_edi_brand
#: model:ir.model.fields,field_description:l10n_co_edi.field_product_template__l10n_co_edi_brand
msgid "Brand"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_469
#: model:res.city,name:l10n_co_edi.city_co_857
msgid "CABRERA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_692
msgid "CABUYARO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1111
msgid "CACAHUAL"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_470
msgid "CACHIPAY"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_26
msgid "CAICEDO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1019
msgid "CAICEDONIA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_937
msgid "CAIMITO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_969
msgid "CAJAMARCA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_370
msgid "CAJIBÍO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_471
msgid "CAJICÁ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1115
#: model:res.city,name:l10n_co_edi.city_co_158
msgid "CALAMAR"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_824
msgid "CALARCÁ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_210
#: model:res.city,name:l10n_co_edi.city_co_27
msgid "CALDAS"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_371
msgid "CALDONO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1009
msgid "CALI"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_858
msgid "CALIFORNIA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1020
msgid "CALIMA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_372
msgid "CALOTO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_28
msgid "CAMPAMENTO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_129
msgid "CAMPO DE LA CRUZ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_614
msgid "CAMPOALEGRE"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_211
msgid "CAMPOHERMOSO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_434
msgid "CANALETE"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1021
#: model:res.city,name:l10n_co_edi.city_co_130
msgid "CANDELARIA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_159
msgid "CANTAGALLO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_472
msgid "CAPARRAPÍ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_859
msgid "CAPITANEJO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_30
msgid "CARACOLÍ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_31
msgid "CARAMANTA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_860
msgid "CARCASÍ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_32
msgid "CAREPA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_970
msgid "CARMEN DE APICALÁ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_474
msgid "CARMEN DE CARUPA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_586
msgid "CARMEN DEL DARIÉN"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_34
msgid "CAROLINA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_151
msgid "CARTAGENA DE INDIAS"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_351
msgid "CARTAGENA DEL CHAIRÁ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1022
msgid "CARTAGO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1119
msgid "CARURÚ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_971
msgid "CASABIANCA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_693
msgid "CASTILLA LA NUEVA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_35
msgid "CAUCASIA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_29
msgid "CAÑASGORDAS"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_861
msgid "CEPITÁ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_435
msgid "CERETÉ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_212
msgid "CERINZA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_862
msgid "CERRITO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_663
msgid "CERRO DE SAN ANTONIO"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields.selection,name:l10n_co_edi.selection__res_company__l10n_co_edi_template_code__01
msgid "CGEN03"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields.selection,name:l10n_co_edi.selection__res_company__l10n_co_edi_template_code__02
msgid "CGEN04"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_733
msgid "CHACHAGÜÍ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_475
msgid "CHAGUANÍ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_941
msgid "CHALÁN"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_972
msgid "CHAPARRAL"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_863
msgid "CHARALÁ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_864
msgid "CHARTA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_36
msgid "CHIGORODÓ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_865
msgid "CHIMA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_412
msgid "CHIMICHAGUA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_436
msgid "CHIMÁ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_213
msgid "CHINAVITA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_326
msgid "CHINCHINÁ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_789
msgid "CHINÁCOTA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_437
msgid "CHINÚ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_477
msgid "CHIPAQUE"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_866
msgid "CHIPATÁ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_214
msgid "CHIQUINQUIRÁ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_413
msgid "CHIRIGUANÁ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_215
msgid "CHISCAS"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_216
msgid "CHITA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_790
msgid "CHITAGÁ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_217
msgid "CHITARAQUE"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_218
msgid "CHIVATÁ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_664
msgid "CHIVOLO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_228
msgid "CHIVOR"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_478
msgid "CHOACHÍ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_479
msgid "CHOCONTÁ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1062
msgid "CHÁMEZA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_476
msgid "CHÍA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_227
msgid "CHÍQUIZA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_160
msgid "CICUCO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_867
msgid "CIMITARRA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_825
msgid "CIRCASIA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_37
msgid "CISNEROS"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_22
msgid "CIUDAD BOLÍVAR"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_665
msgid "CIÉNAGA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_438
msgid "CIÉNAGA DE ORO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_219
msgid "CIÉNEGA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_162
msgid "CLEMENCIA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_38
msgid "COCORNÁ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_973
msgid "COELLO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_480
msgid "COGUA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_615
msgid "COLOMBIA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_938
msgid "COLOSÓ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1080
#: model:res.city,name:l10n_co_edi.city_co_726
msgid "COLÓN"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_39
#: model:res.city,name:l10n_co_edi.city_co_868
msgid "CONCEPCIÓN"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_40
#: model:res.city,name:l10n_co_edi.city_co_666
msgid "CONCORDIA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_588
msgid "CONDOTO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_869
msgid "CONFINES"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_727
msgid "CONSACÁ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_728
msgid "CONTADERO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_870
msgid "CONTRATACIÓN"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_791
msgid "CONVENCIÓN"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_41
msgid "COPACABANA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_221
msgid "COPER"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_373
msgid "CORINTO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_871
msgid "COROMORO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_939
msgid "COROZAL"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_222
msgid "CORRALES"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_481
msgid "COTA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_439
msgid "COTORRA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_223
msgid "COVARACHÍA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_940
msgid "COVEÑAS"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_974
msgid "COYAIMA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1055
msgid "CRAVO NORTE"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_730
msgid "CUASPÚD"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_694
msgid "CUBARRAL"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_224
msgid "CUBARÁ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_225
msgid "CUCAITA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_482
msgid "CUCUNUBÁ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_792
msgid "CUCUTILLA"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields,field_description:l10n_co_edi.field_account_bank_statement_line__l10n_co_edi_cufe_cude_ref
#: model:ir.model.fields,field_description:l10n_co_edi.field_account_move__l10n_co_edi_cufe_cude_ref
#: model:ir.model.fields,field_description:l10n_co_edi.field_account_payment__l10n_co_edi_cufe_cude_ref
msgid "CUFE/CUDE"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_695
msgid "CUMARAL"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1127
msgid "CUMARIBO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_731
msgid "CUMBAL"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_732
msgid "CUMBITARA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_975
msgid "CUNDAY"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_352
msgid "CURILLO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_872
msgid "CURITÍ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_414
msgid "CURUMANÍ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_226
msgid "CUÍTIVA"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields.selection,name:l10n_co_edi.selection__account_debit_note__l10n_co_edi_description_code_debit__3
#: model:ir.model.fields.selection,name:l10n_co_edi.selection__account_move__l10n_co_edi_description_code_debit__3
msgid "Cambio del valor"
msgstr ""

#. module: l10n_co_edi
#: code:addons/l10n_co_edi/models/account_invoice.py:0
#, python-format
msgid "Can not generate electronic invoice for %s (id: %s) because it is not validated."
msgstr ""

#. module: l10n_co_edi
#: model:ir.actions.server,name:l10n_co_edi.check_status_electronic_invoice_server_action
msgid "Check Carvajal status"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model,name:l10n_co_edi.model_res_city
msgid "City"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields,field_description:l10n_co_edi.field_l10n_co_edi_payment_option__code
msgid "Code"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields,field_description:l10n_co_edi.field_res_company__l10n_co_edi_template_code
#: model:ir.model.fields,field_description:l10n_co_edi.field_res_config_settings__l10n_co_edi_template_code
msgid "Colombia Template Code"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model,name:l10n_co_edi.model_l10n_co_edi_tax_type
msgid "Colombian EDI Tax Type"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model,name:l10n_co_edi.model_l10n_co_edi_type_code
msgid "Colombian EDI Type Code"
msgstr ""

#. module: l10n_co_edi
#: model:ir.actions.act_window,name:l10n_co_edi.action_type_code
#: model:ir.ui.menu,name:l10n_co_edi.type_code_menu
msgid "Colombian EDI codes"
msgstr ""

#. module: l10n_co_edi
#: model_terms:ir.ui.view,arch_db:l10n_co_edi.product_template_only_form_view_inherit_l10n_co_edi
#: model_terms:ir.ui.view,arch_db:l10n_co_edi.res_config_settings_view_form
msgid "Colombian Electronic Invoicing"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model,name:l10n_co_edi.model_l10n_co_edi_payment_option
msgid "Colombian Payment Options"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields,help:l10n_co_edi.field_account_move_reversal__l10n_co_edi_description_code_credit
msgid "Colombian code for Credit Notes"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields,help:l10n_co_edi.field_account_debit_note__l10n_co_edi_description_code_debit
msgid "Colombian code for Debit Notes"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields,field_description:l10n_co_edi.field_res_partner__l10n_co_edi_commercial_name
#: model:ir.model.fields,field_description:l10n_co_edi.field_res_users__l10n_co_edi_commercial_name
msgid "Commercial Name"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model,name:l10n_co_edi.model_res_company
msgid "Companies"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields,field_description:l10n_co_edi.field_res_company__l10n_co_edi_company
#: model:ir.model.fields,field_description:l10n_co_edi.field_res_config_settings__l10n_co_edi_company
msgid "Company ID"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields,field_description:l10n_co_edi.field_account_move_reversal__l10n_co_edi_description_code_credit
msgid "Concepto"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields,field_description:l10n_co_edi.field_account_bank_statement_line__l10n_co_edi_description_code_credit
#: model:ir.model.fields,field_description:l10n_co_edi.field_account_move__l10n_co_edi_description_code_credit
#: model:ir.model.fields,field_description:l10n_co_edi.field_account_payment__l10n_co_edi_description_code_credit
msgid "Concepto Nota de Credito"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields,field_description:l10n_co_edi.field_account_bank_statement_line__l10n_co_edi_description_code_debit
#: model:ir.model.fields,field_description:l10n_co_edi.field_account_debit_note__l10n_co_edi_description_code_debit
#: model:ir.model.fields,field_description:l10n_co_edi.field_account_move__l10n_co_edi_description_code_debit
#: model:ir.model.fields,field_description:l10n_co_edi.field_account_payment__l10n_co_edi_description_code_debit
msgid "Concepto Nota de Débito"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model,name:l10n_co_edi.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: l10n_co_edi
#: model_terms:ir.ui.view,arch_db:l10n_co_edi.res_config_settings_view_form
msgid "Configure electronic invoice headers here."
msgstr ""

#. module: l10n_co_edi
#: model_terms:ir.ui.view,arch_db:l10n_co_edi.res_config_settings_view_form
msgid "Configure header information here"
msgstr ""

#. module: l10n_co_edi
#: model_terms:ir.ui.view,arch_db:l10n_co_edi.res_config_settings_view_form
msgid "Configure your Carvajal credentials here"
msgstr ""

#. module: l10n_co_edi
#: model_terms:ir.ui.view,arch_db:l10n_co_edi.res_config_settings_view_form
msgid "Configure your Carvajal credentials here."
msgstr ""

#. module: l10n_co_edi
#: code:addons/l10n_co_edi/models/carvajal_request.py:0
#, python-format
msgid "Connection to Carvajal timed out. Their API is probably down."
msgstr ""

#. module: l10n_co_edi
#: model:ir.model,name:l10n_co_edi.model_res_partner
msgid "Contact"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model,name:l10n_co_edi.model_res_country_state
msgid "Country state"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields,field_description:l10n_co_edi.field_l10n_co_edi_payment_option__create_uid
#: model:ir.model.fields,field_description:l10n_co_edi.field_l10n_co_edi_tax_type__create_uid
#: model:ir.model.fields,field_description:l10n_co_edi.field_l10n_co_edi_type_code__create_uid
msgid "Created by"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields,field_description:l10n_co_edi.field_l10n_co_edi_payment_option__create_date
#: model:ir.model.fields,field_description:l10n_co_edi.field_l10n_co_edi_tax_type__create_date
#: model:ir.model.fields,field_description:l10n_co_edi.field_l10n_co_edi_type_code__create_date
msgid "Created on"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields.selection,name:l10n_co_edi.selection__l10n_co_edi_type_code__type__customs
msgid "Customs"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields,field_description:l10n_co_edi.field_product_product__l10n_co_edi_customs_code
#: model:ir.model.fields,field_description:l10n_co_edi.field_product_template__l10n_co_edi_customs_code
msgid "Customs Code"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_25
msgid "CÁCERES"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_788
msgid "CÁCHIRA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_787
msgid "CÁCOTA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_473
msgid "CÁQUEZA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_587
msgid "CÉRTEGUI"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_220
msgid "CÓMBITA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_161
#: model:res.city,name:l10n_co_edi.city_co_729
#: model:res.city,name:l10n_co_edi.city_co_826
msgid "CÓRDOBA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_782
msgid "CÚCUTA"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields,field_description:l10n_co_edi.field_l10n_co_edi_tax_type__code
msgid "Código"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields,field_description:l10n_co_edi.field_uom_uom__l10n_co_edi_ubl
msgid "Código UBL"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_42
msgid "DABEIBA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1023
msgid "DAGUA"
msgstr ""

#. module: l10n_co_edi
#: model_terms:ir.ui.view,arch_db:l10n_co_edi.electronic_invoice_body
msgid "DIAN 2.1"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_647
msgid "DIBULLA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_648
msgid "DISTRACCIÓN"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_976
msgid "DOLORES"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_43
msgid "DONMATÍAS"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_838
msgid "DOSQUEBRADAS"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_229
msgid "DUITAMA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_793
msgid "DURANIA"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields,field_description:l10n_co_edi.field_l10n_co_edi_tax_type__description
msgid "Descripción"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields,field_description:l10n_co_edi.field_l10n_co_edi_type_code__description
msgid "Description"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields.selection,name:l10n_co_edi.selection__account_move__l10n_co_edi_description_code_credit__4
#: model:ir.model.fields.selection,name:l10n_co_edi.selection__account_move_reversal__l10n_co_edi_description_code_credit__4
msgid "Descuento total aplicado"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields.selection,name:l10n_co_edi.selection__account_move__l10n_co_edi_description_code_credit__1
#: model:ir.model.fields.selection,name:l10n_co_edi.selection__account_move_reversal__l10n_co_edi_description_code_credit__1
msgid "Devolución de parte de los bienes; no aceptación de partes del servicio"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields,field_description:l10n_co_edi.field_account_bank_statement_line__l10n_co_edi_is_direct_payment
#: model:ir.model.fields,field_description:l10n_co_edi.field_account_move__l10n_co_edi_is_direct_payment
#: model:ir.model.fields,field_description:l10n_co_edi.field_account_payment__l10n_co_edi_is_direct_payment
msgid "Direct Payment from Colombia"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields,field_description:l10n_co_edi.field_account_debit_note__display_name
#: model:ir.model.fields,field_description:l10n_co_edi.field_account_journal__display_name
#: model:ir.model.fields,field_description:l10n_co_edi.field_account_move__display_name
#: model:ir.model.fields,field_description:l10n_co_edi.field_account_move_line__display_name
#: model:ir.model.fields,field_description:l10n_co_edi.field_account_move_reversal__display_name
#: model:ir.model.fields,field_description:l10n_co_edi.field_account_tax__display_name
#: model:ir.model.fields,field_description:l10n_co_edi.field_account_tax_template__display_name
#: model:ir.model.fields,field_description:l10n_co_edi.field_l10n_co_edi_payment_option__display_name
#: model:ir.model.fields,field_description:l10n_co_edi.field_l10n_co_edi_tax_type__display_name
#: model:ir.model.fields,field_description:l10n_co_edi.field_l10n_co_edi_type_code__display_name
#: model:ir.model.fields,field_description:l10n_co_edi.field_product_template__display_name
#: model:ir.model.fields,field_description:l10n_co_edi.field_res_city__display_name
#: model:ir.model.fields,field_description:l10n_co_edi.field_res_company__display_name
#: model:ir.model.fields,field_description:l10n_co_edi.field_res_config_settings__display_name
#: model:ir.model.fields,field_description:l10n_co_edi.field_res_country_state__display_name
#: model:ir.model.fields,field_description:l10n_co_edi.field_res_partner__display_name
#: model:ir.model.fields,field_description:l10n_co_edi.field_uom_uom__display_name
msgid "Display Name"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields.selection,name:l10n_co_edi.selection__account_move__l10n_co_edi_type__3
msgid "Documento electrónico de transmisión – tipo 03"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_44
msgid "EBÉJICO"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields,field_description:l10n_co_edi.field_res_city__l10n_co_edi_code
msgid "EDI City Code"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields,field_description:l10n_co_edi.field_res_country_state__l10n_co_edi_code
msgid "EDI State Code"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_45
msgid "EL BAGRE"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_667
msgid "EL BANCO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1025
msgid "EL CAIRO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_696
msgid "EL CALVARIO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_585
msgid "EL CANTÓN DEL SAN PABLO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_794
msgid "EL CARMEN"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_589
msgid "EL CARMEN DE ATRATO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_163
msgid "EL CARMEN DE BOLÍVAR"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_873
msgid "EL CARMEN DE CHUCURÍ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_33
msgid "EL CARMEN DE VIBORAL"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_697
msgid "EL CASTILLO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1026
msgid "EL CERRITO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_734
msgid "EL CHARCO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_230
msgid "EL COCUY"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_483
msgid "EL COLEGIO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_415
msgid "EL COPEY"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_353
msgid "EL DONCELLO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_698
msgid "EL DORADO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1027
msgid "EL DOVIO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1095
msgid "EL ENCANTO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_231
msgid "EL ESPINO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_874
msgid "EL GUACAMAYO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_164
msgid "EL GUAMO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_590
msgid "EL LITORAL DEL SAN JUAN"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_649
msgid "EL MOLINO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_416
msgid "EL PASO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_354
msgid "EL PAUJÍL"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_735
msgid "EL PEÑOL"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_165
#: model:res.city,name:l10n_co_edi.city_co_484
#: model:res.city,name:l10n_co_edi.city_co_875
msgid "EL PEÑÓN"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_668
msgid "EL PIÑÓN"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_876
msgid "EL PLAYÓN"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1116
msgid "EL RETORNO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_669
msgid "EL RETÉN"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_942
msgid "EL ROBLE"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_485
msgid "EL ROSAL"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_736
msgid "EL ROSARIO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_104
msgid "EL SANTUARIO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_737
msgid "EL TABLÓN DE GÓMEZ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_374
#: model:res.city,name:l10n_co_edi.city_co_738
msgid "EL TAMBO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_795
msgid "EL TARRA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_796
msgid "EL ZULIA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1024
msgid "EL ÁGUILA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_616
msgid "ELÍAS"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_877
msgid "ENCINO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_878
msgid "ENCISO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_46
msgid "ENTRERRÍOS"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_47
msgid "ENVIGADO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_977
msgid "ESPINAL"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields,field_description:l10n_co_edi.field_account_bank_statement_line__l10n_co_edi_attachment_url
#: model:ir.model.fields,field_description:l10n_co_edi.field_account_move__l10n_co_edi_attachment_url
#: model:ir.model.fields,field_description:l10n_co_edi.field_account_payment__l10n_co_edi_attachment_url
msgid "Electronic Invoice Attachment URL"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields,field_description:l10n_co_edi.field_account_bank_statement_line__l10n_co_edi_invoice_name
#: model:ir.model.fields,field_description:l10n_co_edi.field_account_move__l10n_co_edi_invoice_name
#: model:ir.model.fields,field_description:l10n_co_edi.field_account_payment__l10n_co_edi_invoice_name
msgid "Electronic Invoice Name"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields,field_description:l10n_co_edi.field_account_bank_statement_line__l10n_co_edi_invoice_status
#: model:ir.model.fields,field_description:l10n_co_edi.field_account_move__l10n_co_edi_invoice_status
#: model:ir.model.fields,field_description:l10n_co_edi.field_account_payment__l10n_co_edi_invoice_status
msgid "Electronic Invoice Status"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields,field_description:l10n_co_edi.field_account_bank_statement_line__l10n_co_edi_type
#: model:ir.model.fields,field_description:l10n_co_edi.field_account_move__l10n_co_edi_type
#: model:ir.model.fields,field_description:l10n_co_edi.field_account_payment__l10n_co_edi_type
msgid "Electronic Invoice Type"
msgstr ""

#. module: l10n_co_edi
#: code:addons/l10n_co_edi/models/account_invoice.py:0
#, python-format
msgid "Electronic invoice download failed. Message from Carvajal:<br/>%s"
msgstr ""

#. module: l10n_co_edi
#: code:addons/l10n_co_edi/models/account_invoice.py:0
#, python-format
msgid "Electronic invoice download succeeded. Message from Carvajal:<br/>%s"
msgstr ""

#. module: l10n_co_edi
#: code:addons/l10n_co_edi/models/account_invoice.py:0
#, python-format
msgid "Electronic invoice status check completed. Message from Carvajal:<br/>Status: %s"
msgstr ""

#. module: l10n_co_edi
#: code:addons/l10n_co_edi/models/account_invoice.py:0
#, python-format
msgid "Electronic invoice status check failed. Message from Carvajal:<br/>%s"
msgstr ""

#. module: l10n_co_edi
#: code:addons/l10n_co_edi/models/account_invoice.py:0
#, python-format
msgid "Electronic invoice submission failed. Message from Carvajal:<br/>%s"
msgstr ""

#. module: l10n_co_edi
#: code:addons/l10n_co_edi/models/account_invoice.py:0
#, python-format
msgid "Electronic invoice submission succeeded. Message from Carvajal:<br/>%s"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields.selection,name:l10n_co_edi.selection__account_move__l10n_co_edi_invoice_status__processing
msgid "En proceso"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields.selection,name:l10n_co_edi.selection__l10n_co_edi_type_code__type__establishment
msgid "Establishment"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields.selection,name:l10n_co_edi.selection__account_move__l10n_co_edi_operation_type__10
msgid "Estandar"
msgstr ""

#. module: l10n_co_edi
#: code:addons/l10n_co_edi/models/account_invoice.py:0
#, python-format
msgid "Every product on a line should at least have a product code (barcode, internal) set."
msgstr ""

#. module: l10n_co_edi
#: code:addons/l10n_co_edi/models/account_invoice.py:0
#, python-format
msgid "Exportation invoices require custom code in all the products, please fill in this information before validating the invoice"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_486
msgid "FACATATIVÁ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_978
msgid "FALAN"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_327
msgid "FILADELFIA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_827
msgid "FILANDIA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_232
msgid "FIRAVITOBA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_979
msgid "FLANDES"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_348
#: model:res.city,name:l10n_co_edi.city_co_375
msgid "FLORENCIA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_233
msgid "FLORESTA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1028
msgid "FLORIDA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_880
msgid "FLORIDABLANCA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_879
msgid "FLORIÁN"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_650
msgid "FONSECA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1056
msgid "FORTUL"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_488
msgid "FOSCA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_760
msgid "FRANCISCO PIZARRO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_48
msgid "FREDONIA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_980
msgid "FRESNO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_49
msgid "FRONTINO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_699
msgid "FUENTEDEORO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_670
msgid "FUNDACIÓN"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_739
msgid "FUNES"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_489
msgid "FUNZA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_491
msgid "FUSAGASUGÁ"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields.selection,name:l10n_co_edi.selection__account_move__l10n_co_edi_type__2
msgid "Factura de exportación"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields.selection,name:l10n_co_edi.selection__account_move__l10n_co_edi_type__1
msgid "Factura de venta"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields.selection,name:l10n_co_edi.selection__account_move__l10n_co_edi_type__4
msgid "Factura electrónica de Venta - tipo 04"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields,field_description:l10n_co_edi.field_account_journal__l10n_co_edi_dian_authorization_date
msgid "Fecha de Resolución"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields,field_description:l10n_co_edi.field_account_journal__l10n_co_edi_dian_authorization_end_date
msgid "Fecha de finalización Resolución"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields,field_description:l10n_co_edi.field_res_partner__l10n_co_edi_fiscal_regimen
#: model:ir.model.fields,field_description:l10n_co_edi.field_res_users__l10n_co_edi_fiscal_regimen
msgid "Fiscal Regimen"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_487
msgid "FÓMEQUE"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_490
msgid "FÚQUENE"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_492
msgid "GACHALÁ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_493
msgid "GACHANCIPÁ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_234
msgid "GACHANTIVÁ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_494
msgid "GACHETÁ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_131
msgid "GALAPA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_943
msgid "GALERAS"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_881
msgid "GALÁN"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_495
msgid "GAMA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_417
msgid "GAMARRA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_236
msgid "GARAGOA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_617
msgid "GARZÓN"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_618
msgid "GIGANTE"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1029
msgid "GINEBRA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_50
msgid "GIRALDO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_496
msgid "GIRARDOT"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_51
msgid "GIRARDOTA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_883
msgid "GIRÓN"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_418
msgid "GONZÁLEZ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_797
msgid "GRAMALOTE"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_497
#: model:res.city,name:l10n_co_edi.city_co_53
#: model:res.city,name:l10n_co_edi.city_co_700
msgid "GRANADA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_884
msgid "GUACA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_237
msgid "GUACAMAYAS"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1030
msgid "GUACARÍ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_376
msgid "GUACHENÉ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_498
msgid "GUACHETÁ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_740
msgid "GUACHUCAL"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1017
msgid "GUADALAJARA DE BUGA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_54
#: model:res.city,name:l10n_co_edi.city_co_619
#: model:res.city,name:l10n_co_edi.city_co_885
msgid "GUADALUPE"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_499
msgid "GUADUAS"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_741
msgid "GUAITARILLA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_742
msgid "GUALMATÁN"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_671
#: model:res.city,name:l10n_co_edi.city_co_701
msgid "GUAMAL"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_981
msgid "GUAMO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_886
msgid "GUAPOTÁ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_377
msgid "GUAPÍ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_944
msgid "GUARANDA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_55
msgid "GUARNE"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_500
msgid "GUASCA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_56
msgid "GUATAPÉ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_501
msgid "GUATAQUÍ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_502
msgid "GUATAVITA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_238
msgid "GUATEQUE"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_887
msgid "GUAVATÁ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_503
msgid "GUAYABAL DE SÍQUIMA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_504
msgid "GUAYABETAL"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_239
msgid "GUAYATÁ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_505
msgid "GUTIÉRREZ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_839
msgid "GUÁTICA"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields.selection,name:l10n_co_edi.selection__account_debit_note__l10n_co_edi_description_code_debit__2
#: model:ir.model.fields.selection,name:l10n_co_edi.selection__account_move__l10n_co_edi_description_code_debit__2
msgid "Gastos por cobrar"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields,field_description:l10n_co_edi.field_res_company__l10n_co_edi_header_gran_contribuyente
#: model:ir.model.fields,field_description:l10n_co_edi.field_res_config_settings__l10n_co_edi_header_gran_contribuyente
#: model:ir.model.fields,field_description:l10n_co_edi.field_res_partner__l10n_co_edi_large_taxpayer
#: model:ir.model.fields,field_description:l10n_co_edi.field_res_users__l10n_co_edi_large_taxpayer
msgid "Gran Contribuyente"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_882
msgid "GÁMBITA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_235
msgid "GÁMEZA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_828
msgid "GÉNOVA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_52
msgid "GÓMEZ PLATA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_888
msgid "GÜEPSA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_240
msgid "GÜICÁN DE LA SIERRA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_798
msgid "HACARÍ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_166
msgid "HATILLO DE LOBA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_889
msgid "HATO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1063
msgid "HATO COROZAL"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_651
msgid "HATONUEVO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_57
msgid "HELICONIA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_799
msgid "HERRÁN"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_982
msgid "HERVEO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_58
msgid "HISPANIA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_620
msgid "HOBO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_983
msgid "HONDA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_962
msgid "IBAGUÉ"
msgstr ""

#. module: l10n_co_edi
#: model:account.tax.group,name:l10n_co_edi.l10n_co_tax_group_ic
msgid "IC"
msgstr ""

#. module: l10n_co_edi
#: model:account.tax.group,name:l10n_co_edi.l10n_co_tax_group_ica
msgid "ICA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_984
msgid "ICONONZO"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields,field_description:l10n_co_edi.field_account_debit_note__id
#: model:ir.model.fields,field_description:l10n_co_edi.field_account_journal__id
#: model:ir.model.fields,field_description:l10n_co_edi.field_account_move__id
#: model:ir.model.fields,field_description:l10n_co_edi.field_account_move_line__id
#: model:ir.model.fields,field_description:l10n_co_edi.field_account_move_reversal__id
#: model:ir.model.fields,field_description:l10n_co_edi.field_account_tax__id
#: model:ir.model.fields,field_description:l10n_co_edi.field_account_tax_template__id
#: model:ir.model.fields,field_description:l10n_co_edi.field_l10n_co_edi_payment_option__id
#: model:ir.model.fields,field_description:l10n_co_edi.field_l10n_co_edi_tax_type__id
#: model:ir.model.fields,field_description:l10n_co_edi.field_l10n_co_edi_type_code__id
#: model:ir.model.fields,field_description:l10n_co_edi.field_product_template__id
#: model:ir.model.fields,field_description:l10n_co_edi.field_res_city__id
#: model:ir.model.fields,field_description:l10n_co_edi.field_res_company__id
#: model:ir.model.fields,field_description:l10n_co_edi.field_res_config_settings__id
#: model:ir.model.fields,field_description:l10n_co_edi.field_res_country_state__id
#: model:ir.model.fields,field_description:l10n_co_edi.field_res_partner__id
#: model:ir.model.fields,field_description:l10n_co_edi.field_uom_uom__id
msgid "ID"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_743
msgid "ILES"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_744
msgid "IMUÉS"
msgstr ""

#. module: l10n_co_edi
#: model:account.tax.group,name:l10n_co_edi.l10n_co_tax_group_inc
msgid "INC"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_378
msgid "INZÁ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1105
msgid "INÍRIDA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_745
msgid "IPIALES"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_622
msgid "ISNOS"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_591
msgid "ISTMINA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_59
msgid "ITAGÜÍ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_60
msgid "ITUANGO"
msgstr ""

#. module: l10n_co_edi
#: model:account.tax.group,name:l10n_co_edi.l10n_co_tax_group_iva
msgid "IVA 19%"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_241
msgid "IZA"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields.selection,name:l10n_co_edi.selection__account_debit_note__l10n_co_edi_description_code_debit__1
#: model:ir.model.fields.selection,name:l10n_co_edi.selection__account_move__l10n_co_edi_description_code_debit__1
msgid "Intereses"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_379
msgid "JAMBALÓ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1031
#: model:res.city,name:l10n_co_edi.city_co_1032
msgid "JAMUNDÍ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_61
msgid "JARDÍN"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_242
msgid "JENESANO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_243
#: model:res.city,name:l10n_co_edi.city_co_62
msgid "JERICÓ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_506
msgid "JERUSALÉN"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_890
msgid "JESÚS MARÍA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_891
msgid "JORDÁN"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_132
msgid "JUAN DE ACOSTA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_507
msgid "JUNÍN"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_592
msgid "JURADÓ"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model,name:l10n_co_edi.model_account_journal
msgid "Journal"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model,name:l10n_co_edi.model_account_move
msgid "Journal Entry"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model,name:l10n_co_edi.model_account_move_line
msgid "Journal Item"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields,field_description:l10n_co_edi.field_uom_uom__l10n_co_edi_country_code
msgid "L10N Co Edi Country Code"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields,field_description:l10n_co_edi.field_account_bank_statement_line__l10n_co_edi_datetime_invoice
#: model:ir.model.fields,field_description:l10n_co_edi.field_account_move__l10n_co_edi_datetime_invoice
#: model:ir.model.fields,field_description:l10n_co_edi.field_account_payment__l10n_co_edi_datetime_invoice
msgid "L10N Co Edi Datetime Invoice"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_440
msgid "LA APARTADA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_623
msgid "LA ARGENTINA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_892
msgid "LA BELLEZA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_508
msgid "LA CALERA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_245
msgid "LA CAPILLA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_63
msgid "LA CEJA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_840
msgid "LA CELIA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1096
msgid "LA CHORRERA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_746
msgid "LA CRUZ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1033
msgid "LA CUMBRE"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_328
msgid "LA DORADA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_801
msgid "LA ESPERANZA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_64
msgid "LA ESTRELLA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_747
msgid "LA FLORIDA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_419
msgid "LA GLORIA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1110
msgid "LA GUADALUPE"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_420
msgid "LA JAGUA DE IBIRICO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_652
msgid "LA JAGUA DEL PILAR"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_748
msgid "LA LLANADA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_704
msgid "LA MACARENA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_329
msgid "LA MERCED"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_509
msgid "LA MESA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_355
msgid "LA MONTAÑITA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_510
msgid "LA PALMA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_426
#: model:res.city,name:l10n_co_edi.city_co_894
msgid "LA PAZ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1097
msgid "LA PEDRERA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_511
msgid "LA PEÑA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_65
msgid "LA PINTADA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_624
msgid "LA PLATA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_802
msgid "LA PLAYA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1125
msgid "LA PRIMAVERA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1064
msgid "LA SALINA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_380
msgid "LA SIERRA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_829
msgid "LA TEBAIDA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_749
msgid "LA TOLA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1034
#: model:res.city,name:l10n_co_edi.city_co_66
#: model:res.city,name:l10n_co_edi.city_co_750
#: model:res.city,name:l10n_co_edi.city_co_945
msgid "LA UNIÓN"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_247
msgid "LA UVITA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_381
#: model:res.city,name:l10n_co_edi.city_co_512
msgid "LA VEGA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1035
#: model:res.city,name:l10n_co_edi.city_co_1098
#: model:res.city,name:l10n_co_edi.city_co_246
msgid "LA VICTORIA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_841
msgid "LA VIRGINIA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_800
msgid "LABATECA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_244
msgid "LABRANZAGRANDE"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_893
msgid "LANDÁZURI"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_895
msgid "LEBRIJA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_751
msgid "LEIVA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_706
msgid "LEJANÍAS"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_513
msgid "LENGUAZAQUE"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1094
msgid "LETICIA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_67
msgid "LIBORINA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_752
msgid "LINARES"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_593
msgid "LLORÓ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_441
msgid "LORICA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_753
msgid "LOS ANDES"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_442
msgid "LOS CÓRDOBAS"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_946
msgid "LOS PALMITOS"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_803
msgid "LOS PATIOS"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_896
msgid "LOS SANTOS"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_804
msgid "LOURDES"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_133
msgid "LURUACO"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields,field_description:l10n_co_edi.field_account_debit_note____last_update
#: model:ir.model.fields,field_description:l10n_co_edi.field_account_journal____last_update
#: model:ir.model.fields,field_description:l10n_co_edi.field_account_move____last_update
#: model:ir.model.fields,field_description:l10n_co_edi.field_account_move_line____last_update
#: model:ir.model.fields,field_description:l10n_co_edi.field_account_move_reversal____last_update
#: model:ir.model.fields,field_description:l10n_co_edi.field_account_tax____last_update
#: model:ir.model.fields,field_description:l10n_co_edi.field_account_tax_template____last_update
#: model:ir.model.fields,field_description:l10n_co_edi.field_l10n_co_edi_payment_option____last_update
#: model:ir.model.fields,field_description:l10n_co_edi.field_l10n_co_edi_tax_type____last_update
#: model:ir.model.fields,field_description:l10n_co_edi.field_l10n_co_edi_type_code____last_update
#: model:ir.model.fields,field_description:l10n_co_edi.field_product_template____last_update
#: model:ir.model.fields,field_description:l10n_co_edi.field_res_city____last_update
#: model:ir.model.fields,field_description:l10n_co_edi.field_res_company____last_update
#: model:ir.model.fields,field_description:l10n_co_edi.field_res_config_settings____last_update
#: model:ir.model.fields,field_description:l10n_co_edi.field_res_country_state____last_update
#: model:ir.model.fields,field_description:l10n_co_edi.field_res_partner____last_update
#: model:ir.model.fields,field_description:l10n_co_edi.field_uom_uom____last_update
msgid "Last Modified on"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields,field_description:l10n_co_edi.field_l10n_co_edi_payment_option__write_uid
#: model:ir.model.fields,field_description:l10n_co_edi.field_l10n_co_edi_tax_type__write_uid
#: model:ir.model.fields,field_description:l10n_co_edi.field_l10n_co_edi_type_code__write_uid
msgid "Last Updated by"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields,field_description:l10n_co_edi.field_l10n_co_edi_payment_option__write_date
#: model:ir.model.fields,field_description:l10n_co_edi.field_l10n_co_edi_tax_type__write_date
#: model:ir.model.fields,field_description:l10n_co_edi.field_l10n_co_edi_type_code__write_date
msgid "Last Updated on"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_985
msgid "LÉRIDA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_986
msgid "LÍBANO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_382
msgid "LÓPEZ DE MICAY"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_249
msgid "MACANAL"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_897
msgid "MACARAVITA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_68
msgid "MACEO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_514
msgid "MACHETÁ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_515
msgid "MADRID"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_167
msgid "MAGANGUÉ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_754
msgid "MAGÜÍ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_168
msgid "MAHATES"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_653
msgid "MAICAO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_947
msgid "MAJAGUAL"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_134
msgid "MALAMBO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_755
msgid "MALLAMA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_135
msgid "MANATÍ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_654
msgid "MANAURE"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_421
msgid "MANAURE BALCÓN DEL CESAR"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_321
msgid "MANIZALES"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_516
msgid "MANTA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_330
msgid "MANZANARES"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1065
msgid "MANÍ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1107
msgid "MAPIRIPANA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_702
msgid "MAPIRIPÁN"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_169
msgid "MARGARITA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_69
msgid "MARINILLA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_250
msgid "MARIPÍ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_331
msgid "MARMATO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_332
msgid "MARQUETALIA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_842
msgid "MARSELLA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_333
msgid "MARULANDA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_170
msgid "MARÍA LA BAJA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_899
msgid "MATANZA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_01
msgid "MEDELLÍN"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_517
msgid "MEDINA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_594
msgid "MEDIO ATRATO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_595
msgid "MEDIO BAUDÓ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_596
msgid "MEDIO SAN JUAN"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_988
msgid "MELGAR"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_383
msgid "MERCADERES"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_703
msgid "MESETAS"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_356
msgid "MILÁN"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1117
#: model:res.city,name:l10n_co_edi.city_co_251
msgid "MIRAFLORES"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_384
msgid "MIRANDA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1099
msgid "MIRITÍ – PARANÁ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_843
msgid "MISTRATÓ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1118
msgid "MITÚ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1079
msgid "MOCOA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_900
msgid "MOGOTES"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_901
msgid "MOLAGAVITA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_443
msgid "MOMIL"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_172
msgid "MOMPÓS"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_252
msgid "MONGUA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_253
msgid "MONGUÍ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_254
msgid "MONIQUIRÁ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_70
msgid "MONTEBELLO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_171
msgid "MONTECRISTO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_444
msgid "MONTELÍBANO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_830
msgid "MONTENEGRO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1066
msgid "MONTERREY"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_431
msgid "MONTERÍA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_173
#: model:res.city,name:l10n_co_edi.city_co_385
msgid "MORALES"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_357
msgid "MORELIA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1113
msgid "MORICHAL"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_948
msgid "MORROA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_518
#: model:res.city,name:l10n_co_edi.city_co_756
msgid "MOSQUERA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_255
msgid "MOTAVITA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_445
msgid "MOÑITOS"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_989
msgid "MURILLO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_71
msgid "MURINDÓ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_72
msgid "MUTATÁ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_805
msgid "MUTISCUA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_256
msgid "MUZO"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields,help:l10n_co_edi.field_product_product__l10n_co_edi_customs_code
#: model:ir.model.fields,help:l10n_co_edi.field_product_template__l10n_co_edi_customs_code
msgid "Mainly needed for Exportation Invoices"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields.selection,name:l10n_co_edi.selection__account_move__l10n_co_edi_operation_type__11
msgid "Mandatos"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_898
msgid "MÁLAGA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_519
#: model:res.city,name:l10n_co_edi.city_co_73
#: model:res.city,name:l10n_co_edi.city_co_757
msgid "NARIÑO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_990
msgid "NATAGAIMA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_75
msgid "NECHÍ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_74
msgid "NECOCLÍ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_334
msgid "NEIRA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_607
msgid "NEIVA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_520
msgid "NEMOCÓN"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_521
msgid "NILO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_522
msgid "NIMAIMA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_257
msgid "NOBSA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_523
msgid "NOCAIMA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_335
msgid "NORCASIA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_174
msgid "NOROSÍ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_672
msgid "NUEVA GRANADA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_258
msgid "NUEVO COLÓN"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1067
msgid "NUNCHÍA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_598
msgid "NUQUÍ"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields,field_description:l10n_co_edi.field_l10n_co_edi_tax_type__name
#: model:ir.model.fields,field_description:l10n_co_edi.field_l10n_co_edi_type_code__name
msgid "Name"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields.selection,name:l10n_co_edi.selection__res_partner__l10n_co_edi_fiscal_regimen__49
msgid "No responsables del IVA"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields.selection,name:l10n_co_edi.selection__account_move__l10n_co_edi_invoice_status__not_sent
msgid "Not sent"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields.selection,name:l10n_co_edi.selection__account_move__l10n_co_edi_operation_type__23
msgid "Nota Crédito para facturación electrónica V1 (Decreto 2242)"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields.selection,name:l10n_co_edi.selection__account_move__l10n_co_edi_operation_type__20
msgid "Nota Crédito que referencia una factura electrónica"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields.selection,name:l10n_co_edi.selection__account_move__l10n_co_edi_operation_type__22
msgid "Nota Crédito sin referencia a facturas"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields.selection,name:l10n_co_edi.selection__account_move__l10n_co_edi_operation_type__33
msgid "Nota Débito para facturación electrónica V1 (Decreto 2242)"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields.selection,name:l10n_co_edi.selection__account_move__l10n_co_edi_operation_type__30
msgid "Nota Débito que referencia una factura electrónica"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields.selection,name:l10n_co_edi.selection__account_move__l10n_co_edi_operation_type__32
msgid "Nota Débito sin referencia a facturas"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields,field_description:l10n_co_edi.field_account_bank_statement_line__l10n_co_edi_debit_note
#: model:ir.model.fields,field_description:l10n_co_edi.field_account_journal__l10n_co_edi_debit_note
#: model:ir.model.fields,field_description:l10n_co_edi.field_account_move__l10n_co_edi_debit_note
#: model:ir.model.fields,field_description:l10n_co_edi.field_account_payment__l10n_co_edi_debit_note
msgid "Nota de Débito"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_625
msgid "NÁTAGA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_597
msgid "NÓVITA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1036
msgid "OBANDO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_902
msgid "OCAMONTE"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_806
msgid "OCAÑA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_903
msgid "OIBA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_259
msgid "OICATÁ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_76
msgid "OLAYA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_758
msgid "OLAYA HERRERA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_904
msgid "ONZAGA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_626
msgid "OPORAPA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1081
msgid "ORITO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1068
msgid "OROCUÉ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_991
msgid "ORTEGA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_759
msgid "OSPINA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_260
msgid "OTANCHE"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_949
msgid "OVEJAS"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields,field_description:l10n_co_edi.field_res_partner__l10n_co_edi_obligation_type_ids
#: model:ir.model.fields,field_description:l10n_co_edi.field_res_users__l10n_co_edi_obligation_type_ids
msgid "Obligaciones y Responsabilidades"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields.selection,name:l10n_co_edi.selection__l10n_co_edi_type_code__type__obligation
msgid "Obligation"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields,field_description:l10n_co_edi.field_account_bank_statement_line__l10n_co_edi_operation_type
#: model:ir.model.fields,field_description:l10n_co_edi.field_account_move__l10n_co_edi_operation_type
#: model:ir.model.fields,field_description:l10n_co_edi.field_account_payment__l10n_co_edi_operation_type
msgid "Operation Type"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields.selection,name:l10n_co_edi.selection__account_move__l10n_co_edi_description_code_credit__6
#: model:ir.model.fields.selection,name:l10n_co_edi.selection__account_move_reversal__l10n_co_edi_description_code_credit__6
msgid "Otros"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_261
msgid "PACHAVITA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_525
msgid "PACHO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1120
msgid "PACOA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_386
msgid "PADILLA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_627
msgid "PAICOL"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_422
msgid "PAILITAS"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_526
msgid "PAIME"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_263
msgid "PAIPA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_264
msgid "PAJARITO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_628
msgid "PALERMO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_337
#: model:res.city,name:l10n_co_edi.city_co_629
msgid "PALESTINA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_905
msgid "PALMAR"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_136
msgid "PALMAR DE VARELA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_906
msgid "PALMAS DEL SOCORRO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1037
msgid "PALMIRA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_950
msgid "PALMITO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_992
msgid "PALOCABILDO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_807
msgid "PAMPLONA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_808
msgid "PAMPLONITA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1112
msgid "PANA PANA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_527
msgid "PANDI"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_265
msgid "PANQUEBA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1122
msgid "PAPUNAHUA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_528
msgid "PARATEBUENO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_529
msgid "PASCA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_718
msgid "PASTO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_388
msgid "PATÍA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_266
msgid "PAUNA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_267
msgid "PAYA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1069
msgid "PAZ DE ARIPORO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_268
msgid "PAZ DE RÍO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_673
msgid "PEDRAZA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_423
msgid "PELAYA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_338
msgid "PENSILVANIA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_78
msgid "PEQUE"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_834
msgid "PEREIRA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_269
msgid "PESCA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_77
msgid "PEÑOL"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_389
msgid "PIAMONTE"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_908
msgid "PIEDECUESTA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_993
msgid "PIEDRAS"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_390
msgid "PIENDAMÓ – TUNÍA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_831
msgid "PIJAO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_674
msgid "PIJIÑO DEL CARMEN"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_909
msgid "PINCHOTE"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_175
msgid "PINILLOS"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_137
msgid "PIOJÓ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_270
msgid "PISBA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_630
msgid "PITAL"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_631
msgid "PITALITO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_675
msgid "PIVIJAY"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_994
msgid "PLANADAS"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_446
msgid "PLANETA RICA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_676
msgid "PLATO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_761
msgid "POLICARPA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_138
msgid "POLONUEVO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_139
msgid "PONEDERA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_364
msgid "POPAYÁN"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1070
msgid "PORE"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_762
msgid "POTOSÍ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1038
msgid "PRADERA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_995
msgid "PRADO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1093
#: model:res.city,name:l10n_co_edi.city_co_763
msgid "PROVIDENCIA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_424
msgid "PUEBLO BELLO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_447
msgid "PUEBLO NUEVO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_844
msgid "PUEBLO RICO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_79
msgid "PUEBLORRICO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_677
msgid "PUEBLOVIEJO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_910
msgid "PUENTE NACIONAL"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_764
msgid "PUERRES"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1100
msgid "PUERTO ALEGRÍA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1101
msgid "PUERTO ARICA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1082
msgid "PUERTO ASÍS"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_80
msgid "PUERTO BERRÍO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_271
msgid "PUERTO BOYACÁ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1083
msgid "PUERTO CAICEDO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1124
msgid "PUERTO CARREÑO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1109
#: model:res.city,name:l10n_co_edi.city_co_140
msgid "PUERTO COLOMBIA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_707
msgid "PUERTO CONCORDIA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_448
msgid "PUERTO ESCONDIDO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_708
msgid "PUERTO GAITÁN"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1084
msgid "PUERTO GUZMÁN"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1085
msgid "PUERTO LEGUÍZAMO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_449
msgid "PUERTO LIBERTADOR"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_710
msgid "PUERTO LLERAS"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_709
msgid "PUERTO LÓPEZ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_81
msgid "PUERTO NARE"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1102
msgid "PUERTO NARIÑO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_911
msgid "PUERTO PARRA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_358
#: model:res.city,name:l10n_co_edi.city_co_711
msgid "PUERTO RICO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1057
msgid "PUERTO RONDÓN"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_530
msgid "PUERTO SALGAR"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1103
#: model:res.city,name:l10n_co_edi.city_co_809
msgid "PUERTO SANTANDER"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_391
msgid "PUERTO TEJADA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_82
msgid "PUERTO TRIUNFO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_912
msgid "PUERTO WILCHES"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_531
msgid "PULÍ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_765
msgid "PUPIALES"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_392
msgid "PURACÉ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_996
msgid "PURIFICACIÓN"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_450
msgid "PURÍSIMA DE LA CONCEPCIÓN"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields,field_description:l10n_co_edi.field_res_company__l10n_co_edi_password
#: model:ir.model.fields,field_description:l10n_co_edi.field_res_config_settings__l10n_co_edi_password
msgid "Password"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields,field_description:l10n_co_edi.field_account_bank_statement_line__l10n_co_edi_payment_option_id
#: model:ir.model.fields,field_description:l10n_co_edi.field_account_move__l10n_co_edi_payment_option_id
#: model:ir.model.fields,field_description:l10n_co_edi.field_account_payment__l10n_co_edi_payment_option_id
#: model:ir.model.fields,field_description:l10n_co_edi.field_l10n_co_edi_payment_option__name
msgid "Payment Option"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model,name:l10n_co_edi.model_product_template
msgid "Product Template"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model,name:l10n_co_edi.model_uom_uom
msgid "Product Unit of Measure"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_336
msgid "PÁCORA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_262
#: model:res.city,name:l10n_co_edi.city_co_387
msgid "PÁEZ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_907
msgid "PÁRAMO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_532
msgid "QUEBRADANEGRA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_533
msgid "QUETAME"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_577
msgid "QUIBDÓ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_832
msgid "QUIMBAYA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_845
msgid "QUINCHÍA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_534
msgid "QUIPILE"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_272
msgid "QUÍPAMA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_810
msgid "RAGONVALIA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_273
msgid "RAMIRIQUÍ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1071
msgid "RECETOR"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_176
#: model:res.city,name:l10n_co_edi.city_co_177
msgid "REGIDOR"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_83
msgid "REMEDIOS"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_678
msgid "REMOLINO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_141
msgid "REPELÓN"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1039
#: model:res.city,name:l10n_co_edi.city_co_712
msgid "RESTREPO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_84
msgid "RETIRO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_536
#: model:res.city,name:l10n_co_edi.city_co_766
msgid "RICAURTE"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_997
msgid "RIOBLANCO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1040
msgid "RIOFRÍO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_644
msgid "RIOHACHA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_85
#: model:res.city,name:l10n_co_edi.city_co_913
msgid "RIONEGRO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_339
#: model:res.city,name:l10n_co_edi.city_co_601
msgid "RIOSUCIO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_340
msgid "RISARALDA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_632
msgid "RIVERA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_767
msgid "ROBERTO PAYÁN"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1041
msgid "ROLDANILLO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_998
msgid "RONCESVALLES"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_275
msgid "RONDÓN"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_393
msgid "ROSAS"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_999
msgid "ROVIRA"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields,field_description:l10n_co_edi.field_account_journal__l10n_co_edi_max_range_number
msgid "Range of numbering (maximum)"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields,field_description:l10n_co_edi.field_account_journal__l10n_co_edi_min_range_number
msgid "Range of numbering (minimum)"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields.selection,name:l10n_co_edi.selection__account_move__l10n_co_edi_description_code_credit__3
#: model:ir.model.fields.selection,name:l10n_co_edi.selection__account_move_reversal__l10n_co_edi_description_code_credit__3
msgid "Rebaja total aplicada"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields.selection,name:l10n_co_edi.selection__account_move__l10n_co_edi_invoice_status__rejected
msgid "Rechazado"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields,help:l10n_co_edi.field_product_product__l10n_co_edi_brand
#: model:ir.model.fields,help:l10n_co_edi.field_product_template__l10n_co_edi_brand
msgid "Reported brand in the Colombian electronic invoice."
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields.selection,name:l10n_co_edi.selection__l10n_co_edi_type_code__type__representation
msgid "Representation"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields.selection,name:l10n_co_edi.selection__account_move__l10n_co_edi_description_code_credit__5
#: model:ir.model.fields.selection,name:l10n_co_edi.selection__account_move_reversal__l10n_co_edi_description_code_credit__5
msgid "Rescisión: nulidad por falta de requisitos"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields,field_description:l10n_co_edi.field_res_company__l10n_co_edi_header_resolucion_aplicable
#: model:ir.model.fields,field_description:l10n_co_edi.field_res_config_settings__l10n_co_edi_header_resolucion_aplicable
msgid "Resolucion Aplicable"
msgstr ""

#. module: l10n_co_edi
#: model_terms:ir.ui.view,arch_db:l10n_co_edi.view_account_journal_form_inherit_l10n_co_edi
msgid "Resolución DIAN"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields,field_description:l10n_co_edi.field_account_journal__l10n_co_edi_dian_authorization_number
msgid "Resolución de Facturación"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields.selection,name:l10n_co_edi.selection__res_partner__l10n_co_edi_fiscal_regimen__48
msgid "Responsable del Impuesto sobre las ventas - IVA"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields,field_description:l10n_co_edi.field_l10n_co_edi_tax_type__retention
msgid "Retencion"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields,field_description:l10n_co_edi.field_res_company__l10n_co_edi_header_retenedores_de_iva
#: model:ir.model.fields,field_description:l10n_co_edi.field_res_config_settings__l10n_co_edi_header_retenedores_de_iva
msgid "Retenedores de IVA"
msgstr ""

#. module: l10n_co_edi
#: model:account.tax.group,name:l10n_co_edi.l10n_co_tax_group_rtefuente
msgid "RteFuente"
msgstr ""

#. module: l10n_co_edi
#: model:account.tax.group,name:l10n_co_edi.l10n_co_tax_group_rteica
msgid "RteICA"
msgstr ""

#. module: l10n_co_edi
#: model:account.tax.group,name:l10n_co_edi.l10n_co_tax_group_rteiva
msgid "RteIVA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_274
msgid "RÁQUIRA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_425
msgid "RÍO DE ORO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_599
msgid "RÍO IRÓ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_600
msgid "RÍO QUITO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_178
msgid "RÍO VIEJO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_914
msgid "SABANA DE TORRES"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_142
msgid "SABANAGRANDE"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1072
#: model:res.city,name:l10n_co_edi.city_co_143
#: model:res.city,name:l10n_co_edi.city_co_86
msgid "SABANALARGA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_679
msgid "SABANAS DE SAN ÁNGEL"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_87
msgid "SABANETA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_276
msgid "SABOYÁ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_451
msgid "SAHAGÚN"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_633
msgid "SALADOBLANCO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_341
#: model:res.city,name:l10n_co_edi.city_co_680
msgid "SALAMINA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_811
msgid "SALAZAR"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1000
msgid "SALDAÑA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_833
msgid "SALENTO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_88
msgid "SALGAR"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_278
msgid "SAMACÁ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_768
msgid "SAMANIEGO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_342
msgid "SAMANÁ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_951
msgid "SAMPUÉS"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_634
msgid "SAN AGUSTÍN"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_427
msgid "SAN ALBERTO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1092
#: model:res.city,name:l10n_co_edi.city_co_915
msgid "SAN ANDRÉS"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_89
msgid "SAN ANDRÉS DE CUERQUÍA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_452
msgid "SAN ANDRÉS DE SOTAVENTO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_779
msgid "SAN ANDRÉS DE TUMACO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_453
msgid "SAN ANTERO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1001
msgid "SAN ANTONIO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_537
msgid "SAN ANTONIO DEL TEQUENDAMA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_916
msgid "SAN BENITO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_952
#: model:res.city,name:l10n_co_edi.city_co_953
msgid "SAN BENITO ABAD"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_538
#: model:res.city,name:l10n_co_edi.city_co_770
msgid "SAN BERNARDO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_454
msgid "SAN BERNARDO DEL VIENTO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_812
msgid "SAN CALIXTO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_455
#: model:res.city,name:l10n_co_edi.city_co_90
msgid "SAN CARLOS"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_713
msgid "SAN CARLOS DE GUAROA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_539
#: model:res.city,name:l10n_co_edi.city_co_813
msgid "SAN CAYETANO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_179
msgid "SAN CRISTÓBAL"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_428
msgid "SAN DIEGO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_279
msgid "SAN EDUARDO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_180
msgid "SAN ESTANISLAO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1108
msgid "SAN FELIPE"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_181
msgid "SAN FERNANDO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1087
#: model:res.city,name:l10n_co_edi.city_co_540
#: model:res.city,name:l10n_co_edi.city_co_91
msgid "SAN FRANCISCO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_917
msgid "SAN GIL"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_182
msgid "SAN JACINTO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_183
msgid "SAN JACINTO DEL CAUCA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_92
msgid "SAN JERÓNIMO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_918
msgid "SAN JOAQUÍN"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_343
msgid "SAN JOSÉ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_93
msgid "SAN JOSÉ DE LA MONTAÑA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_919
msgid "SAN JOSÉ DE MIRANDA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_280
msgid "SAN JOSÉ DE PARE"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_456
msgid "SAN JOSÉ DE URÉ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_359
msgid "SAN JOSÉ DEL FRAGUA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1114
msgid "SAN JOSÉ DEL GUAVIARE"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_602
msgid "SAN JOSÉ DEL PALMAR"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_714
msgid "SAN JUAN DE ARAMA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_954
msgid "SAN JUAN DE BETULIA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_541
msgid "SAN JUAN DE RIOSECO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_94
msgid "SAN JUAN DE URABÁ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_655
msgid "SAN JUAN DEL CESAR"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_184
msgid "SAN JUAN NEPOMUCENO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_715
msgid "SAN JUANITO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_771
msgid "SAN LORENZO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1002
#: model:res.city,name:l10n_co_edi.city_co_95
msgid "SAN LUIS"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_281
msgid "SAN LUIS DE GACENO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1074
msgid "SAN LUIS DE PALENQUE"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_958
msgid "SAN LUIS DE SINCÉ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_955
msgid "SAN MARCOS"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_429
#: model:res.city,name:l10n_co_edi.city_co_716
msgid "SAN MARTÍN"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_185
msgid "SAN MARTÍN DE LOBA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_282
msgid "SAN MATEO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1088
#: model:res.city,name:l10n_co_edi.city_co_920
msgid "SAN MIGUEL"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_283
msgid "SAN MIGUEL DE SEMA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_956
msgid "SAN ONOFRE"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_186
#: model:res.city,name:l10n_co_edi.city_co_772
msgid "SAN PABLO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_284
msgid "SAN PABLO DE BORBUR"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1042
#: model:res.city,name:l10n_co_edi.city_co_957
msgid "SAN PEDRO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_773
msgid "SAN PEDRO DE CARTAGO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_96
msgid "SAN PEDRO DE LOS MILAGROS"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_97
msgid "SAN PEDRO DE URABÁ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_457
msgid "SAN PELAYO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_98
msgid "SAN RAFAEL"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_99
msgid "SAN ROQUE"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_394
msgid "SAN SEBASTIÁN"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_681
msgid "SAN SEBASTIÁN DE BUENAVISTA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_987
msgid "SAN SEBASTIÁN DE MARIQUITA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_921
msgid "SAN VICENTE DE CHUCURÍ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_360
msgid "SAN VICENTE DEL CAGUÁN"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_100
msgid "SAN VICENTE FERRER"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_682
msgid "SAN ZENÓN"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_769
msgid "SANDONÁ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_683
msgid "SANTA ANA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_101
#: model:res.city,name:l10n_co_edi.city_co_774
#: model:res.city,name:l10n_co_edi.city_co_922
msgid "SANTA BÁRBARA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_684
msgid "SANTA BÁRBARA DE PINTO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_187
msgid "SANTA CATALINA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_11
msgid "SANTA FÉ DE ANTIOQUIA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_923
msgid "SANTA HELENA DEL OPÓN"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1003
msgid "SANTA ISABEL"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_144
msgid "SANTA LUCÍA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_659
msgid "SANTA MARTA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_286
#: model:res.city,name:l10n_co_edi.city_co_635
msgid "SANTA MARÍA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_188
#: model:res.city,name:l10n_co_edi.city_co_396
msgid "SANTA ROSA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_846
msgid "SANTA ROSA DE CABAL"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_102
msgid "SANTA ROSA DE OSOS"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_287
msgid "SANTA ROSA DE VITERBO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_189
msgid "SANTA ROSA DEL SUR"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1126
msgid "SANTA ROSALÍA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_288
msgid "SANTA SOFÍA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_775
msgid "SANTACRUZ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_285
msgid "SANTANA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_395
msgid "SANTANDER DE QUILICHAO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1089
#: model:res.city,name:l10n_co_edi.city_co_814
msgid "SANTIAGO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_960
msgid "SANTIAGO DE TOLÚ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_103
msgid "SANTO DOMINGO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_145
msgid "SANTO TOMÁS"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_847
msgid "SANTUARIO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_776
msgid "SAPUYES"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1058
msgid "SARAVENA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_815
msgid "SARDINATA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_542
msgid "SASAIMA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_289
msgid "SATIVANORTE"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_290
msgid "SATIVASUR"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_105
msgid "SEGOVIA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_543
msgid "SESQUILÉ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1043
msgid "SEVILLA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_291
msgid "SIACHOQUE"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_544
msgid "SIBATÉ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1086
msgid "SIBUNDOY"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_816
msgid "SILOS"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_545
msgid "SILVANIA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_397
msgid "SILVIA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_924
msgid "SIMACOTA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_546
msgid "SIMIJACA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_190
msgid "SIMITÍ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_935
msgid "SINCELEJO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_603
msgid "SIPÍ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_685
msgid "SITIONUEVO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_547
msgid "SOACHA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_292
msgid "SOATÁ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_294
msgid "SOCHA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_925
msgid "SOCORRO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_293
msgid "SOCOTÁ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_295
msgid "SOGAMOSO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_361
msgid "SOLANO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_146
msgid "SOLEDAD"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_362
msgid "SOLITA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_296
msgid "SOMONDOCO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_106
msgid "SONSÓN"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_107
msgid "SOPETRÁN"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_191
msgid "SOPLAVIENTO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_548
msgid "SOPÓ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_297
msgid "SORA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_299
msgid "SORACÁ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_298
msgid "SOTAQUIRÁ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_398
msgid "SOTARA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_926
msgid "SUAITA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_147
msgid "SUAN"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_636
msgid "SUAZA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_549
msgid "SUBACHOQUE"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_400
#: model:res.city,name:l10n_co_edi.city_co_927
#: model:res.city,name:l10n_co_edi.city_co_959
msgid "SUCRE"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_550
msgid "SUESCA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_551
msgid "SUPATÁ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_344
msgid "SUPÍA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_928
msgid "SURATÁ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_552
msgid "SUSA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_300
msgid "SUSACÓN"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_301
msgid "SUTAMARCHÁN"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_553
msgid "SUTATAUSA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_302
msgid "SUTATENZA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1004
#: model:res.city,name:l10n_co_edi.city_co_399
msgid "SUÁREZ"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields,field_description:l10n_co_edi.field_res_partner__l10n_co_edi_simplified_regimen
#: model:ir.model.fields,field_description:l10n_co_edi.field_res_users__l10n_co_edi_simplified_regimen
msgid "Simplified Regimen"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1073
msgid "SÁCAMA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_277
msgid "SÁCHICA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_554
msgid "TABIO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_604
msgid "TADÓ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_192
msgid "TALAIGUA NUEVO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_430
msgid "TAMALAMEQUE"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1059
msgid "TAME"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_777
msgid "TAMINANGO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_778
msgid "TANGUA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1121
msgid "TARAIRA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1104
msgid "TARAPACÁ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_109
msgid "TARAZÁ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_637
msgid "TARQUI"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_110
msgid "TARSO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_303
msgid "TASCO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1076
msgid "TAURAMENA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_555
msgid "TAUSA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_639
msgid "TELLO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_556
msgid "TENA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_686
msgid "TENERIFE"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_557
msgid "TENJO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_304
msgid "TENZA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_817
msgid "TEORAMA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_640
msgid "TERUEL"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_638
msgid "TESALIA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_558
msgid "TIBACUY"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_305
msgid "TIBANÁ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_306
msgid "TIBASOSA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_559
msgid "TIBIRITA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_818
msgid "TIBÚ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_458
msgid "TIERRALTA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_641
msgid "TIMANÁ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_402
msgid "TIMBIQUÍ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_401
msgid "TIMBÍO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_307
msgid "TINJACÁ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_308
msgid "TIPACOQUE"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_193
msgid "TIQUISIO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_111
msgid "TITIRIBÍ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_309
msgid "TOCA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_560
msgid "TOCAIMA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_561
msgid "TOCANCIPÁ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_310
msgid "TOGÜÍ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_112
#: model:res.city,name:l10n_co_edi.city_co_819
msgid "TOLEDO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_961
msgid "TOLÚ VIEJO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_929
msgid "TONA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_562
msgid "TOPAIPÍ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_403
msgid "TORIBÍO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1044
msgid "TORO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_312
msgid "TOTA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_404
msgid "TOTORÓ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1077
msgid "TRINIDAD"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1045
msgid "TRUJILLO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_148
msgid "TUBARÁ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_459
msgid "TUCHÍN"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1046
msgid "TULUÁ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_198
msgid "TUNJA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_313
msgid "TUNUNGUÁ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_194
msgid "TURBACO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_195
msgid "TURBANÁ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_113
msgid "TURBO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_314
msgid "TURMEQUÉ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_315
msgid "TUTA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_316
msgid "TUTAZÁ"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model,name:l10n_co_edi.model_account_tax
msgid "Tax"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields,help:l10n_co_edi.field_account_bank_statement_line__l10n_co_edi_datetime_invoice
#: model:ir.model.fields,help:l10n_co_edi.field_account_move__l10n_co_edi_datetime_invoice
#: model:ir.model.fields,help:l10n_co_edi.field_account_payment__l10n_co_edi_datetime_invoice
msgid "Technical field used to store the time of invoice validation."
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields,help:l10n_co_edi.field_account_bank_statement_line__l10n_co_edi_transaction
#: model:ir.model.fields,help:l10n_co_edi.field_account_move__l10n_co_edi_transaction
#: model:ir.model.fields,help:l10n_co_edi.field_account_payment__l10n_co_edi_transaction
msgid "Technical field used to track the status of a submission."
msgstr ""

#. module: l10n_co_edi
#: model:ir.model,name:l10n_co_edi.model_account_tax_template
msgid "Templates for Taxes"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields,field_description:l10n_co_edi.field_res_company__l10n_co_edi_test_mode
#: model:ir.model.fields,field_description:l10n_co_edi.field_res_config_settings__l10n_co_edi_test_mode
msgid "Test mode"
msgstr ""

#. module: l10n_co_edi
#: code:addons/l10n_co_edi/models/account_invoice.py:0
#, python-format
msgid "The invoice wasn't sent to Carvajal as their service is probably not available."
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields,help:l10n_co_edi.field_account_bank_statement_line__l10n_co_edi_invoice_name
#: model:ir.model.fields,help:l10n_co_edi.field_account_move__l10n_co_edi_invoice_name
#: model:ir.model.fields,help:l10n_co_edi.field_account_payment__l10n_co_edi_invoice_name
msgid "The name of the file sent to Carvajal."
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields,help:l10n_co_edi.field_account_bank_statement_line__l10n_co_edi_invoice_status
#: model:ir.model.fields,help:l10n_co_edi.field_account_move__l10n_co_edi_invoice_status
#: model:ir.model.fields,help:l10n_co_edi.field_account_payment__l10n_co_edi_invoice_status
msgid "The status of the document as determined by Carvajal."
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields,field_description:l10n_co_edi.field_res_partner__l10n_co_edi_establishment_type_id
#: model:ir.model.fields,field_description:l10n_co_edi.field_res_users__l10n_co_edi_establishment_type_id
msgid "Tipo Establecimiento"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields,field_description:l10n_co_edi.field_res_partner__l10n_co_edi_representation_type_id
#: model:ir.model.fields,field_description:l10n_co_edi.field_res_users__l10n_co_edi_representation_type_id
msgid "Tipo de Representación"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields,field_description:l10n_co_edi.field_res_company__l10n_co_edi_header_tipo_de_regimen
#: model:ir.model.fields,field_description:l10n_co_edi.field_res_config_settings__l10n_co_edi_header_tipo_de_regimen
msgid "Tipo de Régimen"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields,field_description:l10n_co_edi.field_account_tax__l10n_co_edi_type
#: model:ir.model.fields,field_description:l10n_co_edi.field_account_tax_template__l10n_co_edi_type
msgid "Tipo de Valor"
msgstr ""

#. module: l10n_co_edi
#: model:ir.actions.act_window,name:l10n_co_edi.action_tax_type
#: model:ir.ui.menu,name:l10n_co_edi.tax_type_menu
msgid "Tipo de Valor en Factura"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields,field_description:l10n_co_edi.field_account_bank_statement_line__l10n_co_edi_transaction
#: model:ir.model.fields,field_description:l10n_co_edi.field_account_move__l10n_co_edi_transaction
#: model:ir.model.fields,field_description:l10n_co_edi.field_account_payment__l10n_co_edi_transaction
msgid "Transaction ID"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields,field_description:l10n_co_edi.field_l10n_co_edi_type_code__type
msgid "Type"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1075
msgid "TÁMARA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_108
msgid "TÁMESIS"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_311
msgid "TÓPAGA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_780
msgid "TÚQUERRES"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_563
msgid "UBALÁ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_564
msgid "UBAQUE"
msgstr ""

#. module: l10n_co_edi
#: model_terms:ir.ui.view,arch_db:l10n_co_edi.electronic_invoice_body
msgid "UBL 2.1"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1047
msgid "ULLOA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_566
msgid "UNE"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_605
msgid "UNGUÍA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_606
msgid "UNIÓN PANAMERICANA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_114
msgid "URAMITA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_705
msgid "URIBE"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_656
msgid "URIBIA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_115
msgid "URRAO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_657
msgid "URUMITA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_149
msgid "USIACURÍ"
msgstr ""

#. module: l10n_co_edi
#: model:ir.actions.server,name:l10n_co_edi.upload_electronic_invoice_server_action
msgid "Upload to Carvajal"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields,field_description:l10n_co_edi.field_res_company__l10n_co_edi_username
#: model:ir.model.fields,field_description:l10n_co_edi.field_res_config_settings__l10n_co_edi_username
msgid "Username"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields,field_description:l10n_co_edi.field_res_partner__l10n_co_edi_customs_type_ids
#: model:ir.model.fields,field_description:l10n_co_edi.field_res_users__l10n_co_edi_customs_type_ids
msgid "Usuario Aduanero"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_116
msgid "VALDIVIA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_460
msgid "VALENCIA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_930
msgid "VALLE DE SAN JOSÉ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1005
msgid "VALLE DE SAN JUAN"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1090
msgid "VALLE DEL GUAMUEZ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_406
msgid "VALLEDUPAR"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_117
#: model:res.city,name:l10n_co_edi.city_co_363
msgid "VALPARAÍSO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_118
msgid "VEGACHÍ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1006
msgid "VENADILLO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_119
#: model:res.city,name:l10n_co_edi.city_co_120
#: model:res.city,name:l10n_co_edi.city_co_524
msgid "VENECIA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_318
msgid "VENTAQUEMADA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_568
msgid "VERGARA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1048
msgid "VERSALLES"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_932
msgid "VETAS"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_569
msgid "VIANÍ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_345
msgid "VICTORIA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_121
msgid "VIGÍA DEL FUERTE"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1049
msgid "VIJES"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_820
msgid "VILLA CARO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_248
msgid "VILLA DE LEYVA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_565
msgid "VILLA DE SAN DIEGO DE UBATÉ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_821
msgid "VILLA DEL ROSARIO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_405
msgid "VILLA RICA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1091
msgid "VILLAGARZÓN"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_570
msgid "VILLAGÓMEZ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1007
msgid "VILLAHERMOSA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_346
msgid "VILLAMARÍA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1078
#: model:res.city,name:l10n_co_edi.city_co_196
#: model:res.city,name:l10n_co_edi.city_co_658
#: model:res.city,name:l10n_co_edi.city_co_933
msgid "VILLANUEVA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_571
msgid "VILLAPINZÓN"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1008
msgid "VILLARRICA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_689
msgid "VILLAVICENCIO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_642
msgid "VILLAVIEJA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_572
msgid "VILLETA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_573
msgid "VIOTÁ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_319
msgid "VIRACACHÁ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_717
msgid "VISTAHERMOSA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_347
msgid "VITERBO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_931
msgid "VÉLEZ"
msgstr ""

#. module: l10n_co_edi
#: model:ir.model.fields,help:l10n_co_edi.field_account_bank_statement_line__l10n_co_edi_attachment_url
#: model:ir.model.fields,help:l10n_co_edi.field_account_move__l10n_co_edi_attachment_url
#: model:ir.model.fields,help:l10n_co_edi.field_account_payment__l10n_co_edi_attachment_url
msgid ""
"Will be included in electronic invoice and can point to\n"
"                                             e.g. a ZIP containing additional information about the invoice."
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_574
msgid "YACOPÍ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_781
msgid "YACUANQUER"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_643
msgid "YAGUARÁ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_122
msgid "YALÍ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_123
msgid "YARUMAL"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1123
msgid "YAVARATÉ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_124
msgid "YOLOMBÓ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_125
msgid "YONDÓ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1060
msgid "YOPAL"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1050
msgid "YOTOCO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1051
msgid "YUMBO"
msgstr ""

#. module: l10n_co_edi
#: code:addons/l10n_co_edi/models/account_invoice.py:0
#, python-format
msgid "You can not Download Electronic Invoice for Vendor Bill and Vendor Credit Note."
msgstr ""

#. module: l10n_co_edi
#: code:addons/l10n_co_edi/models/account_invoice.py:0
#, python-format
msgid "You can not validate an invoice that has a partner without VAT number."
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_197
msgid "ZAMBRANO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_934
msgid "ZAPATOCA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_687
msgid "ZAPAYÁN"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_126
msgid "ZARAGOZA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_1052
msgid "ZARZAL"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_320
msgid "ZETAQUIRA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_575
msgid "ZIPACÓN"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_576
msgid "ZIPAQUIRÁ"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_688
msgid "ZONA BANANERA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_783
msgid "ÁBREGO"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_621
msgid "ÍQUIRA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_317
msgid "ÚMBITA"
msgstr ""

#. module: l10n_co_edi
#: model:res.city,name:l10n_co_edi.city_co_567
msgid "ÚTICA"
msgstr ""

#. module: l10n_co_edi
#: code:addons/l10n_co_edi/models/account_invoice.py:0
#, python-format
msgid "The issue date can not be older than 5 days or more than 5 days in the future"
msgstr ""
