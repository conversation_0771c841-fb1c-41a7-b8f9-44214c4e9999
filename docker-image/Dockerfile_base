FROM docker.io/ubuntu:jammy
ARG PYTHONBIN=python3.10
LABEL org.opencontainers.image.authors="<EMAIL>"

ENV \
  ODOO_VERSION=16.0 \
  ODOO_BIN=odoo \
  LANG=C.UTF-8 \
  LC_ALL=C.UTF-8 \
  DEBIAN_FRONTEND=noninteractive \
  KWKHTMLTOPDF_SERVER_URL=http://kwkhtmltopdf:8080

# odoo data dir (filestore, etc)
RUN mkdir -p /data/odoo
VOLUME ["/data/odoo"]

# default uid if no arg is provided
ARG ODOO_UID=1000
# Using useradd and no-log-init because of https://stackoverflow.com/questions/73351423/docker-build-hangs-when-adding-user-with-a-large-value-of-user-id-uid
RUN addgroup --gid $ODOO_UID odoo \
    && useradd --no-log-init -u $ODOO_UID -g $ODOO_UID -d /odoo -m -s /bin/sh -c "" odoo \
    && chown -R odoo:odoo /data/odoo

# Odoo Source files (via Git Submodule) CE + EE
COPY ./src-odoo-ce /odoo/src/odoo
COPY ./src-odoo-ee /odoo/src/enterprise

ENV ADDONS_PATH="/odoo/src/odoo/addons, \
    /odoo/src/odoo/odoo/addons, \
    /odoo/src/enterprise, \
    /odoo/addons/3rdparty, \
    /odoo/addons/oca, \
    /odoo/addons/custom-oca, \
    /odoo/addons/custom"

COPY ./docker-image/install /tmp/install
RUN set -x \
  && /tmp/install/pre-install.sh \
  && /tmp/install/tools.sh \
  && /tmp/install/python3.sh \
  && /tmp/install/wkhtmltopdf.sh \
  && /tmp/install/dockerize.sh \
  && /tmp/install/aws_cli.sh \
  && /tmp/install/post-install-clean.sh

# bin scripts and entrypoint
COPY ./docker-image/start-entrypoint.d /odoo/start-entrypoint.d
COPY ./docker-image/entrypoint.sh \
     ./docker-image/bin/create_conf.sh \
     ./docker-image/bin/start-entrypoint.d.sh \
     ./docker-image/bin/wait_postgres.sh \
     ./docker-image/bin/backup.sh \
     ./docker-image/bin/restore.sh \
     ./docker-image/bin/anonymize.sh \
     ./docker-image/bin/anonymizer.py \
     /usr/local/bin/
RUN chmod +x /usr/local/bin/*

# isolate from system python libraries
RUN set -x \
  && $PYTHONBIN -m venv /odoo \
  && /odoo/bin/pip install -U pip==25.1.1 wheel setuptools
ENV PATH=/odoo/bin:$PATH

# Python packages
COPY ./requirements.txt /odoo/
RUN \
  pip install -q --no-cache-dir \
    -r /odoo/requirements.txt \
    -f https://wheelhouse.acsone.eu/manylinux2014 \
  && pip install -q -e /odoo/src/odoo

# Temporary bugfix patches to Odoo's core files (only on rare occasions where a module-based fix is not possible)
# TODO: Remove when official fixes are released
COPY patches/*.patch /tmp/
RUN for patch in /tmp/*.patch; do \
        patch -p1 -d /odoo/src/odoo < "$patch" && rm "$patch"; \
    done

# odoo config file
COPY ./docker-image/config /odoo/templates
ENV OPENERP_SERVER=/odoo/odoo.cfg
ENV ODOO_RC=/odoo/odoo.cfg

# Set Git SHA for reference in image
ARG COMMIT_SHA
ENV COMMIT_SHA=${COMMIT_SHA:-unknown}

USER odoo
EXPOSE 8069 8072

ENTRYPOINT ["/usr/local/bin/entrypoint.sh"]
CMD ["odoo"]