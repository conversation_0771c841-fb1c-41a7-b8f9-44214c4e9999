# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* web_enterprise
# 
# Translators:
# Qaidjohar <PERSON>bha<PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-10 09:00+0000\n"
"PO-Revision-Date: 2022-09-22 05:49+0000\n"
"Last-Translator: Qaidjohar Barbhaya, 2023\n"
"Language-Team: Gujarati (https://app.transifex.com/odoo/teams/41243/gu/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: gu\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.js:0
#, python-format
msgid "%s days"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/settings_form_view/res_config_edition.xml:0
#, python-format
msgid "(Enterprise Edition)"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.js:0
#, python-format
msgid "1 month"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/legacy/xml/base_mobile.xml:0
#, python-format
msgid "Action"
msgstr "Action"

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/legacy/xml/promote_studio.xml:0
#: code:addons/web_enterprise/static/src/views/list/list_renderer_desktop.xml:0
#, python-format
msgid "Add Custom Field"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/legacy/xml/promote_studio.xml:0
#: code:addons/web_enterprise/static/src/webclient/promote_studio_dialog/promote_studio_dialog.xml:0
#, python-format
msgid "Add new fields and much more with"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/legacy/xml/search_panel.xml:0
#, python-format
msgid "All"
msgstr "All"

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/legacy/xml/control_panel.xml:0
#, python-format
msgid "CLEAR"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/legacy/js/views/view_dialogs.js:0
#, python-format
msgid "Clear"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/legacy/xml/promote_studio.xml:0
#: code:addons/web_enterprise/static/src/webclient/promote_studio_dialog/promote_studio_dialog.xml:0
#, python-format
msgid "Close"
msgstr "Close"

#. module: web_enterprise
#: model:ir.model,name:web_enterprise.model_res_partner
msgid "Contact"
msgstr "Contact"

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.xml:0
#, python-format
msgid ""
"Contact your sales representative to help you to unlink your previous "
"database"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/legacy/xml/control_panel.xml:0
#, python-format
msgid "Control panel toolbar"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/color_scheme/color_scheme_menu_items.js:0
#, python-format
msgid "Dark Mode"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/settings_form_view/res_config_edition.xml:0
#, python-format
msgid "Database expiration:"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.xml:0
#, python-format
msgid "Dismiss"
msgstr ""

#. module: web_enterprise
#: model:ir.actions.server,name:web_enterprise.download_contact
msgid "Download (vCard)"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.xml:0
#, python-format
msgid "Error reason:"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/legacy/xml/control_panel.xml:0
#: code:addons/web_enterprise/static/src/legacy/xml/search_panel.xml:0
#, python-format
msgid "FILTER"
msgstr ""

#. module: web_enterprise
#: model:ir.model,name:web_enterprise.model_ir_http
msgid "HTTP Routing"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/navbar/navbar.js:0
#, python-format
msgid "Home menu"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.xml:0
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.xml:0
#, python-format
msgid "I paid, please recheck!"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/legacy/xml/promote_studio.xml:0
#: code:addons/web_enterprise/static/src/webclient/promote_studio_dialog/promote_studio_dialog.xml:0
#, python-format
msgid "Install Odoo Studio"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.xml:0
#, python-format
msgid "Log in as an administrator to correct the issue."
msgstr ""

#. module: web_enterprise
#: model:ir.model,name:web_enterprise.model_ir_ui_menu
msgid "Menu"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/legacy/xml/web_calendar.xml:0
#, python-format
msgid "No filter available"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/home_menu.xml:0
#, python-format
msgid "No result"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/settings_form_view/res_config_edition.xml:0
#, python-format
msgid "Odoo"
msgstr "Odoo"

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/settings_form_view/res_config_edition.xml:0
#, python-format
msgid "Odoo Enterprise Edition License V1.0"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/legacy/xml/promote_studio.xml:0
#: code:addons/web_enterprise/static/src/webclient/promote_studio_dialog/promote_studio_dialog.xml:0
#, python-format
msgid "Odoo Studio"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.xml:0
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.xml:0
#, python-format
msgid "Odoo Support"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.xml:0
#, python-format
msgid "Paste code here"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/navbar/navbar.js:0
#, python-format
msgid "Previous view"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/legacy/js/views/barcode_fields.js:0
#, python-format
msgid "Quick search: %s"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.js:0
#, python-format
msgid "Register"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.xml:0
#, python-format
msgid "Register your subscription"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/legacy/js/views/view_dialogs.js:0
#, python-format
msgid "Remove"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.xml:0
#, python-format
msgid "Renew your subscription"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.js:0
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.js:0
#, python-format
msgid "Retry"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/legacy/xml/control_panel.xml:0
#: code:addons/web_enterprise/static/src/legacy/xml/search_panel.xml:0
#, python-format
msgid "SEE RESULT"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/legacy/xml/barcode_fields.xml:0
#: code:addons/web_enterprise/static/src/legacy/xml/barcode_fields.xml:0
#, python-format
msgid "Scan barcode"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.xml:0
#, python-format
msgid "Send an email"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.xml:0
#, python-format
msgid "Sending the instructions by email ..."
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.xml:0
#, python-format
msgid ""
"Something went wrong while registering your database. You can try again or "
"contact"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.xml:0
#, python-format
msgid "Subscription Code:"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/home_menu.xml:0
#, python-format
msgid "TIP"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.xml:0
#, python-format
msgid ""
"Thank you, your registration was successful! Your database is valid until"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/enterprise_subscription_service.js:0
#, python-format
msgid ""
"Thank you, your registration was successful! Your database is valid until "
"%s."
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.xml:0
#, python-format
msgid ""
"The instructions to unlink your subscription from the previous database(s) "
"have been sent"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.js:0
#, python-format
msgid "This database has expired. "
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.js:0
#, python-format
msgid "This database will expire in %s. "
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.js:0
#, python-format
msgid "This demo database will expire in %s. "
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/legacy/xml/control_panel.xml:0
#: code:addons/web_enterprise/static/src/legacy/xml/control_panel.xml:0
#, python-format
msgid "Today"
msgstr "Today"

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.xml:0
#, python-format
msgid "Unable to send the instructions by email, please contact the"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.xml:0
#, python-format
msgid "Upgrade your subscription"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/legacy/xml/control_panel.xml:0
#, python-format
msgid "View switcher"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.xml:0
#, python-format
msgid ""
"You have more users or more apps installed than your subscription allows."
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.xml:0
#, python-format
msgid ""
"You will be able to register your database once you have installed your "
"first app."
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.xml:0
#, python-format
msgid "Your subscription code"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.xml:0
#, python-format
msgid "Your subscription is already linked to a database."
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.xml:0
#, python-format
msgid "Your subscription was updated and is valid until"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.xml:0
#, python-format
msgid "buy a subscription"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.xml:0
#, python-format
msgid "buy a subscription."
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.xml:0
#, python-format
msgid "or"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/expiration_panel.xml:0
#, python-format
msgid "to the subscription owner to confirm the change, enter a new code or"
msgstr ""

#. module: web_enterprise
#. odoo-javascript
#: code:addons/web_enterprise/static/src/webclient/home_menu/home_menu.xml:0
#, python-format
msgid "— open me anywhere with"
msgstr ""
