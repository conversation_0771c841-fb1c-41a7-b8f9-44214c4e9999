# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* helpdesk_account
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON><PERSON> <mikael.a<PERSON><PERSON>@mariaakerberg.com>, 2022
# <PERSON> <and<PERSON>.<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2022
# Lasse L, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-10 08:59+0000\n"
"PO-Revision-Date: 2022-09-22 05:46+0000\n"
"Last-Translator: Lasse L, 2023\n"
"Language-Team: Swedish (https://app.transifex.com/odoo/teams/41243/sv/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sv\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: helpdesk_account
#: model:ir.model,name:helpdesk_account.model_account_move_reversal
msgid "Account Move Reversal"
msgstr "Backa bokföringstransaktion"

#. module: helpdesk_account
#. odoo-python
#: code:addons/helpdesk_account/controllers/portal.py:0
#, python-format
msgid "Credit Note"
msgstr "Kreditfaktura"

#. module: helpdesk_account
#. odoo-python
#: code:addons/helpdesk_account/controllers/portal.py:0
#: code:addons/helpdesk_account/models/helpdesk.py:0
#: model:ir.model.fields,field_description:helpdesk_account.field_helpdesk_ticket__invoice_ids
#: model_terms:ir.ui.view,arch_db:helpdesk_account.helpdesk_ticket_view_form_inherit_helpdesk_invoicing
#, python-format
msgid "Credit Notes"
msgstr "Kreditfakturor"

#. module: helpdesk_account
#: model:ir.model.fields,field_description:helpdesk_account.field_helpdesk_ticket__invoices_count
msgid "Credit Notes Count"
msgstr "Antal kreditfakturor"

#. module: helpdesk_account
#: model:ir.model,name:helpdesk_account.model_helpdesk_ticket
#: model:ir.model.fields,field_description:helpdesk_account.field_account_move_reversal__helpdesk_ticket_id
msgid "Helpdesk Ticket"
msgstr "Kundtjänstärende"

#. module: helpdesk_account
#. odoo-python
#: code:addons/helpdesk_account/wizard/account_move_reversal.py:0
#, python-format
msgid "Helpdesk Ticket #%s"
msgstr "Kundtjänstärende #%s"

#. module: helpdesk_account
#: model_terms:ir.ui.view,arch_db:helpdesk_account.view_account_move_reversal_inherit_helpdesk_account
msgid "Invoices to Refund"
msgstr "Fakturor att återbetala"

#. module: helpdesk_account
#: model:ir.model,name:helpdesk_account.model_account_move
msgid "Journal Entry"
msgstr "Journalanteckning"

#. module: helpdesk_account
#: model:ir.model.fields,field_description:helpdesk_account.field_account_move_reversal__move_ids
msgid "Move"
msgstr "Flytta"

#. module: helpdesk_account
#: model:ir.actions.act_window,name:helpdesk_account.helpdesk_ticket_action_refund
#: model_terms:ir.ui.view,arch_db:helpdesk_account.helpdesk_ticket_view_form_inherit_helpdesk_invoicing
msgid "Refund"
msgstr "Kreditfaktura"

#. module: helpdesk_account
#. odoo-python
#: code:addons/helpdesk_account/wizard/account_move_reversal.py:0
#, python-format
msgid "Refund created"
msgstr "Återbetalning skapad"

#. module: helpdesk_account
#: model:ir.model.fields,field_description:helpdesk_account.field_account_move_reversal__helpdesk_sale_order_id
msgid "Sales Order"
msgstr "Order"

#. module: helpdesk_account
#: model:ir.model.fields,field_description:helpdesk_account.field_account_move_reversal__suitable_move_ids
msgid "Suitable Move"
msgstr "Lämpligt Steg"

#. module: helpdesk_account
#: model:ir.model.fields,field_description:helpdesk_account.field_account_move_reversal__suitable_sale_order_ids
msgid "Suitable Sale Order"
msgstr "Lämplig order"
