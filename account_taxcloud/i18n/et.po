# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_taxcloud
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> Õigus <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON>mets, 2022
# Anna, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.5alpha1+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-09-20 09:48+0000\n"
"PO-Revision-Date: 2022-09-22 05:45+0000\n"
"Last-Translator: Anna, 2023\n"
"Language-Team: Estonian (https://app.transifex.com/odoo/teams/41243/et/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: et\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_taxcloud
#: model_terms:ir.ui.view,arch_db:account_taxcloud.res_config_settings_view_form
msgid "<i class=\"fa fa-fw fa-arrow-right\"/>How to Get Credentials"
msgstr "<i class=\"fa fa-fw fa-arrow-right\"/>Kuidas saada volitusi"

#. module: account_taxcloud
#: model_terms:ir.ui.view,arch_db:account_taxcloud.res_config_settings_view_form
msgid ""
"<i title=\"Import/update TICs from TaxCloud\" role=\"img\" aria-"
"label=\"Import/update TICs from TaxCloud\" class=\"fa fa-refresh fa-fw\"/>"
msgstr ""
"<i title=\"Import/update TICs from TaxCloud\" role=\"img\" aria-"
"label=\"Import/update TICs from TaxCloud\" class=\"fa fa-refresh fa-fw\"/>"

#. module: account_taxcloud
#: model_terms:ir.ui.view,arch_db:account_taxcloud.res_config_settings_view_form
msgid "API ID"
msgstr "API ID"

#. module: account_taxcloud
#: model_terms:ir.ui.view,arch_db:account_taxcloud.res_config_settings_view_form
msgid "API KEY"
msgstr "API VÕTI"

#. module: account_taxcloud
#: model:ir.model,name:account_taxcloud.model_res_company
msgid "Companies"
msgstr "Ettevõtted"

#. module: account_taxcloud
#: model:ir.model,name:account_taxcloud.model_res_config_settings
msgid "Config Settings"
msgstr "Seadistused"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_tic_category__create_uid
msgid "Created by"
msgstr "Loonud"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_tic_category__create_date
msgid "Created on"
msgstr "Loomise kuupäev"

#. module: account_taxcloud
#: model_terms:ir.ui.view,arch_db:account_taxcloud.res_config_settings_view_form
msgid "Default Category"
msgstr "Vaikimisi kategooria"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_res_company__tic_category_id
#: model:ir.model.fields,field_description:account_taxcloud.field_res_config_settings__tic_category_id
msgid "Default TIC Code"
msgstr "Vaikimisi \"TIC\" kood"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_tic_category__display_name
msgid "Display Name"
msgstr "Näidatav nimi"

#. module: account_taxcloud
#: model_terms:ir.ui.view,arch_db:account_taxcloud.account_position_taxcloud_inherit_from_view
msgid ""
"Enable <b>Detect Automatically</b> to automatically use TaxCloud when "
"selling to American customers."
msgstr ""
"Luba <b>Automaatne tuvastamine</b>, TaxCloudi automaatseks kasutamiseks kuid"
" müüd Ameerika klientidele. "

#. module: account_taxcloud
#: model:ir.model,name:account_taxcloud.model_account_fiscal_position
msgid "Fiscal Position"
msgstr "Finantspositsioon"

#. module: account_taxcloud
#: model_terms:ir.ui.view,arch_db:account_taxcloud.account_position_taxcloud_inherit_from_view
#: model_terms:ir.ui.view,arch_db:account_taxcloud.invoice_form_inherit
msgid "Go to Settings."
msgstr "Mine seadistustesse."

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_tic_category__id
msgid "ID"
msgstr "ID"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_account_bank_statement_line__is_taxcloud_configured
#: model:ir.model.fields,field_description:account_taxcloud.field_account_fiscal_position__is_taxcloud_configured
#: model:ir.model.fields,field_description:account_taxcloud.field_account_move__is_taxcloud_configured
#: model:ir.model.fields,field_description:account_taxcloud.field_account_payment__is_taxcloud_configured
#: model:ir.model.fields,field_description:account_taxcloud.field_res_company__is_taxcloud_configured
msgid "Is Taxcloud Configured"
msgstr "Taxcloud on konfigureeritud"

#. module: account_taxcloud
#: model:ir.model,name:account_taxcloud.model_account_move
msgid "Journal Entry"
msgstr "Andmiku kanne"

#. module: account_taxcloud
#: model:ir.model,name:account_taxcloud.model_account_move_line
msgid "Journal Item"
msgstr "Andmiku kanderida"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_tic_category____last_update
msgid "Last Modified on"
msgstr "Viimati muudetud"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_tic_category__write_uid
msgid "Last Updated by"
msgstr "Viimati uuendas"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_tic_category__write_date
msgid "Last Updated on"
msgstr "Viimati uuendatud"

#. module: account_taxcloud
#: code:addons/account_taxcloud/models/taxcloud_request.py:0
#, python-format
msgid ""
"Please configure taxcloud credentials on the current company or use a "
"different fiscal position"
msgstr ""

#. module: account_taxcloud
#: model_terms:ir.ui.view,arch_db:account_taxcloud.account_position_taxcloud_inherit_from_view
#: model_terms:ir.ui.view,arch_db:account_taxcloud.invoice_form_inherit
msgid ""
"Please enter your Taxcloud credentials to compute tax rates automatically."
msgstr ""
"Palun sisestage oma Taxcloud'i tõendid, et arvutada automaatselt "
"maksumäärad."

#. module: account_taxcloud
#: model:ir.model,name:account_taxcloud.model_product_template
msgid "Product"
msgstr "Toode"

#. module: account_taxcloud
#: model:ir.model,name:account_taxcloud.model_product_category
msgid "Product Category"
msgstr "Tootekategooria"

#. module: account_taxcloud
#: model_terms:ir.ui.view,arch_db:account_taxcloud.account_taxcloud_tic_category_search
msgid "Product TIC Categories"
msgstr "Toodete \"TIC\" kategooriad"

#. module: account_taxcloud
#: model:ir.model,name:account_taxcloud.model_product_tic_category
msgid "Product TIC Category"
msgstr "Toodete TIC kategooriad"

#. module: account_taxcloud
#: model:ir.model.fields,help:account_taxcloud.field_res_company__tic_category_id
#: model:ir.model.fields,help:account_taxcloud.field_res_config_settings__tic_category_id
msgid ""
"TIC (Taxability Information Codes) allow to get specific tax rates for each "
"product type. This default value applies if no product is used in the "
"order/invoice, or if no TIC is set on the product or its product category. "
"By default, TaxCloud relies on the TIC *[0] Uncategorized* default referring"
" to general goods and services."
msgstr ""

#. module: account_taxcloud
#: model_terms:ir.ui.view,arch_db:account_taxcloud.account_taxcloud_tic_category_tree
msgid "TIC Category"
msgstr "\"TIC\" kategooria"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_tic_category__code
msgid "TIC Category Code"
msgstr "\"TIC\" kategooria kood"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_category__tic_category_id
#: model_terms:ir.ui.view,arch_db:account_taxcloud.account_taxcloud_tic_category_search
msgid "TIC Code"
msgstr "\"TIC\" kood"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_tic_category__description
msgid "TIC Description"
msgstr "\"TIC\" kirjeldus"

#. module: account_taxcloud
#: model_terms:ir.ui.view,arch_db:account_taxcloud.product_category_view_form_inherit_account_taxcloud
msgid "TaxCloud"
msgstr "TaxCloud"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_res_company__taxcloud_api_id
#: model:ir.model.fields,field_description:account_taxcloud.field_res_config_settings__taxcloud_api_id
msgid "TaxCloud API ID"
msgstr "TaxCloud API ID"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_res_company__taxcloud_api_key
#: model:ir.model.fields,field_description:account_taxcloud.field_res_config_settings__taxcloud_api_key
msgid "TaxCloud API KEY"
msgstr "TaxCloud API VÕTI"

#. module: account_taxcloud
#: model:ir.actions.act_window,name:account_taxcloud.account_taxcloud_tic_category_action
#: model:ir.ui.menu,name:account_taxcloud.menu_taxcloud_tic_category_action
msgid "TaxCloud Categories"
msgstr "TaxCloud kategooriad"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_product__tic_category_id
#: model:ir.model.fields,field_description:account_taxcloud.field_product_template__tic_category_id
#: model_terms:ir.ui.view,arch_db:account_taxcloud.product_category_view_form_inherit_account_taxcloud
#: model_terms:ir.ui.view,arch_db:account_taxcloud.product_template_taxcloud_inherit_form
msgid "TaxCloud Category"
msgstr "TaxCloud kategooria"

#. module: account_taxcloud
#: model:ir.model,name:account_taxcloud.model_account_fiscal_position_template
msgid "Template for Fiscal Position"
msgstr "Finantspositsiooni mall"

#. module: account_taxcloud
#. odoo-python
#: code:addons/account_taxcloud/models/product.py:0
#, python-format
msgid "The Taxcloud Category must be integer."
msgstr ""

#. module: account_taxcloud
#: code:addons/account_taxcloud/models/account_invoice.py:0
#, python-format
msgid ""
"The tax rates have been updated, you may want to check it before validation"
msgstr ""
"Teie maksumäärad on uuendatud, kuid ilmselt soovite enne kinnitamist neid "
"kontrollida"

#. module: account_taxcloud
#: model:ir.model.fields,help:account_taxcloud.field_product_product__tic_category_id
#: model:ir.model.fields,help:account_taxcloud.field_product_template__tic_category_id
msgid ""
"This refers to TIC (Taxability Information Codes), these are used by "
"TaxCloud to compute specific tax rates for each product type. The value set "
"here prevails over the one set on the product category."
msgstr ""

#. module: account_taxcloud
#: model:ir.model.fields,help:account_taxcloud.field_product_category__tic_category_id
msgid ""
"This refers to TIC (Taxability Information Codes), these are used by "
"TaxCloud to compute specific tax rates for each product type. This value is "
"used when no TIC is set on the product. If no value is set here, the default"
" value set in Invoicing settings is used."
msgstr ""

#. module: account_taxcloud
#: code:addons/account_taxcloud/models/account_invoice.py:0
#: code:addons/account_taxcloud/models/res_config_settings.py:0
#, python-format
msgid "Unable to retrieve taxes from TaxCloud: "
msgstr "Makse ei õnnestunud kätte saada TaxCloud-ist:"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_account_bank_statement_line__is_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_account_fiscal_position__is_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_account_fiscal_position_template__is_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_account_move__is_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_account_payment__is_taxcloud
msgid "Use TaxCloud API"
msgstr "Kasuta TaxCloud API"

#. module: account_taxcloud
#: model:ir.model.fields,help:account_taxcloud.field_account_bank_statement_line__is_taxcloud_configured
#: model:ir.model.fields,help:account_taxcloud.field_account_fiscal_position__is_taxcloud_configured
#: model:ir.model.fields,help:account_taxcloud.field_account_move__is_taxcloud_configured
#: model:ir.model.fields,help:account_taxcloud.field_account_payment__is_taxcloud_configured
#: model:ir.model.fields,help:account_taxcloud.field_res_company__is_taxcloud_configured
msgid ""
"Used to determine whether or not to warn the user to configure TaxCloud."
msgstr ""
"Kasutatakse, et määrata, kas hoiatada kasutajat TaxCloudi konfigureerimisest"
" või mitte."

#. module: account_taxcloud
#: code:addons/account_taxcloud/models/account_invoice.py:0
#, python-format
msgid ""
"You cannot cancel an invoice sent to TaxCloud.\n"
"You need to issue a refund (credit note) for it instead.\n"
"This way the tax entries will be cancelled in TaxCloud."
msgstr ""

#. module: account_taxcloud
#: code:addons/account_taxcloud/models/product.py:0
#, python-format
msgid "[%s] %s"
msgstr "[%s] %s"
