# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* spreadsheet_dashboard_crm
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-04-14 06:19+0000\n"
"PO-Revision-Date: 2022-09-29 09:37+0000\n"
"Language-Team: Swahili (https://app.transifex.com/odoo/teams/41243/sw/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sw\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: spreadsheet_dashboard_crm
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_crm/data/files/leads_dashboard.json:0
#: code:addons/spreadsheet_dashboard_crm/data/files/leads_dashboard.json:0
#, python-format
msgid " days"
msgstr ""

#. module: spreadsheet_dashboard_crm
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_crm/data/files/leads_dashboard.json:0
#: code:addons/spreadsheet_dashboard_crm/data/files/leads_dashboard.json:0
#: code:addons/spreadsheet_dashboard_crm/data/files/leads_dashboard.json:0
#: code:addons/spreadsheet_dashboard_crm/data/files/leads_dashboard.json:0
#: code:addons/spreadsheet_dashboard_crm/data/files/leads_dashboard.json:0
#: code:addons/spreadsheet_dashboard_crm/data/files/leads_dashboard.json:0
#: code:addons/spreadsheet_dashboard_crm/data/files/leads_dashboard.json:0
#: code:addons/spreadsheet_dashboard_crm/data/files/leads_dashboard.json:0
#: code:addons/spreadsheet_dashboard_crm/data/files/pipeline_dashboard.json:0
#: code:addons/spreadsheet_dashboard_crm/data/files/pipeline_dashboard.json:0
#: code:addons/spreadsheet_dashboard_crm/data/files/pipeline_dashboard.json:0
#: code:addons/spreadsheet_dashboard_crm/data/files/pipeline_dashboard.json:0
#: code:addons/spreadsheet_dashboard_crm/data/files/pipeline_dashboard.json:0
#: code:addons/spreadsheet_dashboard_crm/data/files/pipeline_dashboard.json:0
#, python-format
msgid "# Leads"
msgstr ""

#. module: spreadsheet_dashboard_crm
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_crm/data/files/leads_dashboard.json:0
#, python-format
msgid "Average Deal Size"
msgstr ""

#. module: spreadsheet_dashboard_crm
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_crm/data/files/leads_dashboard.json:0
#, python-format
msgid "Average deal size"
msgstr ""

#. module: spreadsheet_dashboard_crm
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_crm/data/files/leads_dashboard.json:0
#: code:addons/spreadsheet_dashboard_crm/data/files/leads_dashboard.json:0
#, python-format
msgid "Campaign"
msgstr ""

#. module: spreadsheet_dashboard_crm
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_crm/data/files/pipeline_dashboard.json:0
#: code:addons/spreadsheet_dashboard_crm/data/files/pipeline_dashboard.json:0
#, python-format
msgid "City"
msgstr ""

#. module: spreadsheet_dashboard_crm
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_crm/data/files/leads_dashboard.json:0
#, python-format
msgid "Close Rate"
msgstr ""

#. module: spreadsheet_dashboard_crm
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_crm/data/files/leads_dashboard.json:0
#, python-format
msgid "Close rate"
msgstr ""

#. module: spreadsheet_dashboard_crm
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_crm/data/files/pipeline_dashboard.json:0
#, python-format
msgid "Closed"
msgstr ""

#. module: spreadsheet_dashboard_crm
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_crm/data/files/pipeline_dashboard.json:0
#, python-format
msgid "Closed count"
msgstr ""

#. module: spreadsheet_dashboard_crm
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_crm/data/files/pipeline_dashboard.json:0
#, python-format
msgid "Closed revenue"
msgstr ""

#. module: spreadsheet_dashboard_crm
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_crm/data/files/leads_dashboard.json:0
#: code:addons/spreadsheet_dashboard_crm/data/files/leads_dashboard.json:0
#: code:addons/spreadsheet_dashboard_crm/data/files/pipeline_dashboard.json:0
#: code:addons/spreadsheet_dashboard_crm/data/files/pipeline_dashboard.json:0
#: code:addons/spreadsheet_dashboard_crm/data/files/pipeline_dashboard.json:0
#, python-format
msgid "Country"
msgstr ""

#. module: spreadsheet_dashboard_crm
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_crm/data/files/pipeline_dashboard.json:0
#: code:addons/spreadsheet_dashboard_crm/data/files/pipeline_dashboard.json:0
#, python-format
msgid "Current"
msgstr ""

#. module: spreadsheet_dashboard_crm
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_crm/data/files/leads_dashboard.json:0
#: code:addons/spreadsheet_dashboard_crm/data/files/leads_dashboard.json:0
#, python-format
msgid "Current period"
msgstr ""

#. module: spreadsheet_dashboard_crm
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_crm/data/files/leads_dashboard.json:0
#, python-format
msgid "Days to Assign"
msgstr ""

#. module: spreadsheet_dashboard_crm
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_crm/data/files/leads_dashboard.json:0
#, python-format
msgid "Days to Win"
msgstr ""

#. module: spreadsheet_dashboard_crm
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_crm/data/files/leads_dashboard.json:0
#, python-format
msgid "Days to assign"
msgstr ""

#. module: spreadsheet_dashboard_crm
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_crm/data/files/leads_dashboard.json:0
#, python-format
msgid "Days to win"
msgstr ""

#. module: spreadsheet_dashboard_crm
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_crm/data/files/pipeline_dashboard.json:0
#, python-format
msgid "Expected"
msgstr ""

#. module: spreadsheet_dashboard_crm
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_crm/data/files/pipeline_dashboard.json:0
#, python-format
msgid "Expected Closing"
msgstr ""

#. module: spreadsheet_dashboard_crm
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_crm/data/files/pipeline_dashboard.json:0
#, python-format
msgid "Expected count"
msgstr ""

#. module: spreadsheet_dashboard_crm
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_crm/data/files/pipeline_dashboard.json:0
#, python-format
msgid "Expected revenue"
msgstr ""

#. module: spreadsheet_dashboard_crm
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_crm/data/files/leads_dashboard.json:0
#: code:addons/spreadsheet_dashboard_crm/data/files/pipeline_dashboard.json:0
#, python-format
msgid "KPI"
msgstr ""

#. module: spreadsheet_dashboard_crm
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_crm/data/files/leads_dashboard.json:0
#, python-format
msgid "Leads by Month"
msgstr ""

#. module: spreadsheet_dashboard_crm
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_crm/data/files/leads_dashboard.json:0
#: code:addons/spreadsheet_dashboard_crm/data/files/leads_dashboard.json:0
#, python-format
msgid "Lost Reason"
msgstr ""

#. module: spreadsheet_dashboard_crm
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_crm/data/files/leads_dashboard.json:0
#: code:addons/spreadsheet_dashboard_crm/data/files/leads_dashboard.json:0
#: code:addons/spreadsheet_dashboard_crm/data/files/pipeline_dashboard.json:0
#: code:addons/spreadsheet_dashboard_crm/data/files/pipeline_dashboard.json:0
#, python-format
msgid "Medium"
msgstr ""

#. module: spreadsheet_dashboard_crm
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_crm/data/files/pipeline_dashboard.json:0
#, python-format
msgid "Open opportunities"
msgstr ""

#. module: spreadsheet_dashboard_crm
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_crm/data/files/pipeline_dashboard.json:0
#, python-format
msgid "Opportunity"
msgstr ""

#. module: spreadsheet_dashboard_crm
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_crm/data/files/pipeline_dashboard.json:0
#, python-format
msgid "Percentage closed"
msgstr ""

#. module: spreadsheet_dashboard_crm
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_crm/data/files/leads_dashboard.json:0
#: code:addons/spreadsheet_dashboard_crm/data/files/pipeline_dashboard.json:0
#, python-format
msgid "Period"
msgstr ""

#. module: spreadsheet_dashboard_crm
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_crm/data/files/pipeline_dashboard.json:0
#, python-format
msgid "Pipeline Stages"
msgstr ""

#. module: spreadsheet_dashboard_crm
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_crm/data/files/pipeline_dashboard.json:0
#: code:addons/spreadsheet_dashboard_crm/data/files/pipeline_dashboard.json:0
#, python-format
msgid "Previous"
msgstr ""

#. module: spreadsheet_dashboard_crm
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_crm/data/files/leads_dashboard.json:0
#: code:addons/spreadsheet_dashboard_crm/data/files/leads_dashboard.json:0
#, python-format
msgid "Previous period"
msgstr ""

#. module: spreadsheet_dashboard_crm
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_crm/data/files/leads_dashboard.json:0
#: code:addons/spreadsheet_dashboard_crm/data/files/leads_dashboard.json:0
#: code:addons/spreadsheet_dashboard_crm/data/files/leads_dashboard.json:0
#: code:addons/spreadsheet_dashboard_crm/data/files/leads_dashboard.json:0
#: code:addons/spreadsheet_dashboard_crm/data/files/leads_dashboard.json:0
#: code:addons/spreadsheet_dashboard_crm/data/files/leads_dashboard.json:0
#: code:addons/spreadsheet_dashboard_crm/data/files/leads_dashboard.json:0
#: code:addons/spreadsheet_dashboard_crm/data/files/leads_dashboard.json:0
#: code:addons/spreadsheet_dashboard_crm/data/files/leads_dashboard.json:0
#: code:addons/spreadsheet_dashboard_crm/data/files/leads_dashboard.json:0
#: code:addons/spreadsheet_dashboard_crm/data/files/pipeline_dashboard.json:0
#: code:addons/spreadsheet_dashboard_crm/data/files/pipeline_dashboard.json:0
#: code:addons/spreadsheet_dashboard_crm/data/files/pipeline_dashboard.json:0
#: code:addons/spreadsheet_dashboard_crm/data/files/pipeline_dashboard.json:0
#: code:addons/spreadsheet_dashboard_crm/data/files/pipeline_dashboard.json:0
#: code:addons/spreadsheet_dashboard_crm/data/files/pipeline_dashboard.json:0
#: code:addons/spreadsheet_dashboard_crm/data/files/pipeline_dashboard.json:0
#, python-format
msgid "Revenue"
msgstr ""

#. module: spreadsheet_dashboard_crm
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_crm/data/files/leads_dashboard.json:0
#: code:addons/spreadsheet_dashboard_crm/data/files/leads_dashboard.json:0
#: code:addons/spreadsheet_dashboard_crm/data/files/pipeline_dashboard.json:0
#: code:addons/spreadsheet_dashboard_crm/data/files/pipeline_dashboard.json:0
#, python-format
msgid "Sales Team"
msgstr ""

#. module: spreadsheet_dashboard_crm
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_crm/data/files/leads_dashboard.json:0
#: code:addons/spreadsheet_dashboard_crm/data/files/leads_dashboard.json:0
#: code:addons/spreadsheet_dashboard_crm/data/files/pipeline_dashboard.json:0
#: code:addons/spreadsheet_dashboard_crm/data/files/pipeline_dashboard.json:0
#: code:addons/spreadsheet_dashboard_crm/data/files/pipeline_dashboard.json:0
#, python-format
msgid "Salesperson"
msgstr ""

#. module: spreadsheet_dashboard_crm
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_crm/data/files/leads_dashboard.json:0
#: code:addons/spreadsheet_dashboard_crm/data/files/leads_dashboard.json:0
#: code:addons/spreadsheet_dashboard_crm/data/files/pipeline_dashboard.json:0
#: code:addons/spreadsheet_dashboard_crm/data/files/pipeline_dashboard.json:0
#, python-format
msgid "Source"
msgstr ""

#. module: spreadsheet_dashboard_crm
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_crm/data/files/pipeline_dashboard.json:0
#: code:addons/spreadsheet_dashboard_crm/data/files/pipeline_dashboard.json:0
#, python-format
msgid "Stage"
msgstr ""

#. module: spreadsheet_dashboard_crm
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_crm/data/files/leads_dashboard.json:0
#, python-format
msgid "Tag"
msgstr ""

#. module: spreadsheet_dashboard_crm
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_crm/data/files/leads_dashboard.json:0
#, python-format
msgid "Tags"
msgstr ""

#. module: spreadsheet_dashboard_crm
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_crm/data/files/pipeline_dashboard.json:0
#, python-format
msgid "To close"
msgstr ""

#. module: spreadsheet_dashboard_crm
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_crm/data/files/leads_dashboard.json:0
#, python-format
msgid "Top Campaigns"
msgstr ""

#. module: spreadsheet_dashboard_crm
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_crm/data/files/pipeline_dashboard.json:0
#, python-format
msgid "Top Cities"
msgstr ""

#. module: spreadsheet_dashboard_crm
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_crm/data/files/leads_dashboard.json:0
#: code:addons/spreadsheet_dashboard_crm/data/files/pipeline_dashboard.json:0
#, python-format
msgid "Top Countries"
msgstr ""

#. module: spreadsheet_dashboard_crm
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_crm/data/files/leads_dashboard.json:0
#, python-format
msgid "Top Lost Reasons"
msgstr ""

#. module: spreadsheet_dashboard_crm
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_crm/data/files/leads_dashboard.json:0
#: code:addons/spreadsheet_dashboard_crm/data/files/pipeline_dashboard.json:0
#, python-format
msgid "Top Mediums"
msgstr ""

#. module: spreadsheet_dashboard_crm
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_crm/data/files/pipeline_dashboard.json:0
#, python-format
msgid "Top Opportunities"
msgstr ""

#. module: spreadsheet_dashboard_crm
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_crm/data/files/leads_dashboard.json:0
#: code:addons/spreadsheet_dashboard_crm/data/files/pipeline_dashboard.json:0
#, python-format
msgid "Top Sales Teams"
msgstr ""

#. module: spreadsheet_dashboard_crm
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_crm/data/files/leads_dashboard.json:0
#: code:addons/spreadsheet_dashboard_crm/data/files/pipeline_dashboard.json:0
#, python-format
msgid "Top Salespeople"
msgstr ""

#. module: spreadsheet_dashboard_crm
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_crm/data/files/leads_dashboard.json:0
#: code:addons/spreadsheet_dashboard_crm/data/files/pipeline_dashboard.json:0
#, python-format
msgid "Top Sources"
msgstr ""

#. module: spreadsheet_dashboard_crm
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_crm/data/files/leads_dashboard.json:0
#, python-format
msgid "Top Tags"
msgstr ""

#. module: spreadsheet_dashboard_crm
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_crm/data/files/leads_dashboard.json:0
#, python-format
msgid "Total days to assign"
msgstr ""

#. module: spreadsheet_dashboard_crm
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_crm/data/files/leads_dashboard.json:0
#, python-format
msgid "Total days to win"
msgstr ""

#. module: spreadsheet_dashboard_crm
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_crm/data/files/leads_dashboard.json:0
#, python-format
msgid "Total leads"
msgstr ""

#. module: spreadsheet_dashboard_crm
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_crm/data/files/leads_dashboard.json:0
#, python-format
msgid "Won leads"
msgstr ""

#. module: spreadsheet_dashboard_crm
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_crm/data/files/leads_dashboard.json:0
#, python-format
msgid "average deal size - current"
msgstr ""

#. module: spreadsheet_dashboard_crm
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_crm/data/files/leads_dashboard.json:0
#, python-format
msgid "average deal size - previous"
msgstr ""

#. module: spreadsheet_dashboard_crm
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_crm/data/files/leads_dashboard.json:0
#, python-format
msgid "close rate - current"
msgstr ""

#. module: spreadsheet_dashboard_crm
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_crm/data/files/leads_dashboard.json:0
#, python-format
msgid "close rate - previous"
msgstr ""

#. module: spreadsheet_dashboard_crm
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_crm/data/files/leads_dashboard.json:0
#, python-format
msgid "days to - current"
msgstr ""

#. module: spreadsheet_dashboard_crm
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_crm/data/files/leads_dashboard.json:0
#, python-format
msgid "days to - previous"
msgstr ""

#. module: spreadsheet_dashboard_crm
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_crm/data/files/pipeline_dashboard.json:0
#, python-format
msgid "expected - current"
msgstr ""

#. module: spreadsheet_dashboard_crm
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_crm/data/files/pipeline_dashboard.json:0
#, python-format
msgid "expected - previous"
msgstr ""

#. module: spreadsheet_dashboard_crm
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_crm/data/files/leads_dashboard.json:0
#: code:addons/spreadsheet_dashboard_crm/data/files/leads_dashboard.json:0
#: code:addons/spreadsheet_dashboard_crm/data/files/leads_dashboard.json:0
#, python-format
msgid "last period"
msgstr ""

#. module: spreadsheet_dashboard_crm
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_crm/data/files/pipeline_dashboard.json:0
#, python-format
msgid "list opps"
msgstr ""

#. module: spreadsheet_dashboard_crm
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_crm/data/files/leads_dashboard.json:0
#: code:addons/spreadsheet_dashboard_crm/data/files/leads_dashboard.json:0
#: code:addons/spreadsheet_dashboard_crm/data/files/pipeline_dashboard.json:0
#: code:addons/spreadsheet_dashboard_crm/data/files/pipeline_dashboard.json:0
#, python-format
msgid "since last period"
msgstr ""

#. module: spreadsheet_dashboard_crm
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_crm/data/files/leads_dashboard.json:0
#, python-format
msgid "to campaign"
msgstr ""

#. module: spreadsheet_dashboard_crm
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_crm/data/files/pipeline_dashboard.json:0
#, python-format
msgid "to close"
msgstr ""

#. module: spreadsheet_dashboard_crm
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_crm/data/files/pipeline_dashboard.json:0
#, python-format
msgid "top cities"
msgstr ""

#. module: spreadsheet_dashboard_crm
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_crm/data/files/leads_dashboard.json:0
#: code:addons/spreadsheet_dashboard_crm/data/files/pipeline_dashboard.json:0
#, python-format
msgid "top countries"
msgstr ""

#. module: spreadsheet_dashboard_crm
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_crm/data/files/leads_dashboard.json:0
#, python-format
msgid "top lost reasons"
msgstr ""

#. module: spreadsheet_dashboard_crm
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_crm/data/files/leads_dashboard.json:0
#, python-format
msgid "top medium"
msgstr ""

#. module: spreadsheet_dashboard_crm
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_crm/data/files/pipeline_dashboard.json:0
#, python-format
msgid "top mediums"
msgstr ""

#. module: spreadsheet_dashboard_crm
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_crm/data/files/leads_dashboard.json:0
#: code:addons/spreadsheet_dashboard_crm/data/files/pipeline_dashboard.json:0
#, python-format
msgid "top sales team"
msgstr ""

#. module: spreadsheet_dashboard_crm
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_crm/data/files/pipeline_dashboard.json:0
#, python-format
msgid "top salespeople"
msgstr ""

#. module: spreadsheet_dashboard_crm
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_crm/data/files/leads_dashboard.json:0
#, python-format
msgid "top salesperson"
msgstr ""

#. module: spreadsheet_dashboard_crm
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_crm/data/files/leads_dashboard.json:0
#, python-format
msgid "top source"
msgstr ""

#. module: spreadsheet_dashboard_crm
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_crm/data/files/pipeline_dashboard.json:0
#, python-format
msgid "top sources"
msgstr ""

#. module: spreadsheet_dashboard_crm
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_crm/data/files/leads_dashboard.json:0
#, python-format
msgid "top tags"
msgstr ""
