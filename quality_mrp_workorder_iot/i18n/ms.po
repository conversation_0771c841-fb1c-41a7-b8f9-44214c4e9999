# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* quality_mrp_workorder_iot
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:57+0000\n"
"PO-Revision-Date: 2022-09-22 05:48+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON>, 2022\n"
"Language-Team: Malay (https://app.transifex.com/odoo/teams/41243/ms/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ms\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: quality_mrp_workorder_iot
#: model:ir.model.fields,field_description:quality_mrp_workorder_iot.field_iot_trigger__action
msgid "Action"
msgstr "Tindakan"

#. module: quality_mrp_workorder_iot
#: model:ir.model.fields.selection,name:quality_mrp_workorder_iot.selection__iot_trigger__action__fail
msgid "Fail"
msgstr ""

#. module: quality_mrp_workorder_iot
#: model:ir.model,name:quality_mrp_workorder_iot.model_iot_trigger
msgid "IOT Trigger"
msgstr ""

#. module: quality_mrp_workorder_iot
#: model:ir.model.fields.selection,name:quality_mrp_workorder_iot.selection__iot_trigger__action__pass
msgid "Pass"
msgstr ""

#. module: quality_mrp_workorder_iot
#: model:ir.model.fields.selection,name:quality_mrp_workorder_iot.selection__iot_trigger__action__measure
msgid "Take Measure"
msgstr ""
