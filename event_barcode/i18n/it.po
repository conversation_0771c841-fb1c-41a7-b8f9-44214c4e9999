# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* event_barcode
# 
# Translators:
# <PERSON>, 2022
# <PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-04-14 06:17+0000\n"
"PO-Revision-Date: 2022-09-22 05:46+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Italian (https://app.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: event_barcode
#. odoo-javascript
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#: model:ir.model.fields,field_description:event_barcode.field_event_registration__barcode
#: model_terms:ir.ui.view,arch_db:event_barcode.event_report_template_full_page_ticket_inherit_barcode
#, python-format
msgid "Barcode"
msgstr "Codice a barre"

#. module: event_barcode
#: model:ir.actions.client,name:event_barcode.event_barcode_action_main_view
msgid "Barcode Interface"
msgstr "Interfaccia codice a barre"

#. module: event_barcode
#: model_terms:ir.ui.view,arch_db:event_barcode.res_config_settings_view_form
msgid "Barcode Nomenclature"
msgstr "Nomenclatura codice a barre"

#. module: event_barcode
#: model:ir.model.constraint,message:event_barcode.constraint_event_registration_barcode_event_uniq
msgid "Barcode should be unique"
msgstr "Il codice a barre deve essere univoco."

#. module: event_barcode
#. odoo-javascript
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "Canceled registration"
msgstr "Registrazione annullata"

#. module: event_barcode
#. odoo-javascript
#: code:addons/event_barcode/static/src/js/event_barcode.js:0
#: code:addons/event_barcode/static/src/js/event_barcode.js:0
#, python-format
msgid "Close"
msgstr "Chiudi"

#. module: event_barcode
#. odoo-javascript
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "Company"
msgstr "Azienda"

#. module: event_barcode
#. odoo-javascript
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "Company Logo"
msgstr "Logo azienda"

#. module: event_barcode
#: model:ir.model,name:event_barcode.model_res_config_settings
msgid "Config Settings"
msgstr "Impostazioni di configurazione"

#. module: event_barcode
#. odoo-javascript
#: code:addons/event_barcode/static/src/js/event_barcode.js:0
#, python-format
msgid "Confirm"
msgstr "Conferma"

#. module: event_barcode
#. odoo-javascript
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "Confirm attendance for"
msgstr "Conferma presenza per"

#. module: event_barcode
#. odoo-javascript
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "Event"
msgstr "Evento"

#. module: event_barcode
#: model:ir.model,name:event_barcode.model_event_registration
msgid "Event Registration"
msgstr "Registrazione evento"

#. module: event_barcode
#. odoo-javascript
#: code:addons/event_barcode/static/src/js/event_barcode.js:0
#, python-format
msgid "Events"
msgstr "Eventi"

#. module: event_barcode
#. odoo-javascript
#: code:addons/event_barcode/static/src/js/event_barcode.js:0
#, python-format
msgid "Invalid ticket"
msgstr "Biglietto non valido"

#. module: event_barcode
#. odoo-javascript
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "Name"
msgstr "Nome"

#. module: event_barcode
#: model:ir.model.fields,field_description:event_barcode.field_res_config_settings__barcode_nomenclature_id
msgid "Nomenclature"
msgstr "Nomenclatura"

#. module: event_barcode
#. odoo-javascript
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "Payment"
msgstr "Pagamento"

#. module: event_barcode
#. odoo-javascript
#: code:addons/event_barcode/static/src/js/event_barcode.js:0
#, python-format
msgid "Print"
msgstr "Stampa"

#. module: event_barcode
#. odoo-javascript
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "Registration"
msgstr "Registrazione"

#. module: event_barcode
#. odoo-python
#: code:addons/event_barcode/controllers/main.py:0
#: model:ir.ui.menu,name:event_barcode.menu_event_registration_desk
#: model_terms:ir.ui.view,arch_db:event_barcode.event_event_view_form
#, python-format
msgid "Registration Desk"
msgstr "Banco registrazioni"

#. module: event_barcode
#. odoo-javascript
#: code:addons/event_barcode/static/src/js/event_barcode.js:0
#, python-format
msgid "Registration Summary"
msgstr "Riepilogo registrazione"

#. module: event_barcode
#. odoo-javascript
#: code:addons/event_barcode/static/src/js/event_barcode.js:0
#, python-format
msgid "Registration confirmed"
msgstr "Registrazione confermata"

#. module: event_barcode
#. odoo-javascript
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "Scan a badge"
msgstr "Leggi un tesserino"

#. module: event_barcode
#. odoo-javascript
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "Select Attendee"
msgstr "Seleziona partecipante"

#. module: event_barcode
#. odoo-javascript
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "The registration must be paid"
msgstr "La registrazione deve essere pagata"

#. module: event_barcode
#. odoo-javascript
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "This ticket is for another event"
msgstr "Il biglietto è per un altro evento"

#. module: event_barcode
#. odoo-javascript
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "This ticket is not for an ongoing event"
msgstr "Il biglietto non è per un evento in corso"

#. module: event_barcode
#. odoo-javascript
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "Ticket"
msgstr "Biglietto"

#. module: event_barcode
#. odoo-javascript
#: code:addons/event_barcode/static/src/js/event_barcode.js:0
#, python-format
msgid "View"
msgstr "Visualizza"

#. module: event_barcode
#. odoo-javascript
#: code:addons/event_barcode/static/src/js/event_barcode.js:0
#, python-format
msgid "Warning"
msgstr "Avviso"

#. module: event_barcode
#. odoo-javascript
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "Welcome to"
msgstr "Benvenuto in"

#. module: event_barcode
#. odoo-javascript
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "is already registered"
msgstr "è già registrato"

#. module: event_barcode
#. odoo-javascript
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "is successfully registered"
msgstr "è stato registrato con successo"

#. module: event_barcode
#. odoo-javascript
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "or"
msgstr "oppure"
