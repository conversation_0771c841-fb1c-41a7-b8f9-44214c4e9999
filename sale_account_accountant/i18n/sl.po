# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_account_accountant
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-10 08:59+0000\n"
"PO-Revision-Date: 2022-09-22 05:48+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2025\n"
"Language-Team: Slovenian (https://app.transifex.com/odoo/teams/41243/sl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sl\n"
"Plural-Forms: nplurals=4; plural=(n%100==1 ? 0 : n%100==2 ? 1 : n%100==3 || n%100==4 ? 2 : 3);\n"

#. module: sale_account_accountant
#: model:ir.model,name:sale_account_accountant.model_bank_rec_widget
msgid "Bank reconciliation widget for a single statement line"
msgstr "Pripomoček za uskladitev bančnih računov za posamezno vrstico izpiska"

#. module: sale_account_accountant
#: model:ir.model.fields,field_description:sale_account_accountant.field_bank_rec_widget__matched_sale_order_ids
msgid "Matched Sale Order"
msgstr ""

#. module: sale_account_accountant
#: model:ir.model,name:sale_account_accountant.model_account_reconcile_model
msgid ""
"Preset to create journal entries during a invoices and payments matching"
msgstr ""
"Prednastavljeno za ustvarjanje dnevniških vnosov med usklajevanjem računov "
"in plačil"

#. module: sale_account_accountant
#. odoo-python
#: code:addons/sale_account_accountant/models/bank_rec_widget.py:0
#: model_terms:ir.ui.view,arch_db:sale_account_accountant.view_bank_rec_widget_form_inherit_sale_order
#, python-format
msgid "Sale Orders"
msgstr "Prodajni nalogi"

#. module: sale_account_accountant
#: model_terms:ir.ui.view,arch_db:sale_account_accountant.view_bank_rec_widget_form_inherit_sale_order
msgid "There are"
msgstr "Obstajajo"

#. module: sale_account_accountant
#: model_terms:ir.ui.view,arch_db:sale_account_accountant.view_bank_rec_widget_form_inherit_sale_order
msgid "matching the communication of the bank statement line."
msgstr ""

#. module: sale_account_accountant
#: model_terms:ir.ui.view,arch_db:sale_account_accountant.view_bank_rec_widget_form_inherit_sale_order
msgid "uninvoiced sales orders"
msgstr ""
