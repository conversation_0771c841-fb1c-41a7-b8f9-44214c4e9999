# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_appraisal_survey
# 
# Translators:
# SebastianoPist<PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON>, 2023
# <PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:24+0000\n"
"PO-Revision-Date: 2022-09-22 05:46+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Italian (https://app.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: hr_appraisal_survey
#: model_terms:ir.ui.view,arch_db:hr_appraisal_survey.hr_appraisal_survey_page_statistics_header
msgid "- Feedback requested by -"
msgstr "- Riscontro richiesto da -"

#. module: hr_appraisal_survey
#: model_terms:survey.survey,description:hr_appraisal_survey.appraisal_360_feedback_template
msgid ""
"360 Degree Feedback is a system or process in which managers will ask feedback from the people who work around the employee.\n"
"            This typically includes the employee's manager, peers, and direct reports."
msgstr ""
"Il feedback a 360 gradi è un sistema o processo nel quale i manager chiedono feedback alle persone che lavorano nella cerchia del dipendente.\n"
"           Tipicamente, questo include il manager del dipendente, i pari e rapporti diretti."

#. module: hr_appraisal_survey
#: model:survey.survey,title:hr_appraisal_survey.appraisal_360_feedback_template
msgid "360 Feedback"
msgstr "Riscontri a 360°"

#. module: hr_appraisal_survey
#: model:mail.template,body_html:hr_appraisal_survey.mail_template_appraisal_ask_feedback
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        Dear <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>,\n"
"                        <br><br>\n"
"                        An appraisal feedback was requested about <t t-out=\"object.appraisal_id.employee_id.name or 'this'\">this</t>.\n"
"                        <br>\n"
"                        Please take time to fill the survey.\n"
"                        <br><br>\n"
"                        Thank you!\n"
"                        <br><br>\n"
"                        <t t-if=\"ctx.get('recipient_users')\">\n"
"                            <p style=\"margin: 16px 0px 16px 0px;\">\n"
"                                <a t-att-href=\"ctx.get('url')\" style=\"background-color:#875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">\n"
"                                    View Appraisal\n"
"                                </a>\n"
"                            </p>\n"
"                        </t>\n"
"                        <t t-if=\"ctx.get('user_body')\">\n"
"                            <div style=\"background-color:#F8F8F8;\">\n"
"                                <t t-out=\"ctx.get('user_body')\">Annual appraisal.</t>\n"
"                            </div>\n"
"                        </t>\n"
"                        <div style=\"margin: 16px 0px 16px 0px; text-align: center;\">\n"
"                            <a t-att-href=\"object.get_start_url()\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                                <t t-if=\"object.survey_id.certification\">\n"
"                                    Start Certification\n"
"                                </t>\n"
"                                <t t-else=\"\">\n"
"                                    Start Survey\n"
"                                </t>\n"
"                            </a>\n"
"                        </div>\n"
"                    </p>\n"
"                </div>\n"
"            "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        Gentile <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>,\n"
"                        <br><br>\n"
"                        è stato richiesto un feedback sulla valutazione riguardo <t t-out=\"object.appraisal_id.employee_id.name or 'this'\">questo</t>.\n"
"                        <br>\n"
"                        Ti chiediamo di spendere un po' di tempo per completare il sondaggio.\n"
"                        <br><br>\n"
"                        Grazie!\n"
"                        <br><br>\n"
"                        <t t-if=\"ctx.get('recipient_users')\">\n"
"                            <p style=\"margin: 16px 0px 16px 0px;\">\n"
"                                <a t-att-href=\"ctx.get('url')\" style=\"background-color:#875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">\n"
"                                    Mostra valutazione\n"
"                                </a>\n"
"                            </p>\n"
"                        </t>\n"
"                        <t t-if=\"ctx.get('user_body')\">\n"
"                            <div style=\"background-color:#F8F8F8;\">\n"
"                                <t t-out=\"ctx.get('user_body')\">Valutazione annuale.</t>\n"
"                            </div>\n"
"                        </t>\n"
"                        <div style=\"margin: 16px 0px 16px 0px; text-align: center;\">\n"
"                            <a t-att-href=\"object.get_start_url()\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                                <t t-if=\"object.survey_id.certification\">\n"
"                                    Inizia certificazione\n"
"                                </t>\n"
"                                <t t-else=\"\">\n"
"                                    Inizia sondaggio\n"
"                                </t>\n"
"                            </a>\n"
"                        </div>\n"
"                    </p>\n"
"                </div>\n"
"            "

#. module: hr_appraisal_survey
#: model_terms:ir.ui.view,arch_db:hr_appraisal_survey.hr_appraisal_survey_button_form_view
msgid "<i class=\"fa fa-fw fa-arrow-right\"/>Edit Survey"
msgstr "<i class=\"fa fa-fw fa-arrow-right\"/>Modifica sondaggio"

#. module: hr_appraisal_survey
#: model_terms:ir.ui.view,arch_db:hr_appraisal_survey.hr_appraisal_view_form
msgid ""
"<span class=\"o_stat_text\">Feedback</span>\n"
"                        <span class=\"o_stat_text\">Survey</span>"
msgstr ""
"<span class=\"o_stat_text\">Riscontro</span>\n"
"                        <span class=\"o_stat_text\">Sondaggio</span>"

#. module: hr_appraisal_survey
#: model:survey.question.answer,value:hr_appraisal_survey.arow_3_1_1
msgid "Ability to cope with multidisciplinarity of team"
msgstr "Capacità di gestire una squadra multidisciplinare"

#. module: hr_appraisal_survey
#: model:survey.question.answer,value:hr_appraisal_survey.arow_3_1_6
msgid "Ability to follow and complete work as instructed"
msgstr "Capacità di seguire e completare il lavoro come indicato"

#. module: hr_appraisal_survey
#: model:survey.question.answer,value:hr_appraisal_survey.arow_3_2_5
msgid "Ability to manage planning resources, risks, budgets and deadlines"
msgstr ""
"Capacità di gestire le risorse di pianificazione, i rischi, le previsioni di"
" spesa e le scadenze"

#. module: hr_appraisal_survey
#: model:survey.question,title:hr_appraisal_survey.appraisal_360_4
msgid "About us"
msgstr "Chi siamo"

#. module: hr_appraisal_survey
#: model:survey.question,title:hr_appraisal_survey.appraisal_360_1
msgid "About you"
msgstr "Informazioni personali"

#. module: hr_appraisal_survey
#: model:survey.question.answer,value:hr_appraisal_survey.arow_3_1_13
msgid ""
"Adaptability: Ability to adapt oneself to organizational changes while "
"keeping efficiency"
msgstr ""
"Adattabilità: capacità di adattarsi ai cambiamenti organizzativi mantenendo "
"l'efficienza"

#. module: hr_appraisal_survey
#: model_terms:ir.actions.act_window,help:hr_appraisal_survey.survey_survey_action_appraisal
msgid "Add a new survey"
msgstr "Aggiungi un nuovo sondaggio"

#. module: hr_appraisal_survey
#: model_terms:ir.ui.view,arch_db:hr_appraisal_survey.appraisal_ask_feedback_view_form
msgid "Add employees..."
msgstr "Aggiungi dipendenti..."

#. module: hr_appraisal_survey
#: model:survey.question,title:hr_appraisal_survey.appraisal_2_3
msgid "Additional Comments"
msgstr "Commenti aggiuntivi"

#. module: hr_appraisal_survey
#: model:survey.question.answer,value:hr_appraisal_survey.y_360_5_6
msgid "Admit my mistakes"
msgstr "Ammettere i miei errori"

#. module: hr_appraisal_survey
#: model:survey.question.answer,value:hr_appraisal_survey.x_360_5_5
msgid "Almost always"
msgstr "Quasi sempre"

#. module: hr_appraisal_survey
#. odoo-python
#: code:addons/hr_appraisal_survey/wizard/appraisal_ask_feedback.py:0
#, python-format
msgid ""
"An appraisal feedback was requested. Please take time to fill the <a "
"href=\"%s\" target=\"_blank\">survey</a>"
msgstr ""
"È stato richiesto un riscontro di valutazione. Compilare il <a href=\"%s\" "
"target=\"_blank\">sondaggio</a> per il tempo necessario."

#. module: hr_appraisal_survey
#: model:survey.question.answer,value:hr_appraisal_survey.arow_3_1_11
msgid "Analytical and synthetic mind"
msgstr "Spirito analitico e sintetico"

#. module: hr_appraisal_survey
#: model:ir.model.fields,field_description:hr_appraisal_survey.field_appraisal_ask_feedback__deadline
msgid "Answer Deadline"
msgstr "Scadenza risposta"

#. module: hr_appraisal_survey
#: model:ir.model.fields,field_description:hr_appraisal_survey.field_appraisal_ask_feedback__appraisal_id
#: model:ir.model.fields,field_description:hr_appraisal_survey.field_survey_user_input__appraisal_id
msgid "Appraisal"
msgstr "Valutazione"

#. module: hr_appraisal_survey
#: model:ir.model.fields,field_description:hr_appraisal_survey.field_appraisal_ask_feedback__employee_id
msgid "Appraisal Employee"
msgstr "Dipendente valutazione"

#. module: hr_appraisal_survey
#: model:ir.model.fields,field_description:hr_appraisal_survey.field_survey_survey__is_appraisal
msgid "Appraisal Managers Only"
msgstr "Solo supervisori valutazione"

#. module: hr_appraisal_survey
#: model:ir.model.fields,field_description:hr_appraisal_survey.field_hr_department__appraisal_survey_template_id
msgid "Appraisal Survey"
msgstr "Sondaggio valutazione"

#. module: hr_appraisal_survey
#: model:ir.model.fields,field_description:hr_appraisal_survey.field_res_company__appraisal_survey_template_id
#: model:ir.model.fields,field_description:hr_appraisal_survey.field_res_config_settings__appraisal_survey_template_id
msgid "Appraisal Survey Template"
msgstr "Modello sondaggio valutazione"

#. module: hr_appraisal_survey
#: model:survey.question,title:hr_appraisal_survey.appraisal_1_3
msgid "Appraisal for Period"
msgstr "Valutazione per il periodo"

#. module: hr_appraisal_survey
#: model_terms:ir.ui.view,arch_db:hr_appraisal_survey.hr_appraisal_survey_page_statistics_header
msgid "Appraisal of"
msgstr "Valutazione di"

#. module: hr_appraisal_survey
#: model:mail.template,name:hr_appraisal_survey.mail_template_appraisal_ask_feedback
msgid "Appraisal: Ask Feedback"
msgstr "Valutazione: richiesta feedback"

#. module: hr_appraisal_survey
#: model:survey.question,title:hr_appraisal_survey.appraisal_1_5
msgid "Appraiser"
msgstr "Valutatore"

#. module: hr_appraisal_survey
#: model_terms:ir.ui.view,arch_db:hr_appraisal_survey.appraisal_ask_feedback_view_form
#: model_terms:ir.ui.view,arch_db:hr_appraisal_survey.hr_appraisal_view_form
msgid "Ask Feedback"
msgstr "Chiedi riscontro"

#. module: hr_appraisal_survey
#: model:ir.model,name:hr_appraisal_survey.model_appraisal_ask_feedback
msgid "Ask Feedback for Appraisal"
msgstr "Richiesta riscontro per valutazione"

#. module: hr_appraisal_survey
#: model:ir.model.fields,field_description:hr_appraisal_survey.field_hr_appraisal__employee_feedback_ids
msgid "Asked Feedback"
msgstr "Riscontro richiesto"

#. module: hr_appraisal_survey
#: model_terms:survey.question,description:hr_appraisal_survey.appraisal_1
msgid "At the conclusion of the appraisal time period"
msgstr "Al termine del periodo di valutazione"

#. module: hr_appraisal_survey
#: model_terms:survey.question,description:hr_appraisal_survey.appraisal_1
msgid "At the outset of the appraisal time period"
msgstr "All'inizio del periodo di valutazione"

#. module: hr_appraisal_survey
#: model:ir.model.fields,field_description:hr_appraisal_survey.field_appraisal_ask_feedback__attachment_ids
msgid "Attachments"
msgstr "Allegati"

#. module: hr_appraisal_survey
#: model:ir.model.fields,field_description:hr_appraisal_survey.field_appraisal_ask_feedback__author_id
msgid "Author"
msgstr "Autore"

#. module: hr_appraisal_survey
#: model_terms:ir.ui.view,arch_db:hr_appraisal_survey.hr_appraisal_survey_button_form_view
msgid "Back to the appraisal"
msgstr "Ritorna alla valutazione"

#. module: hr_appraisal_survey
#: model:ir.model.fields,field_description:hr_appraisal_survey.field_appraisal_ask_feedback__can_edit_body
msgid "Can Edit Body"
msgstr "Può modificare corpo"

#. module: hr_appraisal_survey
#: model_terms:ir.ui.view,arch_db:hr_appraisal_survey.appraisal_ask_feedback_view_form
msgid "Cancel"
msgstr "Annulla"

#. module: hr_appraisal_survey
#: model:ir.model.fields,help:hr_appraisal_survey.field_survey_survey__is_appraisal
msgid "Check this option to restrict the answers to appraisal managers only."
msgstr ""
"Selezionare l'opzione per limitare le risposte ai soli supervisori "
"valutazione."

#. module: hr_appraisal_survey
#: model:survey.question.answer,value:hr_appraisal_survey.y_360_5_3
msgid "Collaborate effectively with others to achieve shared goals"
msgstr ""
"Collaborare efficacemente con gli altri per raggiungere obiettivi condivisi"

#. module: hr_appraisal_survey
#: model:survey.question.answer,value:hr_appraisal_survey.arow_3_1_9
msgid ""
"Communication skills (written & verbally): clearness, concision, exactitude"
msgstr ""
"Competenze comunicative (scritte e verbali): chiarezza, concisione, "
"precisione"

#. module: hr_appraisal_survey
#: model:ir.model,name:hr_appraisal_survey.model_res_company
msgid "Companies"
msgstr "Aziende"

#. module: hr_appraisal_survey
#: model:survey.question.answer,value:hr_appraisal_survey.arow_3_1_3
msgid ""
"Compliance to internal rules and processes (timesheets completion, etc.)"
msgstr ""
"Rispetto delle regole e delle procedure interne (completamento di fogli ore "
"ecc...)"

#. module: hr_appraisal_survey
#: model:survey.question,title:hr_appraisal_survey.appraisal_360_9
msgid "Conclusion"
msgstr "Conclusione"

#. module: hr_appraisal_survey
#: model:ir.model,name:hr_appraisal_survey.model_res_config_settings
msgid "Config Settings"
msgstr "Impostazioni di configurazione"

#. module: hr_appraisal_survey
#: model:ir.model.fields,field_description:hr_appraisal_survey.field_appraisal_ask_feedback__body
msgid "Contents"
msgstr "Contenuti"

#. module: hr_appraisal_survey
#: model:survey.question.answer,value:hr_appraisal_survey.y_360_5_2
msgid "Create space for different ideas and options to be voiced"
msgstr "Creare uno spazio per dare voce a idee e opzioni diverse."

#. module: hr_appraisal_survey
#: model:ir.model.fields,field_description:hr_appraisal_survey.field_appraisal_ask_feedback__create_uid
msgid "Created by"
msgstr "Creato da"

#. module: hr_appraisal_survey
#: model:ir.model.fields,field_description:hr_appraisal_survey.field_appraisal_ask_feedback__create_date
msgid "Created on"
msgstr "Data creazione"

#. module: hr_appraisal_survey
#: model:survey.question.answer,value:hr_appraisal_survey.arow_3_1_14
msgid "Creativity and forward looking aptitude"
msgstr "Creatività e attitudine alla lungimiranza"

#. module: hr_appraisal_survey
#: model_terms:survey.question,description:hr_appraisal_survey.appraisal_1
msgid ""
"Critical or key elements of performance and professional development needs "
"(if any), should also be noted at this time"
msgstr ""
"Stabilire i punti fondamentali e critici delle prestazioni nonché i bisogni "
"per lo sviluppo professionale (eventualmente)."

#. module: hr_appraisal_survey
#: model:survey.question.answer,value:hr_appraisal_survey.arow_3_1_8
msgid "Customer commitment"
msgstr "Impegno verso i clienti"

#. module: hr_appraisal_survey
#: model:survey.question,title:hr_appraisal_survey.appraisal_1_4
msgid "Date of review"
msgstr "Data della verifica"

#. module: hr_appraisal_survey
#: model:survey.question.answer,value:hr_appraisal_survey.arow_3_1_7
msgid "Decision making"
msgstr "Processo decisionale"

#. module: hr_appraisal_survey
#: model_terms:ir.ui.view,arch_db:hr_appraisal_survey.res_config_settings_view_form_hr_appraisal_survey
msgid "Default Template"
msgstr "Modello predefinito"

#. module: hr_appraisal_survey
#: model:survey.question.answer,value:hr_appraisal_survey.arow_3_2_2
msgid "Delegation: Ability to efficiently assign tasks to other people"
msgstr ""
"Delega: capacità di assegnare compiti ad altre persone in maniera efficiente"

#. module: hr_appraisal_survey
#: model:ir.model,name:hr_appraisal_survey.model_hr_department
msgid "Department"
msgstr "Ufficio"

#. module: hr_appraisal_survey
#: model_terms:survey.question,description:hr_appraisal_survey.appraisal_3
msgid "Did not meet standards and expectations"
msgstr "Non ha raggiunto gli standard e le aspettative"

#. module: hr_appraisal_survey
#: model:ir.model.fields,field_description:hr_appraisal_survey.field_appraisal_ask_feedback__display_name
msgid "Display Name"
msgstr "Nome visualizzato"

#. module: hr_appraisal_survey
#: model:survey.question,title:hr_appraisal_survey.appraisal_360_10
msgid "Do you have any comment to tell me and help me improve?"
msgstr "Hai dei consigli da darmi per migliorare?"

#. module: hr_appraisal_survey
#: model:ir.model,name:hr_appraisal_survey.model_hr_appraisal
msgid "Employee Appraisal"
msgstr "Valutazione dipendente"

#. module: hr_appraisal_survey
#: model:survey.survey,title:hr_appraisal_survey.appraisal_feedback_template
msgid "Employee Appraisal Form"
msgstr "Modulo valutazione dipendente"

#. module: hr_appraisal_survey
#: model:survey.question,title:hr_appraisal_survey.appraisal_5
msgid "Employee Comments"
msgstr "Commenti dipendente"

#. module: hr_appraisal_survey
#: model:survey.survey,title:hr_appraisal_survey.opinion_form
msgid "Employee Opinion Form"
msgstr "Scheda opinione dipendente"

#. module: hr_appraisal_survey
#: model:survey.question,title:hr_appraisal_survey.appraisal_3
msgid "Employee Performance in Key Areas"
msgstr "Prestazione dei dipendenti nelle aree chiave"

#. module: hr_appraisal_survey
#: model:survey.question.answer,value:hr_appraisal_survey.arow_3_1_2
msgid "Enthusiasm & implication toward projects/assignments"
msgstr "Entusiasmo e coinvolgimento riguardo a progetti/incarichi"

#. module: hr_appraisal_survey
#: model_terms:survey.question,description:hr_appraisal_survey.appraisal_3
msgid "Exceeds standards and expectations"
msgstr "Ha superato gli standard e le aspettative"

#. module: hr_appraisal_survey
#. odoo-python
#: code:addons/hr_appraisal_survey/wizard/appraisal_ask_feedback.py:0
#, python-format
msgid "Fill the feedback form on survey"
msgstr "Compilazione modulo di riscontro sondaggio"

#. module: hr_appraisal_survey
#: model:ir.model.fields,field_description:hr_appraisal_survey.field_appraisal_ask_feedback__email_from
msgid "From"
msgstr "Da"

#. module: hr_appraisal_survey
#: model:survey.question,title:hr_appraisal_survey.appraisal_360_5
msgid "How do you feel to work with me? Do I ... "
msgstr "Come ti senti quando lavori con me? Io..."

#. module: hr_appraisal_survey
#: model:survey.question.answer,value:hr_appraisal_survey.appraisal_360_3_sug5
msgid "I'm not from the company."
msgstr "Non faccio parte dell'azienda."

#. module: hr_appraisal_survey
#: model:survey.question.answer,value:hr_appraisal_survey.appraisal_360_3_sug3
msgid "I'm part of your management."
msgstr "Faccio parte del team di gestione."

#. module: hr_appraisal_survey
#: model:survey.question.answer,value:hr_appraisal_survey.appraisal_360_3_sug4
msgid "I'm referring to you."
msgstr "Mi rivolgo a te."

#. module: hr_appraisal_survey
#: model:ir.model.fields,field_description:hr_appraisal_survey.field_appraisal_ask_feedback__id
msgid "ID"
msgstr "ID"

#. module: hr_appraisal_survey
#: model_terms:survey.question,description:hr_appraisal_survey.appraisal_4
msgid ""
"Identify professional, performance, or project objectives you recommend for "
"employee’s continued career development over the coming year."
msgstr ""
"Identifica gli obiettivi professionali, di prestazione o di progetto "
"raccomandati per lo sviluppo continuo della carriera del dipendente nel "
"corso del prossimo anno"

#. module: hr_appraisal_survey
#: model:survey.question.answer,value:hr_appraisal_survey.arow_3_1_5
msgid "Initiative and self autonomy"
msgstr "Iniziativa e autonomia"

#. module: hr_appraisal_survey
#: model:ir.model.fields,field_description:hr_appraisal_survey.field_appraisal_ask_feedback__is_mail_template_editor
msgid "Is Editor"
msgstr "È editor"

#. module: hr_appraisal_survey
#: model_terms:survey.question,description:hr_appraisal_survey.appraisal_1
msgid ""
"It is the joint responsibility of the employee and the supervisor "
"(appraiser) to establish a feasible work plan for the coming year, including"
" major employee responsibilities and corresponding benchmarks against which "
"results will be evaluated."
msgstr ""
"È responsabilità del dipendente e del supervisore (valutatore) stabilire un "
"piano di lavoro fattibile per l'anno a venire, compresi le responsabilità "
"principali del dipendente e i parametri di riferimento corrispondenti "
"rispetto ai quali saranno valutati i risultati."

#. module: hr_appraisal_survey
#: model_terms:survey.question,description:hr_appraisal_survey.appraisal_1
msgid ""
"It is the primary responsibility of the supervisor to gather the necessary "
"input from the appropriate sources of feedback (internal and/or external "
"customers, peers)."
msgstr ""
"Una delle responsabilità principali del supervisore è di riunire gli input "
"necessari dalle fonti di feedback appropriate (clienti interni e/o esterni, "
"pari)."

#. module: hr_appraisal_survey
#: model:ir.model.fields,field_description:hr_appraisal_survey.field_appraisal_ask_feedback__lang
msgid "Language"
msgstr "Lingua"

#. module: hr_appraisal_survey
#: model:ir.model.fields,field_description:hr_appraisal_survey.field_appraisal_ask_feedback____last_update
msgid "Last Modified on"
msgstr "Ultima modifica il"

#. module: hr_appraisal_survey
#: model:ir.model.fields,field_description:hr_appraisal_survey.field_appraisal_ask_feedback__write_uid
msgid "Last Updated by"
msgstr "Ultimo aggiornamento di"

#. module: hr_appraisal_survey
#: model:ir.model.fields,field_description:hr_appraisal_survey.field_appraisal_ask_feedback__write_date
msgid "Last Updated on"
msgstr "Ultimo aggiornamento il"

#. module: hr_appraisal_survey
#: model:survey.question.answer,value:hr_appraisal_survey.arow_3_2_3
msgid ""
"Leadership: create a challenging and motivating work environment aligned "
"with the company's strategy"
msgstr ""
"Leadership: creare un ambiente lavorativo stimolante e motivante, in linea "
"con la strategia dell'azienda"

#. module: hr_appraisal_survey
#: model:survey.question.answer,value:hr_appraisal_survey.arow_3_2_4
msgid "Leadership: sustain subordinates in their professional growth"
msgstr "Leadership: sostenere i subordinati nella loro crescita professionale"

#. module: hr_appraisal_survey
#: model:survey.question.answer,value:hr_appraisal_survey.y_360_5_1
msgid "Listen well to others"
msgstr "Ascoltare gli altri"

#. module: hr_appraisal_survey
#: model:ir.model.fields,field_description:hr_appraisal_survey.field_appraisal_ask_feedback__template_id
msgid "Mail Template"
msgstr "Modello e-mail"

#. module: hr_appraisal_survey
#: model_terms:survey.question,description:hr_appraisal_survey.appraisal_3
msgid "Meet standards and expectations"
msgstr "Ha raggiunto standard ed aspettative"

#. module: hr_appraisal_survey
#. odoo-python
#: code:addons/hr_appraisal_survey/wizard/appraisal_ask_feedback.py:0
#, python-format
msgid "Missing email"
msgstr "E-mail mancante"

#. module: hr_appraisal_survey
#: model:survey.question,title:hr_appraisal_survey.appraisal_1_1
msgid "Name"
msgstr "Nome"

#. module: hr_appraisal_survey
#: model:survey.question.answer,value:hr_appraisal_survey.x_360_5_1
msgid "Never"
msgstr "Mai"

#. module: hr_appraisal_survey
#: model:survey.question,title:hr_appraisal_survey.appraisal_2_1
msgid "Objectives"
msgstr "Obiettivi"

#. module: hr_appraisal_survey
#: model:survey.question.answer,value:hr_appraisal_survey.x_360_5_4
msgid "Often"
msgstr "Spesso"

#. module: hr_appraisal_survey
#: model_terms:ir.ui.view,arch_db:hr_appraisal_survey.appraisal_ask_feedback_view_form
msgid "Optional message"
msgstr "Messaggio opzionale"

#. module: hr_appraisal_survey
#: model:ir.model.fields,help:hr_appraisal_survey.field_appraisal_ask_feedback__lang
msgid ""
"Optional translation language (ISO code) to select when sending out an "
"email. If not set, the english version will be used. This should usually be "
"a placeholder expression that provides the appropriate language, e.g. {{ "
"object.partner_id.lang }}."
msgstr ""
"Lingua di traduzione opzionale (codice ISO) da selezionare quando viene "
"inviata una e-mail. Se non impostata, viene usata la versione inglese. "
"Normalmente dovrebbe essere un'espressione segnaposto che fornisce la lingua"
" appropriata, es. {{ object.partner_id.lang }}."

#. module: hr_appraisal_survey
#: model_terms:survey.question,description:hr_appraisal_survey.appraisal_1
msgid "Overall Purpose Of Employee Appraisal"
msgstr "Scopo generale della valutazione dei dipendenti"

#. module: hr_appraisal_survey
#: model:survey.question,title:hr_appraisal_survey.appraisal_4_2
msgid "Personal Performance Objectives"
msgstr "Obiettivi di prestazione personale"

#. module: hr_appraisal_survey
#: model:survey.question,title:hr_appraisal_survey.appraisal_1_2
msgid "Position Title"
msgstr "Titolo della posizione"

#. module: hr_appraisal_survey
#: model:survey.question,title:hr_appraisal_survey.appraisal_4_1
msgid "Professional Development Objectives"
msgstr "Obiettivi di sviluppo professionale"

#. module: hr_appraisal_survey
#: model:survey.question,title:hr_appraisal_survey.appraisal_4
msgid "Professional Development and Performance Plan"
msgstr "Piano di sviluppo e prestazione professionale"

#. module: hr_appraisal_survey
#: model:survey.question,title:hr_appraisal_survey.appraisal_4_3
msgid "Project Objectives"
msgstr "Obiettivi del progetto"

#. module: hr_appraisal_survey
#: model:survey.question.answer,value:hr_appraisal_survey.arow_3_1_12
msgid "Promptness and attendance record"
msgstr "Puntualità e registro presenze"

#. module: hr_appraisal_survey
#: model:survey.question.answer,value:hr_appraisal_survey.x_360_5_2
msgid "Rarely"
msgstr "Raramente"

#. module: hr_appraisal_survey
#: model:ir.model.fields,field_description:hr_appraisal_survey.field_appraisal_ask_feedback__employee_ids
msgid "Recipients"
msgstr "Destinatari"

#. module: hr_appraisal_survey
#: model:survey.question.answer,value:hr_appraisal_survey.y_360_5_7
msgid "Recognize the contributions of teammates and peers"
msgstr "Riconoscere il contributo di membri del team e pari"

#. module: hr_appraisal_survey
#: model:ir.model.fields,field_description:hr_appraisal_survey.field_appraisal_ask_feedback__render_model
msgid "Rendering Model"
msgstr "Modello di rendering"

#. module: hr_appraisal_survey
#: model_terms:ir.ui.view,arch_db:hr_appraisal_survey.hr_appraisal_survey_page_statistics_header
msgid "Responded:"
msgstr "Ha risposto:"

#. module: hr_appraisal_survey
#: model:survey.question,title:hr_appraisal_survey.appraisal_2_2
msgid "Results"
msgstr "Risultati"

#. module: hr_appraisal_survey
#: model:survey.question.answer,value:hr_appraisal_survey.arow_3_2_1
msgid ""
"Results of the bottom-up survey and mitigation actions to face technical, "
"organizational, structural and/or relational issues"
msgstr ""
"Risultati del sondaggio dal basso verso l'alto (bottom-up) e azioni di "
"mitigazione per affrontare questioni tecniche, organizzative, strutturali "
"e/o relazionali"

#. module: hr_appraisal_survey
#: model:survey.question.answer,value:hr_appraisal_survey.y_360_5_5
msgid "Seek to understand the problem before working on a solution"
msgstr "Cercare di capire il problema prima di trovare una soluzione."

#. module: hr_appraisal_survey
#: model_terms:ir.ui.view,arch_db:hr_appraisal_survey.appraisal_ask_feedback_view_form
msgid "Send"
msgstr "Invia"

#. module: hr_appraisal_survey
#: model:ir.model.fields,help:hr_appraisal_survey.field_hr_appraisal__survey_ids
msgid "Sent out surveys"
msgstr "Invia sondaggi"

#. module: hr_appraisal_survey
#: model:mail.template,description:hr_appraisal_survey.mail_template_appraisal_ask_feedback
msgid "Sent to employees to gather appraisal feedback"
msgstr ""
"E-mail inviata ai dipendenti per raccogliere feedback sulla valutazione"

#. module: hr_appraisal_survey
#: model:survey.question.answer,value:hr_appraisal_survey.y_360_5_4
msgid "Show good judgment in decision making"
msgstr "Mostra buon senso nel prendere decisioni"

#. module: hr_appraisal_survey
#: model_terms:survey.question,description:hr_appraisal_survey.appraisal_3
msgid "Significantly below standards and expectations"
msgstr "Non ha per niente raggiunto gli standard e le aspettative"

#. module: hr_appraisal_survey
#: model_terms:survey.question,description:hr_appraisal_survey.appraisal_3
msgid ""
"Significantly exceeds standards and expectations required of the position"
msgstr ""
"Ha superato di gran lunga gli standard e le aspettative richieste dalla "
"posizione"

#. module: hr_appraisal_survey
#: model:survey.question.answer,value:hr_appraisal_survey.x_360_5_3
msgid "Sometimes"
msgstr "A volte"

#. module: hr_appraisal_survey
#: model:ir.model.fields,field_description:hr_appraisal_survey.field_appraisal_ask_feedback__subject
#: model:survey.question,title:hr_appraisal_survey.appraisal_3_1
msgid "Subject"
msgstr "Oggetto"

#. module: hr_appraisal_survey
#: model_terms:ir.ui.view,arch_db:hr_appraisal_survey.appraisal_ask_feedback_view_form
msgid "Subject..."
msgstr "Oggetto..."

#. module: hr_appraisal_survey
#: model:survey.question,title:hr_appraisal_survey.appraisal_3_2
msgid "Supervisors only"
msgstr "Solo supervisori"

#. module: hr_appraisal_survey
#: model:ir.model,name:hr_appraisal_survey.model_survey_survey
#: model:ir.model.fields,field_description:hr_appraisal_survey.field_hr_appraisal__survey_ids
#: model:ir.model.fields,field_description:hr_appraisal_survey.field_survey_question_answer__survey_id
msgid "Survey"
msgstr "Sondaggio"

#. module: hr_appraisal_survey
#. odoo-python
#: code:addons/hr_appraisal_survey/models/hr_appraisal.py:0
#: code:addons/hr_appraisal_survey/models/hr_appraisal.py:0
#: code:addons/hr_appraisal_survey/models/survey.py:0
#, python-format
msgid "Survey Feedback"
msgstr "Feedback Sondaggio"

#. module: hr_appraisal_survey
#: model:ir.model,name:hr_appraisal_survey.model_survey_question_answer
msgid "Survey Label"
msgstr "Etichetta sondaggio"

#. module: hr_appraisal_survey
#: model:ir.model.fields,field_description:hr_appraisal_survey.field_appraisal_ask_feedback__survey_template_id
msgid "Survey Template"
msgstr "Modello sondaggio"

#. module: hr_appraisal_survey
#: model:ir.model,name:hr_appraisal_survey.model_survey_user_input
msgid "Survey User Input"
msgstr "Inserimento utente nel sondaggio"

#. module: hr_appraisal_survey
#: model:ir.actions.act_window,name:hr_appraisal_survey.survey_survey_action_appraisal
#: model:ir.ui.menu,name:hr_appraisal_survey.menu_hr_appraisal_surveys
msgid "Surveys"
msgstr "Sondaggi"

#. module: hr_appraisal_survey
#: model:mail.template,subject:hr_appraisal_survey.mail_template_appraisal_ask_feedback
msgid "Take part in {{ object.employee_id.name or 'this' }} appraisal"
msgstr "Partecipa a {{ object.employee_id.name or 'this' }} valutazione"

#. module: hr_appraisal_survey
#: model:survey.question.answer,value:hr_appraisal_survey.arow_3_1_4
msgid ""
"Team spirit: ability to work efficiently with peers, manage the conflicts "
"with diplomacy"
msgstr ""
"Spirito di squadra: capacità di lavorare in modo efficiente con i colleghi e"
" di gestire i conflitti con diplomazia"

#. module: hr_appraisal_survey
#: model:survey.question.answer,value:hr_appraisal_survey.arow_3_1_10
msgid "Technical skills regarding to the job requirements"
msgstr "Competenze tecniche che riguardano i requisiti per il lavoro"

#. module: hr_appraisal_survey
#: model_terms:survey.question,description:hr_appraisal_survey.appraisal_3
msgid ""
"The appraiser should rate the employee’s major work accomplishments and "
"performance according to the metric provided below:"
msgstr ""
"Il valutatore dovrebbe dare una valutazione dei principali risultati "
"lavorativi del dipendente e delle prestazioni seconda i parametri indicati "
"di seguito:"

#. module: hr_appraisal_survey
#: model_terms:survey.question,description:hr_appraisal_survey.appraisal_1
msgid ""
"The employee may choose to offer comments or explanation regarding the "
"completed review."
msgstr ""
"Il dipendente può scegliere di dare commenti o spiegazioni sulla valutazione"
" conclusa."

#. module: hr_appraisal_survey
#: model_terms:survey.question,description:hr_appraisal_survey.appraisal_1
msgid ""
"The employee will be responsible for completing a draft of the Appraisal "
"Form as a tool for self-appraisal and a starting point for the supervisor’s "
"evaluation. The employee can add examples of achievements for each "
"criterion. Once the form had been filled, the employee send it to their "
"supervisor."
msgstr ""
"Il dipendente avrà la responsabilità di compilare una bozza del modulo di "
"valutazione come strumento di auto valutazione nonché un punto di partenza "
"per la valutazione del supervisore. Il dipendente può aggiungere esempi di "
"risultati ottenuti per ogni criterio. Una volta che il modulo è stato "
"completto, il dipendente lo invierà al supervisore."

#. module: hr_appraisal_survey
#: model_terms:survey.question,description:hr_appraisal_survey.appraisal_1
msgid ""
"The supervisor synthesizes and integrates all input into the completed "
"appraisal. The motivation of the evaluation is explained in the ad hoc "
"fields."
msgstr ""
"Il supervisore sintetizza  integra tutti gli input nella valutazione "
"completata. Le motivazioni della valutazione sono spiegate nei campi "
"specifici."

#. module: hr_appraisal_survey
#. odoo-python
#: code:addons/hr_appraisal_survey/wizard/appraisal_ask_feedback.py:0
#, python-format
msgid ""
"This employee doesn't have any mail address registered and will not receive any email. \n"
"The following employees do not have any email : \n"
"%s"
msgstr ""
"Questo dipendente non ha un indirizzo e-mail registrato e non ricevera nessuna e-mail.\n"
"I seguenti dipendenti non hanno un indirizzo e-mail: \n"
"%s"

#. module: hr_appraisal_survey
#: model:ir.model.fields,help:hr_appraisal_survey.field_hr_department__appraisal_survey_template_id
msgid ""
"This field is used with 360 Feedback setting on Appraisal App, the aim is to"
" define a default Survey Template related to this department."
msgstr ""
"Questo campo è usato con l'impostazione Feedback 360° su Odoo Valutazione, "
"lo scopo è quello di definire un modello di indagine relativo a questo "
"dipartimento."

#. module: hr_appraisal_survey
#: model_terms:ir.ui.view,arch_db:hr_appraisal_survey.hr_appraisal_survey_button_form_view
msgid "This is a Test Survey."
msgstr "Sondaggio di prova."

#. module: hr_appraisal_survey
#: model_terms:survey.survey,description:hr_appraisal_survey.appraisal_feedback_template
msgid ""
"This survey allows you to give a feedback about your collaboration with an "
"employee. Filling it helps us improving the appraisal process."
msgstr ""
"Questo sondaggio consente di fornire un riscontro riguardo la tua "
"collaborazione con un dipendente. Compilandolo ci aiuti a migliorare il "
"processo di valutazione."

#. module: hr_appraisal_survey
#: model:survey.question.answer,value:hr_appraisal_survey.arow_3_1_15
msgid "Time management: projects/tasks are completed on time"
msgstr "Gestione del tempo: i progetti/lavori sono completati nei tempi"

#. module: hr_appraisal_survey
#: model_terms:survey.question,description:hr_appraisal_survey.appraisal_1
msgid ""
"To assist employees in their professional growth, through the identification"
" of strengths and opportunities for development"
msgstr ""
"Aiutare i dipendenti nel corso della crescita professionale, attraverso "
"l'individuazione di punti di forza e opportunità di sviluppo"

#. module: hr_appraisal_survey
#: model_terms:survey.question,description:hr_appraisal_survey.appraisal_1
msgid "To initiate a clear and open communication of performance expectations"
msgstr ""
"Dare inizio a una comunicazione chiara e aperta circa le aspettative di "
"prestazione"

#. module: hr_appraisal_survey
#. odoo-python
#: code:addons/hr_appraisal_survey/wizard/appraisal_ask_feedback.py:0
#, python-format
msgid "Unable to post message, please configure the sender's email address."
msgstr ""
"Impossibile inviare messaggi, configurare l'indirizzo e-mail del mittente."

#. module: hr_appraisal_survey
#: model:survey.question,title:hr_appraisal_survey.appraisal_5_1
msgid ""
"Use the following space to make any comments regarding the above performance"
" evaluation."
msgstr ""
"Utilizzare il seguente spazio per fare commenti sulla valutazione delle "
"prestazioni di cui sopra."

#. module: hr_appraisal_survey
#: model:ir.model.fields,field_description:hr_appraisal_survey.field_appraisal_ask_feedback__user_body
msgid "User Contents"
msgstr "Contenuti Utenti"

#. module: hr_appraisal_survey
#: model_terms:ir.ui.view,arch_db:hr_appraisal_survey.survey_user_input_view_tree
msgid "View Results"
msgstr "Mostra risultati"

#. module: hr_appraisal_survey
#: model:survey.question.answer,value:hr_appraisal_survey.appraisal_360_3_sug1
msgid "We're colleagues, for the same manager."
msgstr "Siamo colleghi e abbiamo lo stesso manager."

#. module: hr_appraisal_survey
#: model:survey.question.answer,value:hr_appraisal_survey.appraisal_360_3_sug2
msgid "We're colleagues, in different teams."
msgstr "Siamo colleghi ma apparteniamo a team diversi."

#. module: hr_appraisal_survey
#: model:survey.question,title:hr_appraisal_survey.appraisal_360_6
msgid "What should I do in order to improve on my day-to-day job?"
msgstr "Cosa potrei fare per migliorare il lavoro di tutti i giorni?"

#. module: hr_appraisal_survey
#: model:survey.question,title:hr_appraisal_survey.appraisal_360_7
msgid "What's my greatest strength?"
msgstr "Qual è il mio punto di forza migliore?"

#. module: hr_appraisal_survey
#: model:survey.question,title:hr_appraisal_survey.appraisal_360_8
msgid "What's my greatest weakness?"
msgstr "Qual è la mia debolezza più grande?"

#. module: hr_appraisal_survey
#: model:survey.question,title:hr_appraisal_survey.appraisal_360_3
msgid "What's the relation between us?"
msgstr "Che tipo di rapporto abbiamo?"

#. module: hr_appraisal_survey
#: model:survey.question,title:hr_appraisal_survey.appraisal_360_2
msgid "What's your name?"
msgstr "Come ti chiami?"

#. module: hr_appraisal_survey
#: model:survey.question,title:hr_appraisal_survey.appraisal_2
msgid "Work Plan"
msgstr "Piano di lavoro"

#. module: hr_appraisal_survey
#: model_terms:ir.actions.act_window,help:hr_appraisal_survey.survey_survey_action_appraisal
msgid ""
"You can create surveys used for appraisals. Design easily your appraisal,\n"
"                send invitations and analyze answers."
msgstr ""
"Puoi creare sondaggi utilizzati per le valutazioni. Progetta facilmente la tua valutazione, \n"
"invia inviti e analizza le risposte."

#. module: hr_appraisal_survey
#: model_terms:ir.ui.view,arch_db:hr_appraisal_survey.hr_appraisal_survey_button_form_view
msgid "or"
msgstr "o"
