# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* product_unspsc
# 
# Translators:
# <PERSON>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0beta+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-09-23 08:24+0000\n"
"PO-Revision-Date: 2022-09-22 05:48+0000\n"
"Last-Translator: <PERSON>, 2022\n"
"Language-Team: Afrikaans (https://app.transifex.com/odoo/teams/41243/af/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: af\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: product_unspsc
#: model:ir.model.fields,field_description:product_unspsc.field_product_unspsc_code__active
msgid "Active"
msgstr "Aktief"

#. module: product_unspsc
#: model:ir.model.fields,field_description:product_unspsc.field_product_unspsc_code__applies_to
msgid "Applies To"
msgstr ""

#. module: product_unspsc
#: model:ir.model.fields,field_description:product_unspsc.field_product_unspsc_code__code
msgid "Code"
msgstr "Kode"

#. module: product_unspsc
#: model:ir.model.fields,field_description:product_unspsc.field_product_unspsc_code__create_uid
msgid "Created by"
msgstr "Geskep deur"

#. module: product_unspsc
#: model:ir.model.fields,field_description:product_unspsc.field_product_unspsc_code__create_date
msgid "Created on"
msgstr "Geskep op"

#. module: product_unspsc
#: model:ir.model.fields,field_description:product_unspsc.field_product_unspsc_code__display_name
msgid "Display Name"
msgstr "Vertoningsnaam"

#. module: product_unspsc
#: model:ir.model.fields,field_description:product_unspsc.field_product_unspsc_code__id
msgid "ID"
msgstr "ID"

#. module: product_unspsc
#: model:ir.model.fields,help:product_unspsc.field_product_unspsc_code__applies_to
msgid "Indicate if this code could be used in products or in UoM"
msgstr ""

#. module: product_unspsc
#: model:ir.model.fields,field_description:product_unspsc.field_product_unspsc_code____last_update
msgid "Last Modified on"
msgstr "Laas Gewysig op"

#. module: product_unspsc
#: model:ir.model.fields,field_description:product_unspsc.field_product_unspsc_code__write_uid
msgid "Last Updated by"
msgstr "Laas Opgedateer deur"

#. module: product_unspsc
#: model:ir.model.fields,field_description:product_unspsc.field_product_unspsc_code__write_date
msgid "Last Updated on"
msgstr "Laas Opgedateer op"

#. module: product_unspsc
#: model:ir.model.fields,field_description:product_unspsc.field_product_unspsc_code__name
msgid "Name"
msgstr "Naam"

#. module: product_unspsc
#: model:ir.model,name:product_unspsc.model_product_template
#: model:ir.model.fields.selection,name:product_unspsc.selection__product_unspsc_code__applies_to__product
msgid "Product"
msgstr ""

#. module: product_unspsc
#: model:ir.model,name:product_unspsc.model_uom_uom
msgid "Product Unit of Measure"
msgstr ""

#. module: product_unspsc
#: model:ir.model,name:product_unspsc.model_product_unspsc_code
msgid "Product and UOM Codes from UNSPSC"
msgstr ""

#. module: product_unspsc
#: model:ir.model.fields,help:product_unspsc.field_uom_uom__unspsc_code_id
msgid "The UNSPSC code related to this UoM. "
msgstr ""

#. module: product_unspsc
#: model:ir.model.fields,help:product_unspsc.field_product_product__unspsc_code_id
#: model:ir.model.fields,help:product_unspsc.field_product_template__unspsc_code_id
msgid ""
"The UNSPSC code related to this product.  Used for edi in Colombia, Peru and"
" Mexico"
msgstr ""

#. module: product_unspsc
#: model_terms:ir.ui.view,arch_db:product_unspsc.product_template_unspsc
msgid "UNSPSC"
msgstr ""

#. module: product_unspsc
#: model:ir.model.fields,field_description:product_unspsc.field_product_product__unspsc_code_id
#: model:ir.model.fields,field_description:product_unspsc.field_product_template__unspsc_code_id
#: model:ir.model.fields,field_description:product_unspsc.field_uom_uom__unspsc_code_id
msgid "UNSPSC Category"
msgstr ""

#. module: product_unspsc
#: model:ir.model.fields.selection,name:product_unspsc.selection__product_unspsc_code__applies_to__uom
msgid "UoM"
msgstr ""
