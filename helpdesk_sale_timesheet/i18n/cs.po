# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* helpdesk_sale_timesheet
# 
# Translators:
# <PERSON> <jan.<PERSON><PERSON><PERSON>@centrum.cz>, 2022
# <PERSON> <<EMAIL>>, 2022
# ka<PERSON><PERSON><PERSON> schus<PERSON> <karolina.schus<PERSON><PERSON>@vdp.sk>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON><PERSON><PERSON>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# Wil Odoo, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-06 20:43+0000\n"
"PO-Revision-Date: 2022-09-22 05:46+0000\n"
"Last-Translator: Wil Odoo, 2025\n"
"Language-Team: Czech (https://app.transifex.com/odoo/teams/41243/cs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: cs\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n >= 2 && n <= 4 && n % 1 == 0) ? 1: (n % 1 != 0 ) ? 2 : 3;\n"

#. module: helpdesk_sale_timesheet
#: model:helpdesk.sla,name:helpdesk_sale_timesheet.helpdesk_sla_4
msgid "4 hours to finish"
msgstr "4 hodiny do konce"

#. module: helpdesk_sale_timesheet
#: model_terms:sale.order,website_description:helpdesk_sale_timesheet.sale_order_helpdesk_sale_timesheet_1
msgid "About us"
msgstr "O nás"

#. module: helpdesk_sale_timesheet
#: model:ir.model,name:helpdesk_sale_timesheet.model_account_analytic_line
msgid "Analytic Line"
msgstr "Analytická položka"

#. module: helpdesk_sale_timesheet
#: model_terms:sale.order,website_description:helpdesk_sale_timesheet.sale_order_helpdesk_sale_timesheet_1
msgid ""
"As a leading professional services firm,\n"
"                                we know that success is all about the\n"
"                                commitment we put on strong services."
msgstr ""
"Jako přední firma poskytující profesionální služby\n"
"                                     víme, že úspěch je především o\n"
"                                     nasadení velmi kvalitnych služeb."

#. module: helpdesk_sale_timesheet
#: model:ir.model,name:helpdesk_sale_timesheet.model_helpdesk_ticket_convert_wizard
msgid "Convert Helpdesk Tickets to Tasks"
msgstr "Konvertovat helpdesk tickety na úkoly"

#. module: helpdesk_sale_timesheet
#: model:ir.model,name:helpdesk_sale_timesheet.model_project_task_convert_wizard
msgid "Convert Project Tasks to Tickets"
msgstr "Konvertovat projektové úkoly na tickety"

#. module: helpdesk_sale_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.tickets_followup_timesheet
msgid "Date"
msgstr "Datum"

#. module: helpdesk_sale_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.portal_helpdesk_ticket_timesheet
msgid "Days"
msgstr "Dny"

#. module: helpdesk_sale_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.tickets_followup_timesheet
msgid "Days Spent"
msgstr "Strávené dni"

#. module: helpdesk_sale_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.tickets_followup_timesheet
msgid "Days:"
msgstr "Dny:"

#. module: helpdesk_sale_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.tickets_followup_timesheet
msgid "Description"
msgstr "Popis"

#. module: helpdesk_sale_timesheet
#: model:ir.model.fields,field_description:helpdesk_sale_timesheet.field_account_analytic_line__display_sol
msgid "Display Sol"
msgstr "Zobrazit Sol"

#. module: helpdesk_sale_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.tickets_followup_timesheet
msgid "Employee"
msgstr "Zaměstnanec"

#. module: helpdesk_sale_timesheet
#: model_terms:sale.order,website_description:helpdesk_sale_timesheet.sale_order_helpdesk_sale_timesheet_1
msgid ""
"Great quotation templates will significantly\n"
"                                <strong>boost your success rate</strong>. The\n"
"                                first section is usually about your company,\n"
"                                your references, your methodology or\n"
"                                guarantees, your team, SLA, terms and conditions, etc."
msgstr ""
"Skvělé šablony nabídek budou výrazně\n"
"                                <strong>zvyšovat vaši úspěšnost</strong>. \n"
"První část je obvykle o vaší společnosti,\n"
"vaše reference, vaše metodologie nebo\n"
"záruky, váš tým, SLA, podmínky atd."

#. module: helpdesk_sale_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.product_template_form_view_invoice_policy_inherit_helpdesk
msgid "Helpdesk"
msgstr "Helpdesk"

#. module: helpdesk_sale_timesheet
#: model:ir.model,name:helpdesk_sale_timesheet.model_helpdesk_sla
msgid "Helpdesk SLA Policies"
msgstr "Servisní zásady podpory"

#. module: helpdesk_sale_timesheet
#: model:ir.model,name:helpdesk_sale_timesheet.model_helpdesk_team
msgid "Helpdesk Team"
msgstr "Tým Helpdesku"

#. module: helpdesk_sale_timesheet
#: model:ir.model,name:helpdesk_sale_timesheet.model_helpdesk_ticket
msgid "Helpdesk Ticket"
msgstr "Helpdesk požadavek"

#. module: helpdesk_sale_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.portal_helpdesk_ticket_timesheet
msgid "Hours"
msgstr "Hodiny"

#. module: helpdesk_sale_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.tickets_followup_timesheet
msgid "Hours Spent"
msgstr "Strávených hodin"

#. module: helpdesk_sale_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.tickets_followup_timesheet
msgid "Hours:"
msgstr "Hodin:"

#. module: helpdesk_sale_timesheet
#: model_terms:sale.order,website_description:helpdesk_sale_timesheet.sale_order_helpdesk_sale_timesheet_1
msgid ""
"If you edit a quotation from the 'Preview' of a quotation, you will\n"
"                        update that quotation only. If you edit the quotation\n"
"                        template (from the Configuration menu), all future quotations will\n"
"                        use this modified template."
msgstr ""

#. module: helpdesk_sale_timesheet
#: model_terms:sale.order,website_description:helpdesk_sale_timesheet.sale_order_helpdesk_sale_timesheet_1
msgid "Our Offer"
msgstr "Naše nabídka"

#. module: helpdesk_sale_timesheet
#: model_terms:sale.order,website_description:helpdesk_sale_timesheet.sale_order_helpdesk_sale_timesheet_1
msgid "Our Quality"
msgstr "Naše kvalita"

#. module: helpdesk_sale_timesheet
#: model_terms:sale.order,website_description:helpdesk_sale_timesheet.sale_order_helpdesk_sale_timesheet_1
msgid "Our Service"
msgstr "Naše služby"

#. module: helpdesk_sale_timesheet
#: model_terms:sale.order,website_description:helpdesk_sale_timesheet.sale_order_helpdesk_sale_timesheet_1
msgid "Price"
msgstr "Cena"

#. module: helpdesk_sale_timesheet
#: model:ir.model,name:helpdesk_sale_timesheet.model_product_template
msgid "Product"
msgstr "Produkt"

#. module: helpdesk_sale_timesheet
#: model_terms:sale.order,website_description:helpdesk_sale_timesheet.sale_order_helpdesk_sale_timesheet_1
msgid ""
"Product quality is the foundation we\n"
"                                stand on; we build it with a relentless\n"
"                                focus on fabric, performance and craftsmanship."
msgstr ""

#. module: helpdesk_sale_timesheet
#: model:ir.model.fields,field_description:helpdesk_sale_timesheet.field_helpdesk_team__project_id
msgid "Project"
msgstr "Projekt"

#. module: helpdesk_sale_timesheet
#: model:ir.model.fields,help:helpdesk_sale_timesheet.field_helpdesk_team__project_id
msgid ""
"Project to which the timesheets of this helpdesk team's tickets will be "
"linked."
msgstr ""
"Projekt, ke kterému budou propojeny časové výkazy tiketů tohoto týmu "
"helpdesku."

#. module: helpdesk_sale_timesheet
#: model:ir.model.fields,field_description:helpdesk_sale_timesheet.field_helpdesk_ticket__project_sale_order_id
msgid "Project's sale order"
msgstr "Projektová prodejní objednávka"

#. module: helpdesk_sale_timesheet
#: model:ir.model.fields,field_description:helpdesk_sale_timesheet.field_helpdesk_ticket__sale_order_id
msgid "Ref. Sales Order"
msgstr "Čj. Zakázka odběratele"

#. module: helpdesk_sale_timesheet
#: model:ir.model.fields,field_description:helpdesk_sale_timesheet.field_helpdesk_ticket__use_helpdesk_sale_timesheet
msgid "Reinvoicing Timesheet activated on Team"
msgstr "Týmový tým aktivoval časový rozvrh fakturace"

#. module: helpdesk_sale_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_sale_timesheet
msgid "Remaining Days on SO"
msgstr "Zbývající dny na SO"

#. module: helpdesk_sale_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.tickets_followup_timesheet
msgid "Remaining Days on SO:"
msgstr "Zbývající dny na SO:"

#. module: helpdesk_sale_timesheet
#: model:ir.model.fields,field_description:helpdesk_sale_timesheet.field_helpdesk_ticket__remaining_hours_available
msgid "Remaining Hours Available"
msgstr "Zbývající hodiny k dispozici"

#. module: helpdesk_sale_timesheet
#: model:ir.model.fields,field_description:helpdesk_sale_timesheet.field_helpdesk_ticket__remaining_hours_so
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_sale_timesheet
msgid "Remaining Hours on SO"
msgstr "Zbývající hodiny na SO"

#. module: helpdesk_sale_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.tickets_followup_timesheet
msgid "Remaining Hours on SO:"
msgstr "Zbývající hodiny na SO:"

#. module: helpdesk_sale_timesheet
#: model:ir.model.fields,field_description:helpdesk_sale_timesheet.field_product_product__sla_id
#: model:ir.model.fields,field_description:helpdesk_sale_timesheet.field_product_template__sla_id
msgid "SLA Policy"
msgstr "Zásady SLA"

#. module: helpdesk_sale_timesheet
#: model:ir.model.fields,help:helpdesk_sale_timesheet.field_product_product__sla_id
#: model:ir.model.fields,help:helpdesk_sale_timesheet.field_product_template__sla_id
msgid ""
"SLA Policy that will automatically apply to the tickets linked to a sales "
"order item containing this service."
msgstr ""
"Zásady SLA, které se automaticky použijí na tikety spojené s položkou "
"prodejní objednávky obsahující tuto službu."

#. module: helpdesk_sale_timesheet
#. odoo-python
#: code:addons/helpdesk_sale_timesheet/controllers/portal.py:0
#: code:addons/helpdesk_sale_timesheet/models/helpdesk.py:0
#: model:ir.model,name:helpdesk_sale_timesheet.model_sale_order
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_sale_timesheet
#, python-format
msgid "Sales Order"
msgstr "Prodejní objednávka"

#. module: helpdesk_sale_timesheet
#: model:ir.model.fields,field_description:helpdesk_sale_timesheet.field_helpdesk_ticket__sale_line_id
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.helpdesk_ticket_view_search_inherit_helpdesk_sale_timesheet
msgid "Sales Order Item"
msgstr "Položka objednávky"

#. module: helpdesk_sale_timesheet
#: model:ir.model.fields,help:helpdesk_sale_timesheet.field_helpdesk_ticket__sale_line_id
msgid ""
"Sales Order Item to which the time spent on this ticket will be added in order to be invoiced to your customer.\n"
"By default the last prepaid sales order item that has time remaining will be selected.\n"
"Remove the sales order item in order to make this ticket non-billable.\n"
"You can also change or remove the sales order item of each timesheet entry individually."
msgstr ""
"Položka prodejní objednávky, ke které se přidá čas strávený na tomto tiketu, aby byla fakturována vašemu zákazníkovi.\n"
"Jako výchozí bude vybrána poslední položka předplacené prodejní objednávky, která má zbývající čas.\n"
"Odeberte položku prodejní objednávky, aby tento tiket nebyl zúčtovatelný.\n"
"Můžete také změnit nebo odebrat položku prodejní objednávky každému záznamu časového výkazu zvlášť."

#. module: helpdesk_sale_timesheet
#: model:ir.model.fields,field_description:helpdesk_sale_timesheet.field_helpdesk_sla__sale_line_ids
msgid "Sales Order Items"
msgstr "Položky prodejní objednávky"

#. module: helpdesk_sale_timesheet
#: model:ir.model.fields,help:helpdesk_sale_timesheet.field_helpdesk_ticket__project_sale_order_id
msgid "Sales order to which the project is linked."
msgstr "Zakázka odběratele, ke které je projekt propojen."

#. module: helpdesk_sale_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.portal_helpdesk_ticket_timesheet
msgid "Spent"
msgstr "Utraceno"

#. module: helpdesk_sale_timesheet
#: model_terms:sale.order,website_description:helpdesk_sale_timesheet.sale_order_helpdesk_sale_timesheet_1
msgid ""
"This is a <strong>sample quotation template</strong>. You should\n"
"                                customize it to fit your own needs from the <i>Sales</i>\n"
"                                application, using the menu: Configuration /\n"
"                                Quotation Templates."
msgstr ""
"Tohle je <strong>ukázková šablona nabídky</strong>. Měli byste\n"
"                                jej přizpůsobit tak, aby vyhovovali vašim vlastním <i>prodejním</i>\n"
"                                aplikacím pomocí nabídky: Konfigurace /\n"
"                                Šablony nabídek."

#. module: helpdesk_sale_timesheet
#: model:ir.model.fields,field_description:helpdesk_sale_timesheet.field_sale_order__ticket_count
msgid "Ticket Count"
msgstr "Počet požadavků "

#. module: helpdesk_sale_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.sale_order_form_inherit_helpdesk_sale
msgid "Tickets"
msgstr "Požadavky"

#. module: helpdesk_sale_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.helpdesk_ticket_view_search_inherit_helpdesk_sale_timesheet
msgid "Tickets in Overtime"
msgstr "Tikety v prodloužení"

#. module: helpdesk_sale_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.tickets_followup_timesheet
msgid "Timesheets"
msgstr "Pracovní výkazy"

#. module: helpdesk_sale_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.tickets_followup_timesheet
msgid "Total"
msgstr "Celkem"

#. module: helpdesk_sale_timesheet
#: model_terms:sale.order,website_description:helpdesk_sale_timesheet.sale_order_helpdesk_sale_timesheet_1
msgid ""
"We always ensure that our products are\n"
"                                set at a fair price so that you will be\n"
"                                happy to buy them."
msgstr ""

#. module: helpdesk_sale_timesheet
#: model_terms:sale.order,website_description:helpdesk_sale_timesheet.sale_order_helpdesk_sale_timesheet_1
msgid ""
"You can <strong>set a description per product</strong>. Odoo will\n"
"                        automatically create a quotation using the descriptions\n"
"                        of all products in the proposal. The table of content\n"
"                        on the left is generated automatically using the styles you\n"
"                        used in your description (heading 1, heading 2, ...)"
msgstr ""
