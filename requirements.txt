# Python 3.10 required
######################## Base ############################
# Based on https://github.com/camptocamp/docker-odoo-project/blob/master/15.0/base_requirements.txt
# Odoo dependencies
Babel==2.11.0
chardet==5.0.0
decorator==5.1.1
docutils==0.19
ebaysdk==2.2.0
freezegun==1.2.2
gevent==22.10.2
greenlet==2.0.1
zope.event==4.6
zope.interface==5.5.2
html2text==2020.1.16
idna==3.4
Jinja2==3.1.2
libsass==0.21.0
lxml==4.9.1
Mako==1.2.3
MarkupSafe==2.1.1
num2words==0.5.12
ofxparse==0.21
passlib==1.7.4
Pillow==9.3.0
polib==1.1.1
psutil==5.9.4
psycopg2==2.9.5
pydot==1.4.2
python-ldap==3.4.3
PyPDF2==1.27.12
pyserial==3.5
python-dateutil==2.8.2
pytz==2022.6
pyusb==1.2.1
qrcode==7.3.1
reportlab==3.6.12
requests==2.28.1
zeep==4.2.0
python-stdnum==1.17
vobject==0.9.6.1
Werkzeug==0.16.1
XlsxWriter==3.0.3
xlwt==1.3.0
xlrd==2.0.1
pyOpenSSL==22.1.0

setuptools<58

# Not part of official requirements, but used by some addons
# colorama==0.3.9
gdata==2.0.18
html5lib==1.1
odfpy==1.4.1
pyinotify==0.9.6
simplejson==3.17.6
urllib3==1.26.11
odoo_test_helper

######################## Extra ############################
# Based on https://github.com/camptocamp/docker-odoo-project/blob/master/15.0/extra_requirements.txt
# Extra python dependencies
Adyen==7.1.1
cachetools==5.2.0
cerberus==1.3.4
boto3==1.26.16
factur-x==2.3
invoice2data==0.3.6
mailjet-rest==1.3.4
openupgradelib==3.4.0
paramiko==2.12.0
parse-accept-language==0.1.2
paypalrestsdk==1.13.1
phonenumbers==8.13.0
pyquerystring==1.1
pyOpenSSL==22.1.0
pyquerystring==1.1
pysimplesoap==1.16.2
requests-mock==1.10.0
slugify==0.0.1
stripe==5.0.0
unidecode==1.3.6
vcrpy==4.2.1

# Base Library dependency
argh==0.26.2
atomicwrites==1.4.1
attrs==23.2.0
beautifulsoup4==4.12.3
future==0.18.2
mccabe==0.7.0
more-itertools==9.0.0
pathtools==0.1.2
pbr==5.11.0
pexpect==4.8.0
ptyprocess==0.7.0
py==1.11.0
pycodestyle==2.10.0
pyflakes==3.0.1
unicodecsv==0.14.1
wrapt==1.16.0
openpyxl==3.1.4
# Extra Library dependency
asn1crypto==1.5.1
bcrypt==4.0.1
botocore==1.29.16
cffi==1.15.1
cryptography==38.0.3
dateparser==1.1.4
idna==3.4
jmespath==1.0.1
multidict==6.0.2
pdfminer.six==20221105
pyasn1==0.4.8
pycparser==2.21
pycryptodome==3.15.0
PyNaCl==1.5.0
pytesseract==0.3.10
regex==2022.10.31
s3transfer==0.6.0
tzlocal==4.2
Unidecode==1.3.6
yarl==1.8.1
redis==6.2.0 # For camptocamp/session_redis. Original version requirement was 2.10.4

######################## For Butopêa's addons ############################
click-odoo-contrib
sendgrid>=6.9.7 # Sendgrid module
graphene==3.4.3 # Alokai GraphQL module
graphql-server==3.0.0b7  # Alokai GraphQL module
graphql-core==3.2.6  # Alokai GraphQL module
graphql-relay===3.2.0  # Alokai GraphQL module
google-api-python-client>=2.60.0
google-auth-httplib2>=0.1.0
google-auth-oauthlib>=0.5.2
sentry-sdk==1.9.0 # Sentry module
xmltodict==0.13.0
html-slacker==0.1.6 # odoo_slack_connector
slack-sdk==3.35.0 # odoo_slack_connector
python-jose==3.4.0, # For OCA auth_oidc
pysaml2>=7.5.2, # For OCA auth_saml
typing-extensions>=4.0.0, # For OCA connector_search_engine
orjson>=3.10.18, # For OCA connector_search_engine
typesense==1.0.3, # For OCA connector_typesense

######################## For Anonymization ############################
faker