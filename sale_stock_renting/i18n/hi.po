# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_stock_renting
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON> <<EMAIL>>, 2025
# <PERSON><PERSON><PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-22 18:43+0000\n"
"PO-Revision-Date: 2022-09-22 05:48+0000\n"
"Last-Translator: Uj<PERSON><PERSON> Pathak, 2025\n"
"Language-Team: Hindi (https://app.transifex.com/odoo/teams/41243/hi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: sale_stock_renting
#. odoo-python
#: code:addons/sale_stock_renting/models/product_product.py:0
#, python-format
msgid "%(product)s (%(qty)s item)"
msgstr ""

#. module: sale_stock_renting
#. odoo-python
#: code:addons/sale_stock_renting/models/product_product.py:0
#, python-format
msgid "%(product)s (%(qty)s items)"
msgstr ""

#. module: sale_stock_renting
#: model_terms:ir.ui.view,arch_db:sale_stock_renting.rental_order_wizard_view_form
msgid ""
"<i class=\"fa fa-warning\"/>\n"
"                        <span>\n"
"                            Some products don't have the requested qty available for pickup\n"
"                        </span>"
msgstr ""

#. module: sale_stock_renting
#: model_terms:ir.ui.view,arch_db:sale_stock_renting.res_config_settings_inherit_view_form
msgid "<span class=\"o_form_label\">Default Padding Time</span>"
msgstr ""

#. module: sale_stock_renting
#: model_terms:sale.order,website_description:sale_stock_renting.rental_stock_order_1
msgid "About us"
msgstr ""

#. module: sale_stock_renting
#: model:ir.model.fields,help:sale_stock_renting.field_res_company__padding_time
#: model:ir.model.fields,help:sale_stock_renting.field_res_config_settings__padding_time
msgid ""
"Amount of time (in hours) during which a product is considered unavailable "
"prior to renting (preparation time)."
msgstr ""

#. module: sale_stock_renting
#: model_terms:sale.order,website_description:sale_stock_renting.rental_stock_order_1
msgid ""
"As a leading professional services firm,\n"
"                                we know that success is all about the\n"
"                                commitment we put on strong services."
msgstr ""

#. module: sale_stock_renting
#. odoo-javascript
#: code:addons/sale_stock_renting/static/src/widgets/qty_at_date_widget.xml:0
#, python-format
msgid "Availability"
msgstr "उपलब्धता"

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_rental_order_wizard_line__qty_available
msgid "Available"
msgstr ""

#. module: sale_stock_renting
#. odoo-javascript
#: code:addons/sale_stock_renting/static/src/widgets/qty_at_date_widget.xml:0
#, python-format
msgid "Available for Rent"
msgstr ""

#. module: sale_stock_renting
#: model_terms:ir.ui.view,arch_db:sale_stock_renting.rental_configurator_view_form
msgid "Available)"
msgstr ""

#. module: sale_stock_renting
#: model:ir.model,name:sale_stock_renting.model_res_company
msgid "Companies"
msgstr "कंपनियां"

#. module: sale_stock_renting
#: model:ir.model,name:sale_stock_renting.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: sale_stock_renting
#: model:ir.model,name:sale_stock_renting.model_rental_wizard
msgid "Configure the rental of a product"
msgstr ""

#. module: sale_stock_renting
#: model:ir.model.fields,help:sale_stock_renting.field_rental_order_wizard_line__tracking
#: model:ir.model.fields,help:sale_stock_renting.field_rental_wizard__tracking
#: model:ir.model.fields,help:sale_stock_renting.field_sale_order_line__tracking
msgid "Ensure the traceability of a storable product in your warehouse."
msgstr ""

#. module: sale_stock_renting
#: model_terms:sale.order,website_description:sale_stock_renting.rental_stock_order_1
msgid ""
"Great quotation templates will significantly\n"
"                                <strong>boost your success rate</strong>. The\n"
"                                first section is usually about your company,\n"
"                                your references, your methodology or\n"
"                                guarantees, your team, SLA, terms and conditions, etc."
msgstr ""

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_rental_order_wizard__has_lines_missing_stock
msgid "Has lines whose products have insufficient stock"
msgstr ""

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_rental_order_wizard__has_tracked_lines
msgid "Has lines with tracked products"
msgstr ""

#. module: sale_stock_renting
#: model_terms:sale.order,website_description:sale_stock_renting.rental_stock_order_1
msgid ""
"If you edit a quotation from the 'Preview' of a quotation, you will\n"
"                        update that quotation only. If you edit the quotation\n"
"                        template (from the Configuration menu), all future quotations will\n"
"                        use this modified template."
msgstr ""

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_res_company__rental_loc_id
msgid "In rent"
msgstr ""

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_sale_rental_schedule__is_available
msgid "Is Available"
msgstr ""

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_rental_order_wizard_line__is_product_storable
#: model:ir.model.fields,field_description:sale_stock_renting.field_rental_wizard__is_product_storable
msgid "Is Product Storable"
msgstr ""

#. module: sale_stock_renting
#: model:ir.model,name:sale_stock_renting.model_account_move
msgid "Journal Entry"
msgstr "जर्नल एंट्री"

#. module: sale_stock_renting
#: model:ir.model,name:sale_stock_renting.model_stock_lot
msgid "Lot/Serial"
msgstr ""

#. module: sale_stock_renting
#: model_terms:ir.ui.view,arch_db:sale_stock_renting.res_config_settings_inherit_view_form
msgid "Minimum amount of time between two rentals"
msgstr ""

#. module: sale_stock_renting
#. odoo-python
#: code:addons/sale_stock_renting/models/sale_order_line.py:0
#, python-format
msgid "No valid quant has been found in location %s for serial number %s !"
msgstr ""

#. module: sale_stock_renting
#: model:ir.model.fields,help:sale_stock_renting.field_rental_wizard__lot_ids
msgid "Only available serial numbers are suggested."
msgstr ""

#. module: sale_stock_renting
#: model_terms:sale.order,website_description:sale_stock_renting.rental_stock_order_1
msgid "Our Offer"
msgstr ""

#. module: sale_stock_renting
#: model_terms:sale.order,website_description:sale_stock_renting.rental_stock_order_1
msgid "Our Quality"
msgstr ""

#. module: sale_stock_renting
#: model_terms:sale.order,website_description:sale_stock_renting.rental_stock_order_1
msgid "Our Service"
msgstr ""

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_res_config_settings__padding_time
msgid "Padding"
msgstr ""

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_res_company__padding_time
msgid "Padding Time"
msgstr ""

#. module: sale_stock_renting
#: model:ir.model,name:sale_stock_renting.model_rental_order_wizard
msgid "Pick-up/Return products"
msgstr ""

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_rental_order_wizard_line__pickeable_lot_ids
msgid "Pickeable Lot"
msgstr ""

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_rental_order_wizard_line__pickedup_lot_ids
#: model:ir.model.fields,field_description:sale_stock_renting.field_sale_order_line__pickedup_lot_ids
msgid "Pickedup Lot"
msgstr ""

#. module: sale_stock_renting
#. odoo-python
#: code:addons/sale_stock_renting/wizard/rental_processing.py:0
#, python-format
msgid "Please specify the serial numbers picked up for the tracked products."
msgstr ""

#. module: sale_stock_renting
#. odoo-python
#: code:addons/sale_stock_renting/wizard/rental_processing.py:0
#, python-format
msgid "Please specify the serial numbers returned for the tracked products."
msgstr ""

#. module: sale_stock_renting
#: model_terms:sale.order,website_description:sale_stock_renting.rental_stock_order_1
msgid "Price"
msgstr ""

#. module: sale_stock_renting
#: model:product.template,name:sale_stock_renting.rental_stock_product_1_product_template
msgid "Printer"
msgstr ""

#. module: sale_stock_renting
#: model:ir.model,name:sale_stock_renting.model_product_template
msgid "Product"
msgstr "उत्पाद"

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_rental_wizard__product_uom_id
msgid "Product UoM"
msgstr ""

#. module: sale_stock_renting
#: model:ir.model,name:sale_stock_renting.model_product_product
msgid "Product Variant"
msgstr "उत्पाद प्रकार"

#. module: sale_stock_renting
#: model_terms:sale.order,website_description:sale_stock_renting.rental_stock_order_1
msgid ""
"Product quality is the foundation we\n"
"                                stand on; we build it with a relentless\n"
"                                focus on fabric, performance and craftsmanship."
msgstr ""

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_rental_wizard__qty_available_during_period
msgid "Quantity available for given period (Stock - In Rent)"
msgstr ""

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_rental_wizard__rentable_qty
msgid "Quantity available in stock for given period"
msgstr ""

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_rental_wizard__rented_qty_during_period
msgid "Quantity reserved"
msgstr ""

#. module: sale_stock_renting
#: model:ir.model.fields,help:sale_stock_renting.field_rental_wizard__rented_qty_during_period
msgid "Quantity reserved by other Rental lines during the given period"
msgstr ""

#. module: sale_stock_renting
#. odoo-python
#: code:addons/sale_stock_renting/models/res_company.py:0
#, python-format
msgid "Rental"
msgstr ""

#. module: sale_stock_renting
#: model:ir.model,name:sale_stock_renting.model_sale_rental_report
msgid "Rental Analysis Report"
msgstr ""

#. module: sale_stock_renting
#: model:ir.model,name:sale_stock_renting.model_sale_rental_schedule
msgid "Rental Schedule"
msgstr ""

#. module: sale_stock_renting
#. odoo-python
#: code:addons/sale_stock_renting/models/sale_order_line.py:0
#: code:addons/sale_stock_renting/models/sale_order_line.py:0
#, python-format
msgid "Rental move :"
msgstr ""

#. module: sale_stock_renting
#: model:ir.model,name:sale_stock_renting.model_rental_order_wizard_line
msgid "RentalOrderLine transient representation"
msgstr ""

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_sale_order_line__reserved_lot_ids
msgid "Reserved Lot"
msgstr ""

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_rental_order_wizard_line__returnable_lot_ids
msgid "Returnable Lot"
msgstr ""

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_rental_order_wizard_line__returned_lot_ids
#: model:ir.model.fields,field_description:sale_stock_renting.field_sale_order_line__returned_lot_ids
msgid "Returned Lot"
msgstr ""

#. module: sale_stock_renting
#: model:ir.model,name:sale_stock_renting.model_sale_order_line
msgid "Sales Order Line"
msgstr "बिक्री आदेश पंक्ति"

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_product_product__preparation_time
#: model:ir.model.fields,field_description:sale_stock_renting.field_product_template__preparation_time
msgid "Security Time"
msgstr ""

#. module: sale_stock_renting
#: model_terms:ir.ui.view,arch_db:sale_stock_renting.rental_configurator_view_form
msgid "Select available serial numbers."
msgstr ""

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_sale_rental_report__lot_id
#: model:ir.model.fields,field_description:sale_stock_renting.field_sale_rental_schedule__lot_id
#: model_terms:ir.ui.view,arch_db:sale_stock_renting.rental_report_search_view_inherit_lots
#: model_terms:ir.ui.view,arch_db:sale_stock_renting.rental_schedule_view_search_inherit_lots
msgid "Serial Number"
msgstr ""

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_rental_wizard__lot_ids
#: model_terms:ir.ui.view,arch_db:sale_stock_renting.rental_order_wizard_view_form
#: model_terms:ir.ui.view,arch_db:sale_stock_renting.report_rental_order_document
msgid "Serial Numbers"
msgstr ""

#. module: sale_stock_renting
#: model_terms:ir.ui.view,arch_db:sale_stock_renting.rental_configurator_view_form
msgid "Serials Tracking"
msgstr ""

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_rental_wizard__rentable_lot_ids
msgid "Serials available in Stock"
msgstr ""

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_rental_wizard__rented_lot_ids
msgid "Serials in rent for given period"
msgstr ""

#. module: sale_stock_renting
#: model_terms:ir.ui.view,arch_db:sale_stock_renting.rental_configurator_view_form
msgid "Stock"
msgstr ""

#. module: sale_stock_renting
#: model:ir.model.fields,help:sale_stock_renting.field_product_product__preparation_time
#: model:ir.model.fields,help:sale_stock_renting.field_product_template__preparation_time
msgid "Temporarily make this product unavailable before pickup."
msgstr ""

#. module: sale_stock_renting
#: model_terms:ir.ui.view,arch_db:sale_stock_renting.rental_schedule_view_gantt_inherit_stock
msgid "The product is not available during this period."
msgstr ""

#. module: sale_stock_renting
#: model_terms:sale.order,website_description:sale_stock_renting.rental_stock_order_1
msgid ""
"This is a <strong>sample quotation template</strong>. You should\n"
"                                customize it to fit your own needs from the <i>Sales</i>\n"
"                                application, using the menu: Configuration /\n"
"                                Quotation Templates."
msgstr ""

#. module: sale_stock_renting
#: model:ir.model.fields,help:sale_stock_renting.field_res_company__rental_loc_id
msgid ""
"This technical location serves as stock for products currently in rentalThis"
" location is internal because products in rentalare still considered as "
"company assets."
msgstr ""

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_rental_order_wizard_line__tracking
#: model:ir.model.fields,field_description:sale_stock_renting.field_rental_wizard__tracking
#: model:ir.model.fields,field_description:sale_stock_renting.field_sale_order_line__tracking
msgid "Tracking"
msgstr ""

#. module: sale_stock_renting
#. odoo-python
#: code:addons/sale_stock_renting/models/product_template.py:0
#, python-format
msgid ""
"Tracking by lots isn't supported for rental products.\n"
"You should rather change the tracking mode to unique serial numbers."
msgstr ""

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_sale_order_line__unavailable_lot_ids
msgid "Unavailable Lot"
msgstr ""

#. module: sale_stock_renting
#. odoo-javascript
#: code:addons/sale_stock_renting/static/src/widgets/qty_at_date_widget.xml:0
#, python-format
msgid "View Rentals"
msgstr ""

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_rental_wizard__warehouse_id
#: model:ir.model.fields,field_description:sale_stock_renting.field_sale_rental_schedule__warehouse_id
msgid "Warehouse"
msgstr ""

#. module: sale_stock_renting
#: model_terms:sale.order,website_description:sale_stock_renting.rental_stock_order_1
msgid ""
"We always ensure that our products are\n"
"                                set at a fair price so that you will be\n"
"                                happy to buy them."
msgstr ""

#. module: sale_stock_renting
#: model_terms:sale.order,website_description:sale_stock_renting.rental_stock_order_1
msgid ""
"You can <strong>set a description per product</strong>. Odoo will\n"
"                        automatically create a quotation using the descriptions\n"
"                        of all products in the proposal. The table of content\n"
"                        on the left is generated automatically using the styles you\n"
"                        used in your description (heading 1, heading 2, ...)"
msgstr ""

#. module: sale_stock_renting
#. odoo-python
#: code:addons/sale_stock_renting/wizard/rental_configurator.py:0
#, python-format
msgid "You cannot change the product of a picked-up line."
msgstr ""

#. module: sale_stock_renting
#. odoo-python
#: code:addons/sale_stock_renting/models/sale_order_line.py:0
#, python-format
msgid "You cannot change the product of lines linked to stock moves."
msgstr ""

#. module: sale_stock_renting
#: model_terms:ir.ui.view,arch_db:sale_stock_renting.res_config_settings_inherit_view_form
msgid "hours"
msgstr ""

#. module: sale_stock_renting
#: model_terms:ir.ui.view,arch_db:sale_stock_renting.product_template_inherit_view_form_stock_rental
msgid "hours before orders"
msgstr ""

#. module: sale_stock_renting
#. odoo-javascript
#: code:addons/sale_stock_renting/static/src/widgets/qty_at_date_widget.xml:0
#, python-format
msgid "to"
msgstr ""
