# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_be_intrastat
# 
# Translators:
# <PERSON>, 2023
# <PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-12-28 11:18+0000\n"
"PO-Revision-Date: 2023-02-03 07:08+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: l10n_be_intrastat
#: model_terms:ir.ui.view,arch_db:l10n_be_intrastat.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" role=\"img\" aria-label=\"Values set here are company-"
"specific.\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"De hier ingestelde waarden "
"zijn bedrijfsspecifiek.\" role=\"img\" aria-label=\"De hier ingestelde "
"waarden zijn bedrijfsspecifiek.\"/>"

#. module: l10n_be_intrastat
#. odoo-python
#: code:addons/l10n_be_intrastat/models/account_intrastat_report.py:0
#, python-format
msgid "Add company registry"
msgstr "Ondernemingsregister toevoegen"

#. module: l10n_be_intrastat
#: model:ir.model.fields,field_description:l10n_be_intrastat.field_res_config_settings__intrastat_region_id
msgid "Company Intrastat Region"
msgstr "Intrastat regio bedrijf"

#. module: l10n_be_intrastat
#: model:ir.model.fields,field_description:l10n_be_intrastat.field_res_config_settings__company_country_id
msgid "Company country"
msgstr "Land bedrijf"

#. module: l10n_be_intrastat
#: model:ir.model,name:l10n_be_intrastat.model_res_config_settings
msgid "Config Settings"
msgstr "Configuratie instellingen"

#. module: l10n_be_intrastat
#: model:ir.model,name:l10n_be_intrastat.model_account_intrastat_report_handler
msgid "Intrastat Report Custom Handler"
msgstr "Aangepaste handler voor Intrastat-rapportages"

#. module: l10n_be_intrastat
#. odoo-python
#: code:addons/l10n_be_intrastat/models/account_intrastat_report.py:0
#, python-format
msgid "Missing company registry information on the company"
msgstr "Ontbrekende informatie over het ondernemingsregister over het bedrijf"

#. module: l10n_be_intrastat
#: model_terms:ir.ui.view,arch_db:l10n_be_intrastat.intrastat_report_export_xml
msgid "NBB"
msgstr "NBB"

#. module: l10n_be_intrastat
#: model_terms:ir.ui.view,arch_db:l10n_be_intrastat.intrastat_report_export_xml
msgid "SXX"
msgstr "SXX"

#. module: l10n_be_intrastat
#: model:ir.model.fields,help:l10n_be_intrastat.field_res_config_settings__company_country_id
msgid "The country to use the tax reports from for this company"
msgstr ""
"Het land waarvan de btw-aangiften voor dit bedrijf moeten worden gebruikt"

#. module: l10n_be_intrastat
#. odoo-python
#: code:addons/l10n_be_intrastat/models/account_intrastat_report.py:0
#, python-format
msgid ""
"Wrong date range selected. The intrastat declaration export has to be done "
"monthly."
msgstr ""
"Verkeerd datumbereik geselecteerd. Het exporteren van de intrastat-aangifte "
"moet maandelijks gebeuren."

#. module: l10n_be_intrastat
#. odoo-python
#: code:addons/l10n_be_intrastat/models/account_intrastat_report.py:0
#: code:addons/l10n_be_intrastat/models/account_intrastat_report.py:0
#, python-format
msgid "XML"
msgstr "XML"

#. module: l10n_be_intrastat
#. odoo-python
#: code:addons/l10n_be_intrastat/models/account_intrastat_report.py:0
#, python-format
msgid "company %s"
msgstr "bedrijf %s"
