# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_be_hr_payroll_dimona
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-02-02 17:09+0000\n"
"PO-Revision-Date: 2023-02-02 17:09+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_be_hr_payroll_dimona
#: model:ir.actions.server,name:l10n_be_hr_payroll_dimona.ir_cron_check_dimona_ir_actions_server
#: model:ir.cron,cron_name:l10n_be_hr_payroll_dimona.ir_cron_check_dimona
msgid "Belgian Payroll: Check DIMONA"
msgstr ""

#. module: l10n_be_hr_payroll_dimona
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll_dimona.l10n_be_dimona_wizard_view_form
msgid "Cancel"
msgstr ""

#. module: l10n_be_hr_payroll_dimona
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll_dimona.selection__l10n_be_dimona_wizard__declaration_type__cancel
msgid "Cancel employee declaration"
msgstr ""

#. module: l10n_be_hr_payroll_dimona
#. odoo-python
#: code:addons/l10n_be_hr_payroll_dimona/models/hr_contract.py:0
#: code:addons/l10n_be_hr_payroll_dimona/models/hr_contract.py:0
#: code:addons/l10n_be_hr_payroll_dimona/models/hr_contract.py:0
#: code:addons/l10n_be_hr_payroll_dimona/models/hr_contract.py:0
#: code:addons/l10n_be_hr_payroll_dimona/models/hr_contract.py:0
#: code:addons/l10n_be_hr_payroll_dimona/models/hr_contract.py:0
#, python-format
msgid ""
"Cannot connect with the ONSS servers. Please contact an administrator. (%s)"
msgstr ""

#. module: l10n_be_hr_payroll_dimona
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll_dimona.hr_contract_view_form
msgid "Check Dimona"
msgstr ""

#. module: l10n_be_hr_payroll_dimona
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_dimona.field_l10n_be_dimona_wizard__contract_id
msgid "Contract"
msgstr ""

#. module: l10n_be_hr_payroll_dimona
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_dimona.field_l10n_be_dimona_wizard__contract_country_code
msgid "Country Code"
msgstr ""

#. module: l10n_be_hr_payroll_dimona
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_dimona.field_l10n_be_dimona_wizard__create_uid
msgid "Created by"
msgstr ""

#. module: l10n_be_hr_payroll_dimona
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_dimona.field_l10n_be_dimona_wizard__create_date
msgid "Created on"
msgstr ""

#. module: l10n_be_hr_payroll_dimona
#: model:ir.actions.act_window,name:l10n_be_hr_payroll_dimona.l10n_be_dimona_wizard_action
#: model:ir.ui.menu,name:l10n_be_hr_payroll_dimona.menu_l10n_be_dimona_wizard
msgid "DIMONA"
msgstr ""

#. module: l10n_be_hr_payroll_dimona
#. odoo-python
#: code:addons/l10n_be_hr_payroll_dimona/models/hr_contract.py:0
#, python-format
msgid "DIMONA Cancel declaration posted successfully, waiting validation"
msgstr ""

#. module: l10n_be_hr_payroll_dimona
#. odoo-python
#: code:addons/l10n_be_hr_payroll_dimona/models/hr_contract.py:0
#, python-format
msgid "DIMONA IN declaration posted successfully, waiting validation"
msgstr ""

#. module: l10n_be_hr_payroll_dimona
#. odoo-python
#: code:addons/l10n_be_hr_payroll_dimona/models/hr_contract.py:0
#, python-format
msgid "DIMONA Out declaration posted successfully, waiting validation"
msgstr ""

#. module: l10n_be_hr_payroll_dimona
#. odoo-python
#: code:addons/l10n_be_hr_payroll_dimona/models/hr_contract.py:0
#, python-format
msgid "DIMONA Update declaration posted successfully, waiting validation"
msgstr ""

#. module: l10n_be_hr_payroll_dimona
#. odoo-python
#: code:addons/l10n_be_hr_payroll_dimona/models/hr_contract.py:0
#, python-format
msgid ""
"DIMONA declaration treated and accepted with non blocking anomalies\n"
"%s\n"
"%s"
msgstr ""

#. module: l10n_be_hr_payroll_dimona
#. odoo-python
#: code:addons/l10n_be_hr_payroll_dimona/models/hr_contract.py:0
#, python-format
msgid "DIMONA declaration treated and accepted without anomalies"
msgstr ""

#. module: l10n_be_hr_payroll_dimona
#. odoo-python
#: code:addons/l10n_be_hr_payroll_dimona/models/hr_contract.py:0
#, python-format
msgid ""
"DIMONA declaration treated and refused (blocking anomalies)\n"
"%s"
msgstr ""

#. module: l10n_be_hr_payroll_dimona
#. odoo-python
#: code:addons/l10n_be_hr_payroll_dimona/models/hr_contract.py:0
#, python-format
msgid "DIMONA declaration waiting worker identification by Sigedis"
msgstr ""

#. module: l10n_be_hr_payroll_dimona
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_dimona.field_l10n_be_dimona_wizard__employee_birthday
msgid "Date of Birth"
msgstr ""

#. module: l10n_be_hr_payroll_dimona
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_dimona.field_l10n_be_dimona_wizard__declaration_type
msgid "Declaration Type"
msgstr ""

#. module: l10n_be_hr_payroll_dimona
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll_dimona.selection__hr_contract__l10n_be_dimona_declaration_state__done
msgid "Declared and accepted"
msgstr ""

#. module: l10n_be_hr_payroll_dimona
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll_dimona.selection__hr_contract__l10n_be_dimona_declaration_state__done_warning
msgid "Declared and accepted with warnings"
msgstr ""

#. module: l10n_be_hr_payroll_dimona
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll_dimona.selection__hr_contract__l10n_be_dimona_declaration_state__refused
msgid "Declared and refused"
msgstr ""

#. module: l10n_be_hr_payroll_dimona
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll_dimona.selection__hr_contract__l10n_be_dimona_declaration_state__waiting_sigedis
msgid "Declared and waiting Sigedis"
msgstr ""

#. module: l10n_be_hr_payroll_dimona
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll_dimona.selection__hr_contract__l10n_be_dimona_declaration_state__waiting
msgid "Declared and waiting status"
msgstr ""

#. module: l10n_be_hr_payroll_dimona
#: model:ir.model,name:l10n_be_hr_payroll_dimona.model_l10n_be_dimona_wizard
msgid "Dimona Wizard"
msgstr ""

#. module: l10n_be_hr_payroll_dimona
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_dimona.field_l10n_be_dimona_wizard__display_name
msgid "Display Name"
msgstr ""

#. module: l10n_be_hr_payroll_dimona
#. odoo-python
#: code:addons/l10n_be_hr_payroll_dimona/models/hr_contract.py:0
#: code:addons/l10n_be_hr_payroll_dimona/models/hr_contract.py:0
#: code:addons/l10n_be_hr_payroll_dimona/models/hr_contract.py:0
#: code:addons/l10n_be_hr_payroll_dimona/models/hr_contract.py:0
#: code:addons/l10n_be_hr_payroll_dimona/models/hr_contract.py:0
#, python-format
msgid ""
"Due to a technical problem at the ONSS side, the Dimona declaration could "
"not be received by the ONSS."
msgstr ""

#. module: l10n_be_hr_payroll_dimona
#. odoo-python
#: code:addons/l10n_be_hr_payroll_dimona/models/hr_contract.py:0
#, python-format
msgid ""
"Due to a technical problem at the ONSS side, the authentication could not be"
" done by the ONSS."
msgstr ""

#. module: l10n_be_hr_payroll_dimona
#: model:ir.model,name:l10n_be_hr_payroll_dimona.model_hr_employee
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_dimona.field_l10n_be_dimona_wizard__employee_id
msgid "Employee"
msgstr ""

#. module: l10n_be_hr_payroll_dimona
#: model:ir.model,name:l10n_be_hr_payroll_dimona.model_hr_contract
msgid "Employee Contract"
msgstr ""

#. module: l10n_be_hr_payroll_dimona
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_dimona.field_l10n_be_dimona_wizard__without_niss
msgid "Employee Without NISS"
msgstr ""

#. module: l10n_be_hr_payroll_dimona
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_dimona.field_l10n_be_dimona_wizard__contract_date_end
msgid "End Date"
msgstr ""

#. module: l10n_be_hr_payroll_dimona
#: model:ir.model.fields,help:l10n_be_hr_payroll_dimona.field_l10n_be_dimona_wizard__contract_date_end
msgid "End date of the contract (if it's a fixed-term contract)."
msgstr ""

#. module: l10n_be_hr_payroll_dimona
#. odoo-python
#: code:addons/l10n_be_hr_payroll_dimona/models/hr_contract.py:0
#, python-format
msgid "Error on authentication. Please contact an administrator. (%s)"
msgstr ""

#. module: l10n_be_hr_payroll_dimona
#. odoo-python
#: code:addons/l10n_be_hr_payroll_dimona/models/hr_contract.py:0
#, python-format
msgid ""
"Error with one or several invalid parameters on the POST request during "
"authentication. Please contact an administrator. (%s)"
msgstr ""

#. module: l10n_be_hr_payroll_dimona
#. odoo-python
#: code:addons/l10n_be_hr_payroll_dimona/models/hr_contract.py:0
#: code:addons/l10n_be_hr_payroll_dimona/models/hr_contract.py:0
#: code:addons/l10n_be_hr_payroll_dimona/models/hr_contract.py:0
#: code:addons/l10n_be_hr_payroll_dimona/models/hr_contract.py:0
#: code:addons/l10n_be_hr_payroll_dimona/models/hr_contract.py:0
#, python-format
msgid ""
"Error with one or several invalid parameters on the POST request. Please "
"contact an administrator. (%s)"
msgstr ""

#. module: l10n_be_hr_payroll_dimona
#. odoo-python
#: code:addons/l10n_be_hr_payroll_dimona/models/hr_contract.py:0
#, python-format
msgid ""
"Foreigner employees should provide a complete address (street, number, zip, "
"city, country"
msgstr ""

#. module: l10n_be_hr_payroll_dimona
#. odoo-python
#: code:addons/l10n_be_hr_payroll_dimona/models/hr_contract.py:0
#, python-format
msgid ""
"Foreigner employees should provide their name, birthdate, birth place, birth"
" country, nationality and the gender"
msgstr ""

#. module: l10n_be_hr_payroll_dimona
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_dimona.field_l10n_be_dimona_wizard__id
msgid "ID"
msgstr ""

#. module: l10n_be_hr_payroll_dimona
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll_dimona.selection__hr_contract__l10n_be_dimona_declaration_state__error
msgid "Invalid declaration or restricted access"
msgstr ""

#. module: l10n_be_hr_payroll_dimona
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_dimona.field_hr_contract__l10n_be_dimona_declaration_state
msgid "L10N Be Dimona Declaration State"
msgstr ""

#. module: l10n_be_hr_payroll_dimona
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_dimona.field_hr_contract__l10n_be_dimona_in_declaration_number
msgid "L10N Be Dimona In Declaration Number"
msgstr ""

#. module: l10n_be_hr_payroll_dimona
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_dimona.field_hr_contract__l10n_be_dimona_last_declaration_number
msgid "L10N Be Dimona Last Declaration Number"
msgstr ""

#. module: l10n_be_hr_payroll_dimona
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_dimona.field_hr_contract__l10n_be_is_student
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_dimona.field_l10n_be_dimona_wizard__contract_is_student
msgid "L10N Be Is Student"
msgstr ""

#. module: l10n_be_hr_payroll_dimona
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_dimona.field_l10n_be_dimona_wizard____last_update
msgid "Last Modified on"
msgstr ""

#. module: l10n_be_hr_payroll_dimona
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_dimona.field_l10n_be_dimona_wizard__write_uid
msgid "Last Updated by"
msgstr ""

#. module: l10n_be_hr_payroll_dimona
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_dimona.field_l10n_be_dimona_wizard__write_date
msgid "Last Updated on"
msgstr ""

#. module: l10n_be_hr_payroll_dimona
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll_dimona.l10n_be_dimona_wizard_view_form
msgid "Manage DIMONA"
msgstr ""

#. module: l10n_be_hr_payroll_dimona
#. odoo-python
#: code:addons/l10n_be_hr_payroll_dimona/models/hr_contract.py:0
#, python-format
msgid "No DIMONA declaration is linked to this contract"
msgstr ""

#. module: l10n_be_hr_payroll_dimona
#. odoo-python
#: code:addons/l10n_be_hr_payroll_dimona/models/hr_contract.py:0
#, python-format
msgid "No ONSS registration number is defined for company %s"
msgstr ""

#. module: l10n_be_hr_payroll_dimona
#. odoo-python
#: code:addons/l10n_be_hr_payroll_dimona/models/hr_contract.py:0
#, python-format
msgid "No PEM Certificate / Passphrase defined on the Payroll Configuration"
msgstr ""

#. module: l10n_be_hr_payroll_dimona
#. odoo-python
#: code:addons/l10n_be_hr_payroll_dimona/models/hr_contract.py:0
#, python-format
msgid "No expeditor number defined on the payroll settings."
msgstr ""

#. module: l10n_be_hr_payroll_dimona
#. odoo-python
#: code:addons/l10n_be_hr_payroll_dimona/models/hr_contract.py:0
#, python-format
msgid "No house number found on employee street"
msgstr ""

#. module: l10n_be_hr_payroll_dimona
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll_dimona.selection__hr_contract__l10n_be_dimona_declaration_state__none
msgid "Not Declared"
msgstr ""

#. module: l10n_be_hr_payroll_dimona
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll_dimona.selection__l10n_be_dimona_wizard__declaration_type__out
msgid "Register employee departure"
msgstr ""

#. module: l10n_be_hr_payroll_dimona
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll_dimona.selection__l10n_be_dimona_wizard__declaration_type__in
msgid "Register employee entrance"
msgstr ""

#. module: l10n_be_hr_payroll_dimona
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_dimona.field_l10n_be_dimona_wizard__contract_date_start
msgid "Start Date"
msgstr ""

#. module: l10n_be_hr_payroll_dimona
#. odoo-python
#: code:addons/l10n_be_hr_payroll_dimona/wizard/l10n_be_dimona_wizard.py:0
#, python-format
msgid "Start date and end date should belong to the same quarter."
msgstr ""

#. module: l10n_be_hr_payroll_dimona
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_dimona.field_hr_contract__l10n_be_dimona_planned_hours
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_dimona.field_l10n_be_dimona_wizard__contract_planned_hours
msgid "Student Planned Hours"
msgstr ""

#. module: l10n_be_hr_payroll_dimona
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll_dimona.l10n_be_dimona_wizard_view_form
msgid "Submit Declaration"
msgstr ""

#. module: l10n_be_hr_payroll_dimona
#. odoo-python
#: code:addons/l10n_be_hr_payroll_dimona/wizard/l10n_be_dimona_wizard.py:0
#, python-format
msgid "The DIMONA should be introduced before start date for students."
msgstr ""

#. module: l10n_be_hr_payroll_dimona
#: model:ir.model.fields,help:l10n_be_hr_payroll_dimona.field_l10n_be_dimona_wizard__contract_country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""

#. module: l10n_be_hr_payroll_dimona
#. odoo-python
#: code:addons/l10n_be_hr_payroll_dimona/models/hr_contract.py:0
#, python-format
msgid "The NISS is invalid."
msgstr ""

#. module: l10n_be_hr_payroll_dimona
#. odoo-python
#: code:addons/l10n_be_hr_payroll_dimona/models/hr_contract.py:0
#, python-format
msgid ""
"The declaration has been submitted but not processed yet or the declaration "
"reference is not known. (%s)"
msgstr ""

#. module: l10n_be_hr_payroll_dimona
#. odoo-python
#: code:addons/l10n_be_hr_payroll_dimona/models/hr_contract.py:0
#, python-format
msgid "The employee name is incomplete"
msgstr ""

#. module: l10n_be_hr_payroll_dimona
#. odoo-python
#: code:addons/l10n_be_hr_payroll_dimona/models/hr_contract.py:0
#, python-format
msgid "The employee zip does not exist."
msgstr ""

#. module: l10n_be_hr_payroll_dimona
#. odoo-python
#: code:addons/l10n_be_hr_payroll_dimona/wizard/l10n_be_dimona_wizard.py:0
#, python-format
msgid "There is already a IN declaration for this contract."
msgstr ""

#. module: l10n_be_hr_payroll_dimona
#. odoo-python
#: code:addons/l10n_be_hr_payroll_dimona/wizard/l10n_be_dimona_wizard.py:0
#, python-format
msgid "There is no contract defined on the employee form."
msgstr ""

#. module: l10n_be_hr_payroll_dimona
#. odoo-python
#: code:addons/l10n_be_hr_payroll_dimona/wizard/l10n_be_dimona_wizard.py:0
#, python-format
msgid "There is no defined end date on the student contract."
msgstr ""

#. module: l10n_be_hr_payroll_dimona
#. odoo-python
#: code:addons/l10n_be_hr_payroll_dimona/wizard/l10n_be_dimona_wizard.py:0
#, python-format
msgid "There is no defined planned hours on the student contract."
msgstr ""

#. module: l10n_be_hr_payroll_dimona
#. odoo-python
#: code:addons/l10n_be_hr_payroll_dimona/wizard/l10n_be_dimona_wizard.py:0
#, python-format
msgid "There is not end date defined on the employee contract."
msgstr ""

#. module: l10n_be_hr_payroll_dimona
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll_dimona.selection__l10n_be_dimona_wizard__declaration_type__update
msgid "Update employee information"
msgstr ""

#. module: l10n_be_hr_payroll_dimona
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_dimona.field_l10n_be_dimona_wizard__contract_wage_type
msgid "Wage Type"
msgstr ""

#. module: l10n_be_hr_payroll_dimona
#. odoo-python
#: code:addons/l10n_be_hr_payroll_dimona/models/hr_contract.py:0
#, python-format
msgid "You don't have the right to call this action"
msgstr ""

#. module: l10n_be_hr_payroll_dimona
#. odoo-python
#: code:addons/l10n_be_hr_payroll_dimona/wizard/l10n_be_dimona_wizard.py:0
#, python-format
msgid "You must be logged in a Belgian company to use this feature"
msgstr ""

#. module: l10n_be_hr_payroll_dimona
#. odoo-python
#: code:addons/l10n_be_hr_payroll_dimona/models/hr_contract.py:0
#, python-format
msgid ""
"Your user does not have the rights to consult this declaration. This "
"happens, for example, if the user does not have or no longer has a mandate "
"for the employer. (%s)"
msgstr ""

#. module: l10n_be_hr_payroll_dimona
#. odoo-python
#: code:addons/l10n_be_hr_payroll_dimona/models/hr_contract.py:0
#: code:addons/l10n_be_hr_payroll_dimona/models/hr_contract.py:0
#: code:addons/l10n_be_hr_payroll_dimona/models/hr_contract.py:0
#: code:addons/l10n_be_hr_payroll_dimona/models/hr_contract.py:0
#, python-format
msgid ""
"Your user does not have the rights to make a declaration for the employer. "
"This happens, for example, if the user does not have or no longer has a "
"mandate for the employer. (%s)"
msgstr ""
