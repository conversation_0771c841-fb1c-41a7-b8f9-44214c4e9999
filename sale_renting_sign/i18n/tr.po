# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_renting_sign
# 
# Translators:
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# Nadir <PERSON>azi<PERSON>lu <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0beta+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-04 09:43+0000\n"
"PO-Revision-Date: 2022-09-22 05:48+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Turkish (https://app.transifex.com/odoo/teams/41243/tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: sale_renting_sign
#: model:ir.model.fields,field_description:sale_renting_sign.field_sale_order__sign_request_count
msgid "# of Signature Requests"
msgstr "# İmza İstekleri"

#. module: sale_renting_sign
#: model_terms:ir.ui.view,arch_db:sale_renting_sign.message_signature_link
msgid "A document has been signed and a copy attached to this order:"
msgstr "Bir belge imzalandı ve bu siparişe bir kopyası eklendi:"

#. module: sale_renting_sign
#: model_terms:ir.ui.view,arch_db:sale_renting_sign.rental_sign_wizard_view_form
msgid "Cancel"
msgstr "İptal"

#. module: sale_renting_sign
#: model:ir.model,name:sale_renting_sign.model_res_company
msgid "Companies"
msgstr "Şirketler"

#. module: sale_renting_sign
#: model:ir.model,name:sale_renting_sign.model_res_config_settings
msgid "Config Settings"
msgstr "Yapılandırma Ayarları"

#. module: sale_renting_sign
#: model:ir.model.fields,field_description:sale_renting_sign.field_rental_sign_wizard__create_uid
msgid "Created by"
msgstr "Oluşturan"

#. module: sale_renting_sign
#: model:ir.model.fields,field_description:sale_renting_sign.field_rental_sign_wizard__create_date
msgid "Created on"
msgstr "Oluşturulma"

#. module: sale_renting_sign
#: model:ir.model.fields,field_description:sale_renting_sign.field_res_config_settings__rental_sign_tmpl_id
msgid "Default Document"
msgstr "Varsayılan Belge"

#. module: sale_renting_sign
#: model:ir.model.fields,field_description:sale_renting_sign.field_res_company__rental_sign_tmpl_id
msgid "Default Document Template for Rentals"
msgstr "Kiralama için Varsayılan Belge Şablonu"

#. module: sale_renting_sign
#: model:ir.model.fields,field_description:sale_renting_sign.field_rental_sign_wizard__display_name
msgid "Display Name"
msgstr "Görünüm Adı"

#. module: sale_renting_sign
#: model:ir.model.fields,field_description:sale_renting_sign.field_rental_sign_wizard__template_id
msgid "Document Template"
msgstr "Belge Şablonu"

#. module: sale_renting_sign
#: model_terms:ir.ui.view,arch_db:sale_renting_sign.rental_order_inherit_rental_sign
msgid "Document(s) Signed"
msgstr "İmzalanan Belge(ler)"

#. module: sale_renting_sign
#: model:ir.model.fields,field_description:sale_renting_sign.field_rental_sign_wizard__id
msgid "ID"
msgstr "ID"

#. module: sale_renting_sign
#: model:ir.model.fields,field_description:sale_renting_sign.field_rental_sign_wizard____last_update
msgid "Last Modified on"
msgstr "Son Düzenleme"

#. module: sale_renting_sign
#: model:ir.model.fields,field_description:sale_renting_sign.field_rental_sign_wizard__write_uid
msgid "Last Updated by"
msgstr "Son Güncelleyen"

#. module: sale_renting_sign
#: model:ir.model.fields,field_description:sale_renting_sign.field_rental_sign_wizard__write_date
msgid "Last Updated on"
msgstr "Son Güncelleme"

#. module: sale_renting_sign
#: model:sign.template,redirect_url_text:sale_renting_sign.template_rental_sign
msgid "Open Link"
msgstr "Bağlantıyı Aç"

#. module: sale_renting_sign
#: model:ir.model,name:sale_renting_sign.model_sale_order
#: model:ir.model.fields,field_description:sale_renting_sign.field_rental_sign_wizard__order_id
#: model:ir.model.fields,field_description:sale_renting_sign.field_sign_request__sale_order_id
#: model:ir.model.fields,field_description:sale_renting_sign.field_sign_send_request__sale_order_id
msgid "Sales Order"
msgstr "Satış Siparişi"

#. module: sale_renting_sign
#: model:ir.model.fields,help:sale_renting_sign.field_res_config_settings__rental_sign_tmpl_id
msgid "Set a default document template for all rentals in the current company"
msgstr ""
"Geçerli şirketteki tüm kiralama işlemleri için varsayılan belge şablonu "
"ayarlama"

#. module: sale_renting_sign
#: model_terms:ir.ui.view,arch_db:sale_renting_sign.rental_sign_wizard_view_form
msgid "Sign Document"
msgstr "Belgeyi İmzala"

#. module: sale_renting_sign
#: model:ir.actions.act_window,name:sale_renting_sign.rental_sign_documents
#: model_terms:ir.ui.view,arch_db:sale_renting_sign.rental_order_inherit_rental_sign
#: model_terms:ir.ui.view,arch_db:sale_renting_sign.rental_sign_wizard_view_form
msgid "Sign Documents"
msgstr "Belgeleri imzalama"

#. module: sale_renting_sign
#: model:ir.model,name:sale_renting_sign.model_rental_sign_wizard
msgid "Sign Documents from a SO"
msgstr "SO'dan Evrak İmzalama"

#. module: sale_renting_sign
#: model:ir.model,name:sale_renting_sign.model_sign_send_request
msgid "Sign send request"
msgstr "İşaret gönderme isteği"

#. module: sale_renting_sign
#: model:ir.model,name:sale_renting_sign.model_sign_request
msgid "Signature Request"
msgstr "İmza Talebi"

#. module: sale_renting_sign
#: model:ir.model.fields,field_description:sale_renting_sign.field_sale_order__sign_request_ids
msgid "Signature Requests"
msgstr "İmza İstekleri"

#. module: sale_renting_sign
#: model:ir.model.fields,help:sale_renting_sign.field_res_company__rental_sign_tmpl_id
msgid ""
"This document template will be selected by default when signing documents "
"from a rental order. You should select a template accessible to all Sign "
"users."
msgstr ""
"Bu belge şablonu, bir kiralama siparişinden belge imzalanırken varsayılan "
"olarak seçilecektir. Tüm Sign kullanıcıları tarafından erişilebilen bir "
"şablon seçmelisiniz."

#. module: sale_renting_sign
#: model_terms:ir.ui.view,arch_db:sale_renting_sign.res_config_settings_view_form
msgid "Upload Template"
msgstr "Şablonu Yükle"
