# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_helpdesk_knowledge
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0beta+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-04 09:43+0000\n"
"PO-Revision-Date: 2022-09-22 05:50+0000\n"
"Language-Team: Tamil (https://app.transifex.com/odoo/teams/41243/ta/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ta\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_helpdesk_knowledge
#: model_terms:ir.ui.view,arch_db:website_helpdesk_knowledge.search_result
msgid ", by"
msgstr ""

#. module: website_helpdesk_knowledge
#: model_terms:ir.ui.view,arch_db:website_helpdesk_knowledge.knowledge_base_articles_card
msgid ""
"<i class=\"fa fa-3x rounded bg-secondary m-3 fa-book fa-3x rounded bg-"
"secondary m-3\">​</i>"
msgstr ""

#. module: website_helpdesk_knowledge
#: model_terms:ir.ui.view,arch_db:website_helpdesk_knowledge.search_result
msgid "<i class=\"fa fa-fw fa-2x fa-book\" title=\"Knowledge Article\"/>"
msgstr ""

#. module: website_helpdesk_knowledge
#: model_terms:ir.ui.view,arch_db:website_helpdesk_knowledge.knowledge_base_articles_card
msgid ""
"<span style=\"font-size: 18px;\">Search our documentation for answers<br/>to"
" common questions</span>"
msgstr ""

#. module: website_helpdesk_knowledge
#: model_terms:ir.ui.view,arch_db:website_helpdesk_knowledge.knowledge_base_articles_card
msgid "<span style=\"font-weight: bolder; font-size: 18px;\">Articles</span>"
msgstr ""

#. module: website_helpdesk_knowledge
#: model:ir.model.fields,field_description:website_helpdesk_knowledge.field_helpdesk_team__website_article_id
msgid "Article"
msgstr ""

#. module: website_helpdesk_knowledge
#: model:ir.model.fields,help:website_helpdesk_knowledge.field_helpdesk_team__website_article_id
msgid ""
"Article on which customers will land by default, and to which the search "
"will be restricted."
msgstr ""

#. module: website_helpdesk_knowledge
#: code:addons/website_helpdesk_knowledge/models/helpdesk.py:0
#, python-format
msgid "Articles"
msgstr ""

#. module: website_helpdesk_knowledge
#: model_terms:ir.ui.view,arch_db:website_helpdesk_knowledge.knowledge_base_articles_card
msgid "Browse Articles"
msgstr ""

#. module: website_helpdesk_knowledge
#: model:ir.model,name:website_helpdesk_knowledge.model_helpdesk_team
msgid "Helpdesk Team"
msgstr ""

#. module: website_helpdesk_knowledge
#: model:ir.model,name:website_helpdesk_knowledge.model_knowledge_article
msgid "Knowledge Article"
msgstr ""

#. module: website_helpdesk_knowledge
#: model:ir.model.fields,field_description:website_helpdesk_knowledge.field_helpdesk_team__show_knowledge_base_article
msgid "Show Knowledge Base Article"
msgstr ""

#. module: website_helpdesk_knowledge
#: code:addons/website_helpdesk_knowledge/models/knowledge_article.py:0
#, python-format
msgid ""
"You cannot delete, unpublish or set a parent on an article that is used by a"
" helpdesk team."
msgstr ""
